package com.deepinnet.skyflow.operationcenter.service.client;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.UserClient;
import com.deepinnet.infra.api.dto.UserDetailDTO;
import com.deepinnet.infra.api.dto.UserQueryDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:
 * Date: 2025/5/7
 * Author: lijunheng
 */
@Component
public class FlightUserClient {

    @Resource
    private UserClient userClient;

    public List<UserDetailDTO> getUserDetailList(UserQueryDTO userQueryDTO) {
        Result<List<UserDetailDTO>> userDetailListResult = userClient.getUserDetailList(userQueryDTO);

        if (!userDetailListResult.isSuccess()) {
            LogUtil.error("getUserDetailList error:{}", userDetailListResult.getErrorDesc());
            throw new BizException(userDetailListResult.getErrorCode(), userDetailListResult.getErrorDesc());
        }

        return userDetailListResult.getData();
    }
}
