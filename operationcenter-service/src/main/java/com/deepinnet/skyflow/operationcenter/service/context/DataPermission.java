package com.deepinnet.skyflow.operationcenter.service.context;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Description: 数据权限注解，标注该注解的方法会自动注入用户数据权限
 * Date: 2025/5/22
 * Author: lijunheng
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DataPermission {
    /**
     * 是否启用数据权限
     */
    boolean value() default true;
}
