package com.deepinnet.skyflow.operationcenter.service.product;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.PriceDTO;
import com.deepinnet.skyflow.operationcenter.dto.PriceQueryDTO;

import java.util.List;

/**
 * 价格服务接口
 *
 * <AUTHOR>
 */
public interface PriceService {

    /**
     * 保存价格
     *
     * @param priceDTO 价格数据
     * @return 已保存的价格ID
     */
    Integer savePrice(PriceDTO priceDTO);

    /**
     * 批量保存价格
     *
     * @param priceDTOList 价格数据列表
     * @return 是否保存成功
     */
    boolean batchSavePrice(List<PriceDTO> priceDTOList);

    /**
     * 更新价格
     *
     * @param priceDTO 价格数据
     * @return 更新是否成功
     */
    boolean updatePrice(PriceDTO priceDTO);

    /**
     * 根据ID获取价格
     *
     * @param id 价格ID
     * @return 价格数据
     */
    PriceDTO getPriceById(Integer id);

    /**
     * 根据产品编号获取价格列表
     *
     * @param productNo 产品编号
     * @return 价格数据列表
     */
    List<PriceDTO> getPricesByProductNo(String productNo);

    /**
     * 分页查询价格
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    CommonPage<PriceDTO> pageQueryPrice(PriceQueryDTO queryDTO);

    /**
     * 删除价格
     *
     * @param id 价格ID
     * @return 删除是否成功
     */
    boolean deletePrice(Integer id);

    /**
     * 根据产品编号删除所有相关价格
     *
     * @param productNo 产品编号
     * @return 删除是否成功
     */
    boolean deletePricesByProductNo(String productNo);
} 