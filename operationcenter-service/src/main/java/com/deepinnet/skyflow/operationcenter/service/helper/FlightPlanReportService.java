package com.deepinnet.skyflow.operationcenter.service.helper;

import com.deepinnet.skyflow.operationcenter.dto.FileInfoDTO;
import com.deepinnet.skyflow.operationcenter.service.file.MinioService;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * Description: 飞行报告生成服务
 * Date: 2025/5/26
 * Author: lijunheng
 */
@Service
@Slf4j
public class FlightPlanReportService {

    @Resource
    private MinioService minioService;

    public FileInfoDTO generateFlightPlanReport(String planId) {
        //todo 生成飞行计划报告
        String templateFileName = "日常巡逻任务飞行报告.docx";
        String contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";

        // 从resources目录加载模板文件
        ClassPathResource resource = new ClassPathResource(templateFileName);

        if (!resource.exists()) {
            log.error("飞行报告文件不存在: {}", templateFileName);
            throw new RuntimeException("飞行报告文件不存在");
        }

        // 生成日期和唯一文件名
        LocalDate today = LocalDate.now();
        String dateStr = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String objectName = dateStr + "/" + IdGenerateUtil.getId("report") + "_" + planId + "飞行报告.docx";


        byte[] fileBytes = readFileContent(resource);

        // 上传文件到MinIO
        String fileUrl = minioService.uploadBytes(fileBytes, objectName, contentType);

        // 创建并返回文件信息
        return FileInfoDTO.builder()
                .url(fileUrl)
                .filename(templateFileName)
                .objectName(objectName)
                .size((long) fileBytes.length)
                .contentType(contentType)
                .bucketName(minioService.getDefaultBucketName())
                .build();

    }

    private byte[] readFileContent(org.springframework.core.io.Resource resource) {
        try (BufferedInputStream fis = new BufferedInputStream(resource.getInputStream())) {
            return fis.readAllBytes();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
