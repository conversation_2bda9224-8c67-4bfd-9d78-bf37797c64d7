package com.deepinnet.skyflow.operationcenter.service.demand.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.error.ErrorCode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.digitaltwin.common.util.JsonConvertUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandPlanRelationDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandPropDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandPlanRelationRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandPropRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandRepository;
import com.deepinnet.skyflow.operationcenter.dto.DemandSyncPlanBindDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandQueryDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandSyncStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.client.FlightPlanQueryClient;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightDemandPlanRelationConvert;
import com.deepinnet.skyflow.operationcenter.service.demand.FlightDemandService;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandMatchStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightDemandConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.infra.util.WktUtil;
import com.deepinnet.skyflow.operationcenter.service.validator.FlightDemandValidatorDispatcher;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.deepinnet.spatiotemporalplatform.client.area.GeoFenceClient;
import com.deepinnet.spatiotemporalplatform.model.area.StreetPolygonQueryParams;
import com.deepinnet.spatiotemporalplatform.model.area.StreetPolygonRespDTO;
import com.deepinnet.spatiotemporalplatform.vo.FlightPlanVO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 飞行需求服务实现类
 *
 * <AUTHOR>
 */
@Service
public class FlightDemandServiceImpl implements FlightDemandService {

    @Resource
    private FlightDemandRepository flightDemandRepository;

    @Resource
    private FlightDemandPropRepository flightDemandPropRepository;

    @Resource
    private FlightDemandConvert flightDemandConvert;

    @Resource
    private FlightDemandValidatorDispatcher flightDemandValidatorDispatcher;

    @Resource
    private FlightDemandPlanRelationRepository flightDemandPlanRelationRepository;

    @Resource
    private FlightDemandPlanRelationConvert flightDemandPlanRelationConvert;

    @Resource
    private GeoFenceClient geoFenceClient;

    @Resource
    private FlightPlanQueryClient flightPlanQueryClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveFlightDemand(FlightDemandDTO flightDemandDTO) {
        flightDemandValidatorDispatcher.validate(flightDemandDTO);

        setWKT(flightDemandDTO);

        String flightDemandCode = IdGenerateUtil.getId("FLIGHT_DEMAND");
        flightDemandDTO.setDemandNo(flightDemandCode);
        //初始化为未匹配状态
        flightDemandDTO.setMatchStatus(FlightDemandMatchStatusEnum.UNMATCHED);
        flightDemandDTO.setSyncStatus(FlightDemandSyncStatusEnum.UN_SYNCED);

        FlightDemandDO flightDemandDO = flightDemandConvert.convertToDO(flightDemandDTO);
        flightDemandDO.setPublishTime(LocalDateTime.now());

        boolean success = flightDemandRepository.save(flightDemandDO);
        if (!success) {
            LogUtil.error("保存飞行需求失败: {}", flightDemandDTO.getDemandNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        // 保存需求属性
        saveFlightDemandProps(flightDemandDTO);

        LogUtil.info("保存飞行需求成功: {}", flightDemandDO.getId());
        return flightDemandDTO.getDemandNo();
    }

    private void setWKT(FlightDemandDTO flightDemandDTO) {
        //如果没有设置WKT，说明需要后端查询区域的范围
        if (StrUtil.isBlank(flightDemandDTO.getAreaCoordinate())) {
            String inspectionAreaCode = flightDemandDTO.getInspectionAreaCode();
            if (StrUtil.isBlank(inspectionAreaCode)) {
                throw new BizException(ErrorCode.ILLEGAL_PARAMS.getCode(), "请设置巡检区域行政编号");
            }
            String[] codeSplit = inspectionAreaCode.split("/");
            StreetPolygonQueryParams streetPolygonQueryParams = new StreetPolygonQueryParams();
            //这里只查询街道的WKT
            streetPolygonQueryParams.setStreetid(Long.valueOf(codeSplit[3]));
            streetPolygonQueryParams.setPageSize(1);
            Result<CommonPage<StreetPolygonRespDTO>> streetPolygon = geoFenceClient.getStreetPolygon(streetPolygonQueryParams);
            if (!streetPolygon.isSuccess()) {
                throw new BizException(ErrorCode.ILLEGAL_PARAMS.getCode(), "获取街道信息失败");
            }
            StreetPolygonRespDTO streetPolygonRespDTO = streetPolygon.getData().getList().get(0);
            String areaWkt = streetPolygonRespDTO.getPolygon();
            String centerPointWkt = streetPolygonRespDTO.getCenter();
            Point point = WktUtil.toPoint(centerPointWkt);
            flightDemandDTO.setCenterPointLongitude(String.valueOf(point.getX()));
            flightDemandDTO.setCenterPointLatitude(String.valueOf(point.getY()));
            flightDemandDTO.setAreaCoordinate(areaWkt);
            Polygon polygon = WktUtil.toPolygon(areaWkt);
            //todo 这里需要做坐标系转换
            flightDemandDTO.setArea(polygon.getArea() / 1000000);
        }
    }

    @Override
    public FlightDemandDTO getFlightDemandByNo(String demandNo) {
        FlightDemandDO flightDemandDO = flightDemandRepository.getOne(
                Wrappers.lambdaQuery(FlightDemandDO.class)
                        .eq(FlightDemandDO::getDemandNo, demandNo)
        );
        if (flightDemandDO == null) {
            return null;
        }

        FlightDemandDTO flightDemandDTO = flightDemandConvert.convert(flightDemandDO);
        return readFromProp(flightDemandDTO);
    }

    @Override
    public CommonPage<FlightDemandDTO> pageQueryFlightDemand(FlightDemandQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<FlightDemandDO> queryWrapper = Wrappers.lambdaQuery(FlightDemandDO.class)
                .eq(StringUtils.isNotBlank(queryDTO.getDemandNo()), FlightDemandDO::getDemandNo, queryDTO.getDemandNo())
                .like(StringUtils.isNotBlank(queryDTO.getName()), FlightDemandDO::getName, queryDTO.getName())
                .eq(queryDTO.getType() != null, FlightDemandDO::getType, queryDTO.getType() != null ? queryDTO.getType().name() : null)
                .eq(StringUtils.isNotBlank(queryDTO.getPublisherNo()), FlightDemandDO::getPublisherNo, queryDTO.getPublisherNo())
                .like(StringUtils.isNotBlank(queryDTO.getPublisherName()), FlightDemandDO::getPublisherName, queryDTO.getPublisherName())
                .eq(StringUtils.isNotBlank(queryDTO.getFlightOrderNo()), FlightDemandDO::getFlightOrderNo, queryDTO.getFlightOrderNo())
                .eq(StringUtils.isNotBlank(queryDTO.getFlightUavBm()), FlightDemandDO::getFlightUavBm, queryDTO.getFlightUavBm())
                .eq(StringUtils.isNotBlank(queryDTO.getCategoryNo()), FlightDemandDO::getCategoryNo, queryDTO.getCategoryNo())
                .eq(queryDTO.getMatchStatus() != null, FlightDemandDO::getMatchStatus, queryDTO.getMatchStatus() != null ? queryDTO.getMatchStatus().name() : null)
                .eq(queryDTO.getSyncStatus() != null, FlightDemandDO::getSyncStatus, queryDTO.getSyncStatus() != null ? queryDTO.getSyncStatus().name() : null)
                .eq(StringUtils.isNotBlank(queryDTO.getServiceProviderNo()), FlightDemandDO::getServiceProviderNo, queryDTO.getServiceProviderNo())
                .like(StringUtils.isNotBlank(queryDTO.getServiceProviderName()), FlightDemandDO::getServiceProviderName, queryDTO.getServiceProviderName())
                .like(StringUtils.isNotBlank(queryDTO.getServiceProviderCompanyName()), FlightDemandDO::getServiceProviderCompanyName, queryDTO.getServiceProviderCompanyName())
                .eq(StringUtils.isNotBlank(queryDTO.getServiceProviderOrganizationId()), FlightDemandDO::getServiceProviderOrganizationId, queryDTO.getServiceProviderOrganizationId())
                .eq(StringUtils.isNotBlank(queryDTO.getOrganizationId()), FlightDemandDO::getOrganizationId, queryDTO.getOrganizationId())
                .like(StringUtils.isNotBlank(queryDTO.getOrganizationName()), FlightDemandDO::getOrganizationName, queryDTO.getOrganizationName())
                .like(StringUtils.isNotBlank(queryDTO.getProductName()), FlightDemandDO::getProductName, queryDTO.getProductName())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), FlightDemandDO::getTenantId, queryDTO.getTenantId())
                .orderByDesc(FlightDemandDO::getPublishTime);

        List<FlightDemandDO> flightDemandDOList = flightDemandRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(flightDemandDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<FlightDemandDTO> flightDemandDTOList = flightDemandConvert.convertList(flightDemandDOList);

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), page.getPages(), page.getTotal(), flightDemandDTOList);
    }

    @Override
    public void updateFlightDemandStatus(FlightDemandDTO flightDemandDTO) {
        if (StringUtils.isBlank(flightDemandDTO.getDemandNo())) {
            LogUtil.error("更新飞行需求失败，需求编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "需求编号不能为空");
        }

        // 检查需求是否存在
        FlightDemandDO existDemand = flightDemandRepository.getOne(
                Wrappers.lambdaQuery(FlightDemandDO.class)
                        .eq(FlightDemandDO::getDemandNo, flightDemandDTO.getDemandNo())
        );
        if (existDemand == null) {
            LogUtil.error("更新飞行需求匹配状态失败，需求不存在: {}", flightDemandDTO.getDemandNo());
            throw new BizException(BizErrorCode.DEMAND_NOT_FOUND.getCode(), BizErrorCode.DEMAND_NOT_FOUND.getDesc());
        }

        FlightDemandDO updateDO = new FlightDemandDO();
        updateDO.setDemandNo(flightDemandDTO.getDemandNo()); // 主键/条件字段必须设置

        if (flightDemandDTO.getMatchStatus() != null) {
            updateDO.setMatchStatus(flightDemandDTO.getMatchStatus().name());
        }
        if (flightDemandDTO.getSyncStatus() != null) {
            updateDO.setSyncStatus(flightDemandDTO.getSyncStatus().name());
        }
        if (StringUtils.isNotBlank(flightDemandDTO.getSyncOrderNo())) {
            updateDO.setSyncOrderNo(flightDemandDTO.getSyncOrderNo());
        }
        if (StringUtils.isNotBlank(flightDemandDTO.getServiceProviderNo())) {
            updateDO.setServiceProviderNo(flightDemandDTO.getServiceProviderNo());
        }
        if (StringUtils.isNotBlank(flightDemandDTO.getServiceProviderName())) {
            updateDO.setServiceProviderName(flightDemandDTO.getServiceProviderName());
        }
        if (StringUtils.isNotBlank(flightDemandDTO.getServiceProviderCompanyName())) {
            updateDO.setServiceProviderCompanyName(flightDemandDTO.getServiceProviderCompanyName());
        }
        if (flightDemandDTO.getServiceProviderOrganizationId() != null) {
            updateDO.setServiceProviderOrganizationId(flightDemandDTO.getServiceProviderOrganizationId());
        }

        boolean success = flightDemandRepository.update(updateDO,
                Wrappers.lambdaQuery(FlightDemandDO.class)
                        .eq(FlightDemandDO::getDemandNo, flightDemandDTO.getDemandNo()));
        if (!success) {
            LogUtil.error("更新飞行需求匹配状态失败: {}", flightDemandDTO.getDemandNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("更新飞行需求匹配状态成功: {}", flightDemandDTO.getDemandNo());
    }

    @Override
    public Boolean saveDemandPlanBind(DemandSyncPlanBindDTO relationDTO) {
        if (StringUtils.isBlank(relationDTO.getFlightDemandCode()) || StringUtils.isBlank(relationDTO.getPlanId())) {
            LogUtil.error("保存飞行需求与计划关联失败，参数不完整: {}", relationDTO);
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "需求编号和计划ID不能为空");
        }

        FlightDemandPlanRelationDO relationDO = flightDemandPlanRelationConvert.convertToDO(relationDTO);
        FlightDemandDTO flightDemand = this.getFlightDemandByNo(relationDTO.getFlightDemandCode());
        relationDO.setTenantId(flightDemand.getTenantId());
        try {
            flightDemandPlanRelationRepository.save(relationDO);
        } catch (DuplicateKeyException e) {
            //重复插入，说明已经存在，忽略
        }
        LogUtil.info("保存飞行需求与计划关联成功: {}", relationDO.getId());
        return true;
    }

    @Override
    public FlightDemandDTO getFlightDemandByPlanId(String planId) {
        FlightPlanVO flightPlanVO = flightPlanQueryClient.queryFlightPlan(planId);
        return this.getFlightDemandByNo(flightPlanVO.getBizNo());
    }

    /**
     * 保存需求属性
     *
     * @param flightDemandDTO 需求DTO
     */
    private void saveFlightDemandProps(FlightDemandDTO flightDemandDTO) {
        List<FlightDemandPropDO> flightDemandPropDOList = new ArrayList<>();

        // 根据需求类型获取对应的详情对象
        Object specialDetail = flightDemandDTO.queryDetail();
        if (specialDetail == null) {
            LogUtil.warn("飞行需求特殊详情为空: {}", flightDemandDTO.getDemandNo());
            return;
        }

        Field[] declaredFields = specialDetail.getClass().getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            try {
                Object obj = declaredField.get(specialDetail);
                if (obj != null) {
                    FlightDemandPropDO flightDemandPropDO = new FlightDemandPropDO();
                    flightDemandPropDO.setDemandNo(flightDemandDTO.getDemandNo());
                    flightDemandPropDO.setTenantId(flightDemandDTO.getTenantId());
                    flightDemandPropDO.setPropKey(declaredField.getName());
                    flightDemandPropDO.setPropValue(JsonConvertUtil.toJsonStr(obj));
                    flightDemandPropDOList.add(flightDemandPropDO);
                }
            } catch (IllegalAccessException e) {
                LogUtil.error("保存需求属性失败", e);
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "保存需求属性失败");
            }
        }

        if (CollectionUtil.isNotEmpty(flightDemandPropDOList)) {
            flightDemandPropRepository.saveBatch(flightDemandPropDOList);
        }
    }

    /**
     * 从属性表读取需求属性
     *
     * @param flightDemandDTO 需求DTO
     * @return 填充属性后的需求DTO
     */
    private FlightDemandDTO readFromProp(FlightDemandDTO flightDemandDTO) {
        List<FlightDemandPropDO> propDOList = flightDemandPropRepository.list(
                Wrappers.lambdaQuery(FlightDemandPropDO.class)
                        .eq(FlightDemandPropDO::getDemandNo, flightDemandDTO.getDemandNo())
        );

        if (CollectionUtil.isEmpty(propDOList)) {
            return flightDemandDTO;
        }

        Map<String, String> propMap = propDOList.stream()
                .collect(Collectors.toMap(FlightDemandPropDO::getPropKey, FlightDemandPropDO::getPropValue));

        // 根据需求类型，将属性设置到对应的详情字段
        flightDemandDTO.createDetail(getSpecialDetail(flightDemandDTO.getType().getClazz(), propMap));
        return flightDemandDTO;
    }

    private <T> T getSpecialDetail(Class<T> demandClass, Map<String, String> propMap) {
        Field[] declaredFields = demandClass.getDeclaredFields();
        Map<String, Object> objMap = new HashMap<>();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            String objStr = propMap.get(declaredField.getName());
            if (objStr == null) {
                continue;
            }
            objMap.put(declaredField.getName(), JsonConvertUtil.parseJson(objStr, declaredField.getType()));
        }

        return JsonConvertUtil.parseJson(JsonConvertUtil.toJsonStr(objMap), demandClass);
    }
}