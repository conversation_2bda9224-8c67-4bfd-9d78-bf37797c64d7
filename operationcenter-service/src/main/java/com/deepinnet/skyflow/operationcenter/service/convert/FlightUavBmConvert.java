package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavBmDO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmDTO;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavBmVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 飞行无人机品牌型号转换接口
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface FlightUavBmConvert {

    FlightUavBmConvert INSTANCE = Mappers.getMapper(FlightUavBmConvert.class);

    /**
     * DO 转 DTO
     *
     * @param flightUavBmDO DO 对象
     * @return DTO 对象
     */
    FlightUavBmDTO convert(FlightUavBmDO flightUavBmDO);

    /**
     * DTO 转 DO
     *
     * @param flightUavBmDTO DTO 对象
     * @return DO 对象
     */
    @Mapping(target = "gmtModified", ignore = true)
    FlightUavBmDO convertToDO(FlightUavBmDTO flightUavBmDTO);

    /**
     * DTO List 转 DO List
     *
     * @param flightUavBmDTOList DTO 列表
     * @return DO 列表
     */
    List<FlightUavBmDO> convertToDOList(List<FlightUavBmDTO> flightUavBmDTOList);

    /**
     * DO List 转 DTO List
     *
     * @param flightUavBmDOList DO 列表
     * @return DTO 列表
     */
    List<FlightUavBmDTO> convertList(List<FlightUavBmDO> flightUavBmDOList);

    /**
     * DTO 转 VO
     *
     * @param flightUavBmDTO DTO 对象
     * @return VO 对象
     */
    FlightUavBmVO convertToVO(FlightUavBmDTO flightUavBmDTO);

    /**
     * DTO列表 转 VO列表
     *
     * @param flightUavBmDTOList DTO 列表
     * @return VO 列表
     */
    List<FlightUavBmVO> convertToVOList(List<FlightUavBmDTO> flightUavBmDTOList);
} 