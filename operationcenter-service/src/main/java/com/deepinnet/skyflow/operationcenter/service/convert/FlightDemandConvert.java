package com.deepinnet.skyflow.operationcenter.service.convert;

import cn.hutool.core.lang.TypeReference;
import com.deepinnet.digitaltwin.common.util.JsonConvertUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO;
import com.deepinnet.skyflow.operationcenter.dto.FileDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.enums.*;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * 飞行需求转换接口
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface FlightDemandConvert {

    FlightDemandConvert INSTANCE = Mappers.getMapper(FlightDemandConvert.class);

    /**
     * DO 转 DTO
     *
     * @param flightDemandDO DO 对象
     * @return DTO 对象
     */
    @Mapping(source = "type", target = "type", qualifiedByName = "stringToFlightDemandTypeEnum")
    @Mapping(source = "matchStatus", target = "matchStatus", qualifiedByName = "stringToFlightDemandMatchStatusEnum")
    @Mapping(source = "syncStatus", target = "syncStatus", qualifiedByName = "stringToFlightDemandSyncStatusEnum")
    @Mapping(source = "requestAdditionalFiles", target = "requestAdditionalFiles", qualifiedByName = "stringToFileDTOListString")
    @Mapping(source = "incrementService", target = "incrementService", qualifiedByName = "stringToListString")
    @Mapping(source = "scene", target = "scene", qualifiedByName = "stringToFlightDemandSceneEnum")
    FlightDemandDTO convert(FlightDemandDO flightDemandDO);

    /**
     * DTO 转 DO
     *
     * @param flightDemandDTO DTO 对象
     * @return DO 对象
     */
    @Mapping(source = "type", target = "type", qualifiedByName = "flightDemandTypeEnumToString")
    @Mapping(source = "matchStatus", target = "matchStatus", qualifiedByName = "flightDemandMatchStatusEnumToString")
    @Mapping(source = "syncStatus", target = "syncStatus", qualifiedByName = "flightDemandSyncStatusEnumToString")
    @Mapping(source = "requestAdditionalFiles", target = "requestAdditionalFiles", qualifiedByName = "fileDTOListToString")
    @Mapping(source = "incrementService", target = "incrementService", qualifiedByName = "listStringToString")
    @Mapping(source = "scene", target = "scene", qualifiedByName = "flightDemandSceneEnumToString")
    FlightDemandDO convertToDO(FlightDemandDTO flightDemandDTO);

    /**
     * DTO List 转 DO List
     *
     * @param flightDemandDTOList DTO 列表
     * @return DO 列表
     */
    List<FlightDemandDO> convertToDOList(List<FlightDemandDTO> flightDemandDTOList);

    /**
     * DO List 转 DTO List
     *
     * @param flightDemandDOList DO 列表
     * @return DTO 列表
     */
    List<FlightDemandDTO> convertList(List<FlightDemandDO> flightDemandDOList);

    /**
     * DTO 转 VO
     *
     * @param flightDemandDTO DTO 对象
     * @return VO 对象
     */
    FlightDemandVO convertToVO(FlightDemandDTO flightDemandDTO);

    /**
     * DTO列表 转 VO列表
     *
     * @param flightDemandDTOList DTO 列表
     * @return VO 列表
     */
    List<FlightDemandVO> convertToVOList(List<FlightDemandDTO> flightDemandDTOList);

    /**
     * String 转 List<String>
     *
     * @param value String 值
     * @return List<String> 值
     */
    @Named("stringToListString")
    default List<String> stringToListString(String value) {
        if (value == null || value.isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return JsonConvertUtil.parseJson(value, List.class);
        } catch (Exception e) {
            List<String> result = new ArrayList<>();
            result.add(value);
            return result;
        }
    }

    /**
     * List<String> 转 String
     *
     * @param value List<String> 值
     * @return String 值
     */
    @Named("listStringToString")
    default String listStringToString(List<String> value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        return JsonConvertUtil.toJsonStr(value);
    }

    /**
     * String 转 FlightDemandTypeEnum
     */
    @Named("stringToFlightDemandTypeEnum")
    default FlightDemandTypeEnum stringToFlightDemandTypeEnum(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        return FlightDemandTypeEnum.valueOf(value);
    }

    /**
     * FlightDemandTypeEnum 转 String
     */
    @Named("flightDemandTypeEnumToString")
    default String flightDemandTypeEnumToString(FlightDemandTypeEnum value) {
        if (value == null) {
            return null;
        }
        return value.name();
    }

    /**
     * String 转 FlightServiceTypeEnum
     */
    @Named("stringToFlightServiceTypeEnum")
    default FlightServiceTypeEnum stringToFlightServiceTypeEnum(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        return FlightServiceTypeEnum.valueOf(value);
    }

    /**
     * FlightServiceTypeEnum 转 String
     */
    @Named("flightServiceTypeEnumToString")
    default String flightServiceTypeEnumToString(FlightServiceTypeEnum value) {
        if (value == null) {
            return null;
        }
        return value.name();
    }

    /**
     * String 转 FlightDemandMatchStatusEnum
     */
    @Named("stringToFlightDemandMatchStatusEnum")
    default FlightDemandMatchStatusEnum stringToFlightDemandMatchStatusEnum(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        return FlightDemandMatchStatusEnum.valueOf(value);
    }

    /**
     * FlightDemandMatchStatusEnum 转 String
     */
    @Named("flightDemandMatchStatusEnumToString")
    default String flightDemandMatchStatusEnumToString(FlightDemandMatchStatusEnum value) {
        if (value == null) {
            return null;
        }
        return value.name();
    }

    @Named("stringToFlightDemandSyncStatusEnum")
    default FlightDemandSyncStatusEnum stringToFlightDemandSyncStatusEnum(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        return FlightDemandSyncStatusEnum.valueOf(value);
    }

    @Named("flightDemandSyncStatusEnumToString")
    default String flightDemandSyncStatusEnumToString(FlightDemandSyncStatusEnum value) {
        if (value == null) {
            return null;
        }
        return value.name();
    }


    @Named("flightDemandSceneEnumToString")
    default String flightDemandSceneEnumToString(FlightDemandSceneEnum value) {
        if (value == null) {
            return null;
        }
        return value.name();
    }

    @Named("stringToFlightDemandSceneEnum")
    default FlightDemandSceneEnum stringToFlightDemandSceneEnum(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        return FlightDemandSceneEnum.valueOf(value);
    }

    @Named("stringToFileDTOListString")
    default List<FileDTO> stringToFileDTOListString(String value) {
        if (value == null || value.isEmpty()) {
            return new ArrayList<>();
        }
        return JsonConvertUtil.parseJson(value, new TypeReference<List<FileDTO>>() {});
    }

    @Named("fileDTOListToString")
    default String fileDTOListToString(List<FileDTO> value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        return JsonConvertUtil.toJsonStr(value);
    }
} 