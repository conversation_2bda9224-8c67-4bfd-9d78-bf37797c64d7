package com.deepinnet.skyflow.operationcenter.service;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * Description:
 * Date: 2025/5/12
 * Author: lijunheng
 */
@Component
public class RedisTaskStateManager {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final String START_SCRIPT_WITH_TTL =
            // ARGV[1]: "started"
            // ARGV[2]: TTL 秒数（字符串）
            "if redis.call('SETNX', KEYS[1], ARGV[1]) == 1 then\n" +
                    "  redis.call('EXPIRE', KEYS[1], tonumber(ARGV[2]))\n" +
                    "  return 1\n" +
                    "else\n" +
                    "  return 0\n" +
                    "end";

    private static final String END_SCRIPT_WITH_TTL =
            // ARGV[1]: 当前值必须是 "started"
            // ARGV[2]: 要设置的新值 "ended"
            // ARGV[3]: TTL 秒数（字符串）
            "local current = redis.call('GET', KEYS[1])\n" +
                    "if current == ARGV[1] then\n" +
                    "  redis.call('SET', KEYS[1], ARGV[2])\n" +
                    "  redis.call('EXPIRE', KEYS[1], tonumber(ARGV[3]))\n" +
                    "  return 1\n" +
                    "else\n" +
                    "  return 0\n" +
                    "end";

    public boolean tryStart(String taskId, long ttlSeconds) {
        String key = buildKey(taskId);
        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(START_SCRIPT_WITH_TTL);
        script.setResultType(Long.class);

        Long result = stringRedisTemplate.execute(
                script,
                Collections.singletonList(key),
                "started", String.valueOf(ttlSeconds)
        );
        return result != null && result == 1L;
    }

    public boolean tryEnd(String taskId, long ttlSeconds) {
        String key = buildKey(taskId);
        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(END_SCRIPT_WITH_TTL);
        script.setResultType(Long.class);

        Long result = stringRedisTemplate.execute(
                script,
                Collections.singletonList(key),
                "started", "ended", String.valueOf(ttlSeconds)
        );
        return result != null && result == 1L;
    }

    private String buildKey(String taskId) {
        return "task_status:" + taskId;
    }
}

