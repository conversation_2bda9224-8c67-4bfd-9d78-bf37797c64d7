package com.deepinnet.skyflow.operationcenter.service.product.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavBmDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavBmImportDetailDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavBmImportRecordDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightUavBmImportDetailRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightUavBmImportRecordRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightUavBmRepository;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportDetailDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportRecordDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportRequestDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportResultDTO;
import com.deepinnet.skyflow.operationcenter.dto.PageQueryDTO;
import com.deepinnet.skyflow.operationcenter.enums.BizTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavCrewTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavFlyTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavWeightClassificationEnum;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavBmConvert;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavBmImportDetailConvert;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavBmImportRecordConvert;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavBmImportService;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavBmService;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 飞行无人机品牌型号导入服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FlightUavBmImportServiceImpl implements FlightUavBmImportService {

    @Resource
    private FlightUavBmRepository flightUavBmRepository;

    @Resource
    private FlightUavBmImportRecordRepository importRecordRepository;

    @Resource
    private FlightUavBmImportDetailRepository importDetailRepository;

    @Resource
    private FlightUavBmService flightUavBmService;

    @Resource
    private FlightUavBmConvert flightUavBmConvert;

    @Resource
    private FlightUavBmImportRecordConvert importRecordConvert;

    @Resource
    private FlightUavBmImportDetailConvert importDetailConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FlightUavBmImportResultDTO importFlightUavBm(MultipartFile file, FlightUavBmImportRequestDTO requestDTO) {
        // 生成导入批次号
        String importBatchNo = IdGenerateUtil.getId(BizTypeEnum.FLIGHT_UAV_BM_IMPORT.getType());
        requestDTO.setImportBatchNo(importBatchNo);

        // 创建导入记录
        FlightUavBmImportRecordDTO importRecordDTO = createImportRecord(importBatchNo, requestDTO);

        try {
            // 解析Excel文件
            List<Map<Integer, String>> dataList = parseExcel(file);
            if (CollectionUtils.isEmpty(dataList)) {
                updateImportRecordStatus(importRecordDTO, 2, "导入文件为空");
                return buildEmptyResult(importBatchNo);
            }

            // 处理Excel数据
            return processExcelData(dataList, importBatchNo, requestDTO);
        } catch (Exception e) {
            // 更新导入记录状态为失败
            log.error("导入飞行无人机品牌型号失败", e);
            updateImportRecordStatus(importRecordDTO, 2, "导入异常: " + e.getMessage());
            return buildFailResult(importBatchNo, e.getMessage());
        }
    }

    @Override
    public FlightUavBmImportRecordDTO getImportRecord(String importBatchNo) {
        FlightUavBmImportRecordDO importRecordDO = importRecordRepository.getOne(
                Wrappers.lambdaQuery(FlightUavBmImportRecordDO.class)
                        .eq(FlightUavBmImportRecordDO::getImportBatchNo, importBatchNo)
        );
        return importRecordConvert.convert(importRecordDO);
    }

    @Override
    public CommonPage<FlightUavBmImportRecordDTO> pageQueryImportRecord(PageQueryDTO queryDTO) {
        Page<FlightUavBmImportRecordDO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        LambdaQueryWrapper<FlightUavBmImportRecordDO> queryWrapper = 
                Wrappers.lambdaQuery(FlightUavBmImportRecordDO.class)
                        .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), 
                                FlightUavBmImportRecordDO::getTenantId, queryDTO.getTenantId())
                        .orderByDesc(FlightUavBmImportRecordDO::getGmtCreated);
        
        Page<FlightUavBmImportRecordDO> pageResult = importRecordRepository.page(page, queryWrapper);
        
        List<FlightUavBmImportRecordDTO> records = importRecordConvert.convertList(pageResult.getRecords());
        
        return CommonPage.buildPage((int)pageResult.getCurrent(), (int)pageResult.getSize(), (int)pageResult.getPages(), pageResult.getTotal(), records);
    }

    @Override
    public List<FlightUavBmImportDetailDTO> listImportDetail(String importBatchNo) {
        List<FlightUavBmImportDetailDO> detailList = importDetailRepository.list(
                Wrappers.lambdaQuery(FlightUavBmImportDetailDO.class)
                        .eq(FlightUavBmImportDetailDO::getImportBatchNo, importBatchNo)
                        .orderByAsc(FlightUavBmImportDetailDO::getRowNum)
        );
        return importDetailConvert.convertList(detailList);
    }

    @Override
    public CommonPage<FlightUavBmImportDetailDTO> pageQueryImportDetail(String importBatchNo, PageQueryDTO queryDTO) {
        Page<FlightUavBmImportDetailDO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        LambdaQueryWrapper<FlightUavBmImportDetailDO> queryWrapper = 
                Wrappers.lambdaQuery(FlightUavBmImportDetailDO.class)
                        .eq(FlightUavBmImportDetailDO::getImportBatchNo, importBatchNo)
                        .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), 
                                FlightUavBmImportDetailDO::getTenantId, queryDTO.getTenantId())
                        .orderByAsc(FlightUavBmImportDetailDO::getRowNum);
        
        Page<FlightUavBmImportDetailDO> pageResult = importDetailRepository.page(page, queryWrapper);
        
        List<FlightUavBmImportDetailDTO> records = importDetailConvert.convertList(pageResult.getRecords());

        return CommonPage.buildPage((int)pageResult.getCurrent(), (int)pageResult.getSize(), (int)pageResult.getPages(), pageResult.getTotal(), records);
    }

    /**
     * 创建导入记录
     *
     * @param importBatchNo 导入批次号
     * @param requestDTO 导入请求DTO
     * @return 导入记录DTO
     */
    private FlightUavBmImportRecordDTO createImportRecord(String importBatchNo, FlightUavBmImportRequestDTO requestDTO) {
        FlightUavBmImportRecordDTO recordDTO = new FlightUavBmImportRecordDTO();
        recordDTO.setImportBatchNo(importBatchNo);
        recordDTO.setImportStatus(0); // 导入中
        recordDTO.setCreator(requestDTO.getCreator());
        recordDTO.setTenantId(requestDTO.getTenantId());
        
        // 保存导入记录
        FlightUavBmImportRecordDO recordDO = new FlightUavBmImportRecordDO();
        recordDO.setImportBatchNo(importBatchNo);
        recordDO.setImportStatus(0);
        recordDO.setCreator(requestDTO.getCreator());
        recordDO.setModifier(requestDTO.getCreator());
        recordDO.setTenantId(requestDTO.getTenantId());
        recordDO.setGmtCreated(LocalDateTime.now());
        recordDO.setGmtModified(LocalDateTime.now());
        recordDO.setIsDeleted(false);
        importRecordRepository.save(recordDO);
        
        return recordDTO;
    }

    /**
     * 更新导入记录状态
     *
     * @param importRecordDTO 导入记录DTO
     * @param status 状态（0-导入中，1-导入成功，2-导入失败）
     * @param failReason 失败原因
     */
    private void updateImportRecordStatus(FlightUavBmImportRecordDTO importRecordDTO, int status, String failReason) {
        importRecordDTO.setImportStatus(status);
        importRecordDTO.setFailReason(failReason);
        updateImportRecord(importRecordDTO);
    }

    /**
     * 解析Excel文件
     *
     * @param file Excel文件
     * @return 解析结果
     */
    private List<Map<Integer, String>> parseExcel(MultipartFile file) throws IOException {
        List<Map<Integer, String>> result = new ArrayList<>();
        
        try {
            // 定义标题行索引，标题行通常是第4行（索引为3）
            final int headRowIndex = 3;
            
            // 使用EasyExcel读取Excel文件
            EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, Object>>() {
                // 当前行索引
                private int currentRowIndex = 0;
                
                @Override
                public void invoke(Map<Integer, Object> rowData, AnalysisContext context) {
                    // 记录当前行索引
                    currentRowIndex = context.readRowHolder().getRowIndex();
                    
                    // 跳过标题行及之前的行
                    if (currentRowIndex < headRowIndex) {
                        return;
                    }
                    
                    // 转换为Map<Integer, String>格式
                    Map<Integer, String> stringMap = new HashMap<>();
                    for (Map.Entry<Integer, Object> entry : rowData.entrySet()) {
                        Object value = entry.getValue();
                        stringMap.put(entry.getKey(), value == null ? null : value.toString());
                    }
                    
                    // 检查是否为空行
                    if (!isEmptyRow(stringMap)) {
                        result.add(stringMap);
                    }
                }
                
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel解析完成，共解析{}行数据", result.size());
                }
                
                @Override
                public void onException(Exception exception, AnalysisContext context) throws Exception {
                    log.error("Excel解析异常", exception);
                    if (exception instanceof ExcelAnalysisException) {
                        throw new IllegalArgumentException("Excel格式不正确，无法解析", exception);
                    }
                    super.onException(exception, context);
                }
            }).doReadAll();
            
            log.info("解析Excel文件成功: {}, 共解析{}行数据", file.getOriginalFilename(), result.size());
            
            // 校验结果
            if (result.isEmpty()) {
                log.warn("Excel文件无有效数据");
            } else {
                // 校验每一行数据
                for (int i = 0; i < result.size(); i++) {
                    Map<Integer, String> rowData = result.get(i);
                    int rowNum = i + headRowIndex + 1; // 实际Excel中的行号
                    validateRowData(rowData, rowNum);
                }
            }
        } catch (Exception e) {
            log.error("解析Excel文件失败: {}", file.getOriginalFilename(), e);
            if (e instanceof IllegalArgumentException) {
                throw e;
            }
            throw new IllegalArgumentException("Excel文件解析失败: " + e.getMessage(), e);
        }
        
        return result;
    }
    
    /**
     * 检查行是否为空
     *
     * @param rowData 行数据
     * @return 是否为空行
     */
    private boolean isEmptyRow(Map<Integer, String> rowData) {
        // 检查前5列，如果都为空则认为整行为空
        for (int i = 0; i < 5; i++) {
            if (StringUtils.isNotBlank(getStringValue(rowData, i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 创建导入明细记录
     *
     * @param importBatchNo 导入批次号
     * @param rowNum 行号
     * @param rawData 原始数据
     * @param isSuccess 是否成功
     * @param failReason 失败原因
     * @param requestDTO 导入请求参数
     */
    private void createImportDetail(String importBatchNo, int rowNum, Map<Integer, String> rawData, 
                                    boolean isSuccess, String failReason, FlightUavBmImportRequestDTO requestDTO) {
        try {
            FlightUavBmImportDetailDO detailDO = new FlightUavBmImportDetailDO();
            detailDO.setImportBatchNo(importBatchNo);
            detailDO.setRowNum(rowNum);
            detailDO.setRawData(rawData.toString());
            detailDO.setProcessStatus(isSuccess ? 1 : 2); // 处理状态：1-成功，2-失败
            detailDO.setGmtCreated(LocalDateTime.now());
            detailDO.setGmtModified(LocalDateTime.now());
            detailDO.setCreator(requestDTO.getCreator());
            detailDO.setModifier(requestDTO.getCreator());
            detailDO.setTenantId(requestDTO.getTenantId());
            detailDO.setIsDeleted(false);
            
            if (isSuccess) {
                // 如果成功，转换并保存无人机品牌型号
                FlightUavBmDTO dto = convertToDTO(rawData, requestDTO.getTenantId());
                String flightUavBmNo = flightUavBmService.saveFlightUavBm(dto);
                detailDO.setFlightUavBmNo(flightUavBmNo);
            } else {
                detailDO.setFailReason(failReason);
            }
            
            importDetailRepository.save(detailDO);
        } catch (Exception e) {
            log.error("创建导入明细记录失败", e);
        }
    }

    /**
     * 验证行数据
     *
     * @param rowData 行数据
     * @param rowNum 行号
     */
    private void validateRowData(Map<Integer, String> rowData, int rowNum) {
        // 检查必填字段
        String manufacturer = getStringValue(rowData, 1);
        if (StringUtils.isBlank(manufacturer)) {
            throw createValidationException("厂家/品牌不能为空", "厂家/品牌", manufacturer);
        }
        
        String model = getStringValue(rowData, 2);
        if (StringUtils.isBlank(model)) {
            throw createValidationException("机型不能为空", "机型", model);
        }
        
        // 检查枚举字段格式
        String flyTypeDesc = getStringValue(rowData, 0);
        if (StringUtils.isNotBlank(flyTypeDesc) && convertToFlyTypeEnum(flyTypeDesc) == null) {
            throw createValidationException("无效的机型类目", "机型类目", flyTypeDesc);
        }
        
        String crewTypeDesc = getStringValue(rowData, 3);
        if (StringUtils.isNotBlank(crewTypeDesc) && convertToCrewTypeEnum(crewTypeDesc) == null) {
            throw createValidationException("无效的载人类型", "载人类型", crewTypeDesc);
        }
        
        String weightClassDesc = getStringValue(rowData, 4);
        if (StringUtils.isNotBlank(weightClassDesc) && convertToWeightClassificationEnum(weightClassDesc) == null) {
            throw createValidationException("无效的重量分类", "重量分类", weightClassDesc);
        }
        
        // 检查数值字段格式
        validateNumberField(rowData, 5, "最长飞行时间");
        validateNumberField(rowData, 6, "最大飞行半径");
        validateNumberField(rowData, 7, "最大续航里程");
        validateNumberField(rowData, 8, "最大载物重量");
        validateNumberField(rowData, 9, "最大飞行高度");

        // 检查视频拍摄字段
        String videoSupport = getStringValue(rowData, 10);
        if (StringUtils.isNotBlank(videoSupport)) {
            if (!isValidVideoSupportValue(videoSupport)) {
                throw createValidationException("视频拍摄字段值不合法，应为：是/否/支持/不支持/true/false/1/0", "视频拍摄", videoSupport);
            }
        }
    }
    
    /**
     * 创建验证异常
     *
     * @param message 错误消息
     * @param columnName 列名
     * @param value 错误值
     * @return 异常
     */
    private ValidationException createValidationException(String message, String columnName, String value) {
        ValidationException exception = new ValidationException(message);
        exception.setColumnName(columnName);
        exception.setValue(value);
        return exception;
    }
    
    /**
     * 验证数值字段
     *
     * @param rowData 行数据
     * @param columnIndex 列索引
     * @param fieldName 字段名称
     */
    private void validateNumberField(Map<Integer, String> rowData, int columnIndex, String fieldName) {
        String value = getStringValue(rowData, columnIndex);
        if (StringUtils.isNotBlank(value)) {
            try {
                new BigDecimal(value);
            } catch (NumberFormatException e) {
                throw createValidationException(fieldName + "必须是数字", fieldName, value);
            }
        }
    }
    
    /**
     * 检查视频支持字段的值是否合法
     *
     * @param value 字段值
     * @return 是否合法
     */
    private boolean isValidVideoSupportValue(String value) {
        return "是".equals(value) || "否".equals(value) || 
               "支持".equals(value) || "不支持".equals(value) || 
               "true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value) || 
               "1".equals(value) || "0".equals(value);
    }
    
    /**
     * 自定义验证异常类
     */
    private static class ValidationException extends IllegalArgumentException {
        private String columnName;
        private String value;
        
        public ValidationException(String message) {
            super(message);
        }
        
        public void setColumnName(String columnName) {
            this.columnName = columnName;
        }
        
        public String getColumnName() {
            return columnName;
        }
        
        public void setValue(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
    }

    /**
     * 处理Excel数据
     *
     * @param excelData       Excel数据
     * @param importBatchNo   导入批次号
     * @param requestDTO      导入请求参数
     * @return 处理结果
     */
    private FlightUavBmImportResultDTO processExcelData(List<Map<Integer, String>> excelData, String importBatchNo, FlightUavBmImportRequestDTO requestDTO) {
        FlightUavBmImportResultDTO resultDTO = new FlightUavBmImportResultDTO();
        resultDTO.setImportBatchNo(importBatchNo);
        resultDTO.setImportStatus(1); // 默认成功
        
        if (CollectionUtils.isEmpty(excelData)) {
            resultDTO.setImportStatus(0);
            resultDTO.setFailReason("Excel数据为空");
            return resultDTO;
        }
        
        // 创建导入记录
        FlightUavBmImportRecordDTO recordDTO = createImportRecord(importBatchNo, requestDTO);
        
        // 保存导入总记录数
        int totalRows = excelData.size();
        recordDTO.setTotalCount(totalRows);
        resultDTO.setTotalCount(totalRows);
        
        // 跟踪成功和失败的记录
        int successCount = 0;
        int failCount = 0;
        List<FlightUavBmImportResultDTO.FlightUavBmImportErrorDTO> errorList = new ArrayList<>();
        
        // 用于检查本次导入中的重复数据
        Set<String> importedModels = new HashSet<>();
        
        // 处理每一行数据
        for (int i = 0; i < excelData.size(); i++) {
            Map<Integer, String> rowData = excelData.get(i);
            int rowNum = i + 4; // Excel中的行号，假设数据从第4行开始
            
            try {
                // 检查是否为空行
                if (isEmptyRow(rowData)) {
                    continue; // 跳过空行，不计入成功或失败
                }
                
                // 提取品牌和型号信息用于重复校验
                String brandName = getStringValue(rowData, 1); // 厂家/品牌
                String modelNo = getStringValue(rowData, 2); // 机型
                String modelKey = brandName + "|" + modelNo;
                
                // 检查是否在当前导入批次中重复
                if (importedModels.contains(modelKey)) {
                    throw new IllegalArgumentException("导入的Excel中存在重复的品牌和机型组合");
                }
                
                // 添加到已导入集合
                importedModels.add(modelKey);
                
                // 检查数据库中是否已存在相同品牌和型号
                if (checkDuplicateInDatabase(brandName, modelNo, requestDTO.getTenantId())) {
                    throw new IllegalArgumentException("系统中已存在相同的品牌和机型组合");
                }
                
                // 检查图片URL格式
                validateImageUrls(rowData);
                
                // 转换为DTO
                FlightUavBmDTO flightUavBmDTO = convertToDTO(rowData, requestDTO.getTenantId());
                
                // 保存到数据库
                flightUavBmService.saveFlightUavBm(flightUavBmDTO);
                
                // 创建导入明细记录
                createImportDetail(importBatchNo, rowNum, rowData, true, null, requestDTO);
                
                successCount++;
            } catch (Exception e) {
                String errorMessage = e.getMessage();
                if (errorMessage == null || errorMessage.trim().isEmpty()) {
                    errorMessage = "未知错误";
                }
                
                // 创建导入明细记录
                createImportDetail(importBatchNo, rowNum, rowData, false, errorMessage, requestDTO);
                
                // 添加到错误列表
                FlightUavBmImportResultDTO.FlightUavBmImportErrorDTO errorDTO = new FlightUavBmImportResultDTO.FlightUavBmImportErrorDTO();
                errorDTO.setRowNum(rowNum);
                errorDTO.setErrorReason(errorMessage);
                
                // 尝试提取错误字段和值
                if (e instanceof ValidationException) {
                    ValidationException ve = (ValidationException) e;
                    errorDTO.setErrorColumnName(ve.getColumnName());
                    errorDTO.setErrorValue(ve.getValue());
                } else if (e instanceof IllegalArgumentException) {
                    String errorMsg = e.getMessage();
                    if (errorMsg.contains("必须是数字") || errorMsg.contains("不能为空")) {
                        String fieldName = extractFieldName(errorMsg);
                        String fieldValue = extractFieldValue(rowData, fieldName);
                        errorDTO.setErrorColumnName(fieldName);
                        errorDTO.setErrorValue(fieldValue);
                    }
                }
                
                errorList.add(errorDTO);
                failCount++;
                
                log.error("处理第{}行数据失败: {}", rowNum, errorMessage, e);
            }
        }
        
        // 更新导入记录
        recordDTO.setSuccessCount(successCount);
        recordDTO.setFailCount(failCount);
        
        if (failCount > 0) {
            recordDTO.setImportStatus(2); // 部分失败
            resultDTO.setImportStatus(0); // 设置为失败状态
            resultDTO.setFailReason("部分数据导入失败，共" + failCount + "条");
        } else if (successCount == 0) {
            recordDTO.setImportStatus(2); // 全部失败
            resultDTO.setImportStatus(0); // 设置为失败状态
            resultDTO.setFailReason("全部数据导入失败");
        } else {
            recordDTO.setImportStatus(1); // 全部成功
            resultDTO.setImportStatus(1); // 成功状态
        }
        
        // 更新导入记录
        updateImportRecord(recordDTO);
        
        // 设置返回结果
        resultDTO.setSuccessCount(successCount);
        resultDTO.setFailCount(failCount);
        resultDTO.setErrorList(errorList);
        
        return resultDTO;
    }
    
    /**
     * 验证图片URL格式
     *
     * @param rowData 行数据
     */
    private void validateImageUrls(Map<Integer, String> rowData) {
        for (int i = 12; i <= 16; i++) {
            String imageUrl = getStringValue(rowData, i);
            if (StringUtils.isNotBlank(imageUrl)) {
                if (!isValidUrl(imageUrl)) {
                    throw new IllegalArgumentException("图片URL格式不正确: " + imageUrl);
                }
            }
        }
    }
    
    /**
     * 检查URL格式是否有效
     *
     * @param url URL字符串
     * @return 是否有效
     */
    private boolean isValidUrl(String url) {
        // 简单的URL格式验证，实际项目中可能需要更复杂的验证
        return url.startsWith("http://") || url.startsWith("https://");
    }

    /**
     * 将Excel数据转换为DTO对象
     *
     * @param rowData 行数据
     * @param tenantId 租户ID
     * @return DTO对象
     */
    private FlightUavBmDTO convertToDTO(Map<Integer, String> rowData, String tenantId) {
        try {
            FlightUavBmDTO dto = new FlightUavBmDTO();
            
            // 设置基本信息
            String flyTypeDesc = getStringValue(rowData, 0); // 机型类目
            // 将机型类目描述转换为对应的枚举
            dto.setFlightUavFlyType(convertToFlyTypeEnum(flyTypeDesc));
            
            dto.setFlightUavBmName(getRequiredStringValue(rowData, 1, "厂家/品牌")); // 厂家/品牌
            dto.setFlightUavBmModelNo(getRequiredStringValue(rowData, 2, "机型")); // 机型
            
            String crewTypeDesc = getStringValue(rowData, 3); // 载人类型
            // 将载人类型描述转换为对应的枚举
            dto.setFlightUavCrewType(convertToCrewTypeEnum(crewTypeDesc));
            
            String weightClassDesc = getStringValue(rowData, 4); // 重量分类
            // 将重量分类描述转换为对应的枚举
            dto.setFlightUavWeightClassfication(convertToWeightClassificationEnum(weightClassDesc));
            
            // 设置飞行数据
            String maxFlyMinuteStr = getStringValue(rowData, 5); // 最长飞行时间
            if (StringUtils.isNotBlank(maxFlyMinuteStr)) {
                try {
                    dto.setFlightUavMaxFlyMinute(Integer.parseInt(maxFlyMinuteStr));
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("最长飞行时间必须是数字");
                }
            }
            
            String maxFlyRadiusStr = getStringValue(rowData, 6); // 最大飞行半径
            if (StringUtils.isNotBlank(maxFlyRadiusStr)) {
                try {
                    dto.setFlightUavRadius(new BigDecimal(maxFlyRadiusStr));
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("最大飞行半径必须是数字");
                }
            }
            
            String maxFlyRangeStr = getStringValue(rowData, 7); // 最大续航里程
            if (StringUtils.isNotBlank(maxFlyRangeStr)) {
                try {
                    dto.setFlightUavMaxFlyRange(new BigDecimal(maxFlyRangeStr));
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("最大续航里程必须是数字");
                }
            }
            
            String maxCarrierWeightStr = getStringValue(rowData, 8); // 最大载物重量
            if (StringUtils.isNotBlank(maxCarrierWeightStr)) {
                try {
                    dto.setFlightUavMaxCarrierWeight(new BigDecimal(maxCarrierWeightStr));
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("最大载物重量必须是数字");
                }
            }
            
            String maxFlyHeightStr = getStringValue(rowData, 9); // 最大飞行高度
            if (StringUtils.isNotBlank(maxFlyHeightStr)) {
                try {
                    dto.setFlightUavMaxFlyHeight(Integer.parseInt(maxFlyHeightStr));
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("最大飞行高度必须是数字");
                }
            }
            
            // 设置视频支持
            String videoSupport = getStringValue(rowData, 10); // 视频拍摄
            if (StringUtils.isNotBlank(videoSupport)) {
                if ("是".equals(videoSupport) || "支持".equals(videoSupport) || "true".equalsIgnoreCase(videoSupport) || "1".equals(videoSupport)) {
                    dto.setFlightUavSupportVedio(true);
                } else if ("否".equals(videoSupport) || "不支持".equals(videoSupport) || "false".equalsIgnoreCase(videoSupport) || "0".equals(videoSupport)) {
                    dto.setFlightUavSupportVedio(false);
                } else {
                    throw new IllegalArgumentException("视频拍摄字段值不合法，应为：是/否/支持/不支持");
                }
            }
            
            // 设置相机像素
            String cameraPixel = getStringValue(rowData, 11); // 相机像素
            if (StringUtils.isNotBlank(cameraPixel)) {
                dto.setFlightUavCameraPixel(cameraPixel);
            }
            
            // 设置图片信息 - 收集所有图片URL到一个集合中
            List<String> pictureUrls = new ArrayList<>();
            for (int i = 12; i <= 16; i++) {
                String imageUrl = getStringValue(rowData, i);
                if (StringUtils.isNotBlank(imageUrl)) {
                    pictureUrls.add(imageUrl);
                }
            }
            
            // 如果有图片URL，设置到flightUavPictures字段
            if (!pictureUrls.isEmpty()) {
                dto.setFlightUavPictures(pictureUrls.toArray(new String[0]));
            }
            
            // 设置租户ID
            dto.setTenantId(tenantId);
            
            return dto;
        } catch (Exception e) {
            if (e instanceof IllegalArgumentException) {
                throw e;
            }
            throw new IllegalArgumentException("数据转换失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取必填字段值
     *
     * @param rowData 行数据
     * @param columnIndex 列索引
     * @param fieldName 字段名
     * @return 字段值
     */
    private String getRequiredStringValue(Map<Integer, String> rowData, int columnIndex, String fieldName) {
        String value = getStringValue(rowData, columnIndex);
        if (StringUtils.isBlank(value)) {
            throw createValidationException(fieldName + "不能为空", fieldName, value);
        }
        return value;
    }

    /**
     * 获取字段值，安全地处理可能存在的键
     *
     * @param rowData 行数据
     * @param columnIndex 列索引
     * @return 字段值
     */
    private String getStringValue(Map<Integer, String> rowData, int columnIndex) {
        String value = rowData.get(columnIndex);
        return value == null ? "" : value.trim();
    }
    
    /**
     * 检查数据库中是否已存在相同机型
     *
     * @param brandName 品牌名称
     * @param modelNo 机型编号
     * @param tenantId 租户ID
     * @return 是否存在相同机型
     */
    private boolean checkDuplicateInDatabase(String brandName, String modelNo, String tenantId) {
        LambdaQueryWrapper<FlightUavBmDO> queryWrapper = Wrappers.lambdaQuery(FlightUavBmDO.class)
                .eq(FlightUavBmDO::getFlightUavBmName, brandName)
                .eq(FlightUavBmDO::getFlightUavBmModelNo, modelNo)
                .eq(StringUtils.isNotBlank(tenantId), FlightUavBmDO::getTenantId, tenantId)
                .eq(FlightUavBmDO::getIsDeleted, false);
        
        FlightUavBmDO existingDO = flightUavBmRepository.getOne(queryWrapper);
        return existingDO != null;
    }

    /**
     * 构建空结果
     *
     * @param importBatchNo 导入批次号
     * @return 导入结果
     */
    private FlightUavBmImportResultDTO buildEmptyResult(String importBatchNo) {
        FlightUavBmImportResultDTO resultDTO = new FlightUavBmImportResultDTO();
        resultDTO.setImportBatchNo(importBatchNo);
        resultDTO.setImportStatus(2); // 失败
        resultDTO.setTotalCount(0);
        resultDTO.setSuccessCount(0);
        resultDTO.setFailCount(0);
        resultDTO.setFailReason("导入文件为空");
        resultDTO.setErrorList(new ArrayList<>());
        return resultDTO;
    }

    /**
     * 构建失败结果
     *
     * @param importBatchNo 导入批次号
     * @param errorMsg 错误信息
     * @return 导入结果
     */
    private FlightUavBmImportResultDTO buildFailResult(String importBatchNo, String errorMsg) {
        FlightUavBmImportResultDTO resultDTO = new FlightUavBmImportResultDTO();
        resultDTO.setImportBatchNo(importBatchNo);
        resultDTO.setImportStatus(2); // 失败
        resultDTO.setTotalCount(0);
        resultDTO.setSuccessCount(0);
        resultDTO.setFailCount(0);
        resultDTO.setFailReason(errorMsg);
        resultDTO.setErrorList(new ArrayList<>());
        return resultDTO;
    }

    /**
     * 从错误消息中提取字段信息
     *
     * @param errorMsg 错误消息
     * @return 字段名
     */
    private String extractFieldName(String errorMsg) {
        String[] commonFields = {"厂家/品牌", "机型", "最长飞行时间", "最大飞行半径", "最大续航里程", "最大载物重量", "最大飞行高度", "视频距离", "相机/像素"};
        
        for (String field : commonFields) {
            if (errorMsg.contains(field)) {
                return field;
            }
        }
        return null;
    }

    /**
     * 从错误消息中提取字段值
     *
     * @param rowData 行数据
     * @param fieldName 字段名
     * @return 字段值
     */
    private String extractFieldValue(Map<Integer, String> rowData, String fieldName) {
        String value = rowData.get(fieldName);
        return value == null ? "" : value.trim();
    }

    /**
     * 更新导入记录
     *
     * @param recordDTO 导入记录DTO
     */
    private void updateImportRecord(FlightUavBmImportRecordDTO recordDTO) {
        try {
            FlightUavBmImportRecordDO recordDO = new FlightUavBmImportRecordDO();
            recordDO.setImportBatchNo(recordDTO.getImportBatchNo());
            recordDO.setTotalCount(recordDTO.getTotalCount());
            recordDO.setSuccessCount(recordDTO.getSuccessCount());
            recordDO.setFailCount(recordDTO.getFailCount());
            recordDO.setImportStatus(recordDTO.getImportStatus());
            recordDO.setFailReason(recordDTO.getFailReason());
            recordDO.setGmtModified(LocalDateTime.now());
            
            importRecordRepository.updateById(recordDO);
        } catch (Exception e) {
            log.error("更新导入记录失败", e);
        }
    }

    /**
     * 将载人类型描述转换为枚举
     *
     * @param crewTypeDesc 载人类型描述
     * @return 载人类型枚举
     */
    private FlightUavCrewTypeEnum convertToCrewTypeEnum(String crewTypeDesc) {
        if (StringUtils.isBlank(crewTypeDesc)) {
            return null;
        }
        
        for (FlightUavCrewTypeEnum enumValue : FlightUavCrewTypeEnum.values()) {
            if (enumValue.getDesc().equals(crewTypeDesc)) {
                return enumValue;
            }
        }
        
        // 如果无法匹配描述，尝试匹配编码
        return FlightUavCrewTypeEnum.getByCode(crewTypeDesc);
    }
    
    /**
     * 将重量分类描述转换为枚举
     *
     * @param weightClassDesc 重量分类描述
     * @return 重量分类枚举
     */
    private FlightUavWeightClassificationEnum convertToWeightClassificationEnum(String weightClassDesc) {
        if (StringUtils.isBlank(weightClassDesc)) {
            return null;
        }
        
        for (FlightUavWeightClassificationEnum enumValue : FlightUavWeightClassificationEnum.values()) {
            if (enumValue.getDesc().equals(weightClassDesc)) {
                return enumValue;
            }
        }
        
        // 如果无法匹配描述，尝试匹配编码
        return FlightUavWeightClassificationEnum.getByCode(weightClassDesc);
    }
    
    /**
     * 将飞行方式描述转换为枚举
     *
     * @param flyTypeDesc 飞行方式描述
     * @return 飞行方式枚举
     */
    private FlightUavFlyTypeEnum convertToFlyTypeEnum(String flyTypeDesc) {
        if (StringUtils.isBlank(flyTypeDesc)) {
            return null;
        }
        
        for (FlightUavFlyTypeEnum enumValue : FlightUavFlyTypeEnum.values()) {
            if (enumValue.getDesc().equals(flyTypeDesc)) {
                return enumValue;
            }
        }
        
        // 如果无法匹配描述，尝试匹配类型编码
        for (FlightUavFlyTypeEnum enumValue : FlightUavFlyTypeEnum.values()) {
            if (enumValue.getType().equals(flyTypeDesc)) {
                return enumValue;
            }
        }
        
        return null;
    }
} 