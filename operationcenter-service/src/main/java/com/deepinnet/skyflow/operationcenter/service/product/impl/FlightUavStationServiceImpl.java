package com.deepinnet.skyflow.operationcenter.service.product.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavStationDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightUavStationRepository;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavStationDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavStationQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.StationDistanceQueryDTO;
import com.deepinnet.skyflow.operationcenter.enums.BizTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavStationService;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavService;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavBmService;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavStationConvert;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavConvert;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavBmConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.deepinnet.skyflow.operationcenter.vo.StationDistanceVO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmDTO;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavBmVO;
import com.deepinnet.tenant.TenantIdUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 飞行无人机站点服务实现类
 *
 * <AUTHOR>
 */
@Service
public class FlightUavStationServiceImpl implements FlightUavStationService {

    private static final int SRID = 4326; // WGS84坐标系SRID

    @Resource
    private FlightUavStationRepository flightUavStationRepository;

    @Resource
    private FlightUavStationConvert flightUavStationConvert;

    @Resource
    private FlightUavService flightUavService;

    @Resource
    private FlightUavBmService flightUavBmService;

    @Resource
    private FlightUavConvert flightUavConvert;

    @Resource
    private FlightUavBmConvert flightUavBmConvert;

    // 地球半径（米）
    private static final double EARTH_RADIUS = 6371000.0;

    @Override
    public String saveFlightUavStation(FlightUavStationDTO flightUavStationDTO) {
        // 参数校验
        validateFlightUavStationParams(flightUavStationDTO);

        // 生成机巢编号
        String flightStationNo = IdGenerateUtil.getId();
        flightUavStationDTO.setFlightStationNo(flightStationNo);

        FlightUavStationDO flightUavStationDO = flightUavStationConvert.convertToDO(flightUavStationDTO);
        flightUavStationDO.setGmtCreated(LocalDateTime.now());
        flightUavStationDO.setGmtModified(LocalDateTime.now());
        flightUavStationDO.setId(null);

        boolean success = flightUavStationRepository.save(flightUavStationDO);
        if (!success) {
            LogUtil.error("保存飞行无人机站点失败: {}", flightUavStationDTO.getFlightStationNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("保存飞行无人机站点成功: {}", flightUavStationDO.getId());
        return flightUavStationDO.getFlightStationNo();
    }

    @Override
    public boolean updateFlightUavStation(FlightUavStationDTO flightUavStationDTO) {
        if (flightUavStationDTO.getId() == null) {
            LogUtil.error("更新飞行无人机站点失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 参数校验
        validateFlightUavStationParams(flightUavStationDTO);

        // 检查飞行无人机站点是否存在
        FlightUavStationDO existFlightUavStation = flightUavStationRepository.getById(flightUavStationDTO.getId());
        if (existFlightUavStation == null) {
            LogUtil.error("更新飞行无人机站点失败，站点不存在: {}", flightUavStationDTO.getId());
            throw new BizException(BizErrorCode.FLIGHT_STATION_NOT_FOUND.getCode(), BizErrorCode.FLIGHT_STATION_NOT_FOUND.getDesc());
        }

        FlightUavStationDO flightUavStationDO = flightUavStationConvert.convertToDO(flightUavStationDTO);
        flightUavStationDO.setGmtModified(LocalDateTime.now());

        // 使用自定义方法更新所有字段，包括空值
        boolean success = flightUavStationRepository.updateAllColumnById(flightUavStationDO);
        if (!success) {
            LogUtil.error("更新飞行无人机站点失败: {}", flightUavStationDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("更新飞行无人机站点成功: {}", flightUavStationDO.getId());
        return true;
    }

    @Override
    public FlightUavStationDTO getFlightUavStationById(Integer id) {
        if (id == null) {
            LogUtil.error("获取飞行无人机站点失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightUavStationDO flightUavStationDO = flightUavStationRepository.getById(id);
        if (flightUavStationDO == null) {
            LogUtil.error("获取飞行无人机站点失败，站点不存在: {}", id);
            throw new BizException(BizErrorCode.FLIGHT_STATION_NOT_FOUND.getCode(), BizErrorCode.FLIGHT_STATION_NOT_FOUND.getDesc());
        }

        FlightUavStationDTO flightUavStationDTO = flightUavStationConvert.convert(flightUavStationDO);
        
        // 添加飞行器信息和机型信息
        if (flightUavStationDTO != null) {
            List<FlightUavStationDTO> dtoList = new ArrayList<>();
            dtoList.add(flightUavStationDTO);
            enrichWithFlightUavAndBmInfo(dtoList);
            flightUavStationDTO = dtoList.get(0);
        }

        return flightUavStationDTO;
    }

    @Override
    public FlightUavStationDTO getFlightUavStationByNo(String flightStationNo) {
        if (StringUtils.isBlank(flightStationNo)) {
            LogUtil.error("获取飞行无人机站点失败，机巢编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightUavStationDO flightUavStationDO = flightUavStationRepository.getOne(
                Wrappers.lambdaQuery(FlightUavStationDO.class)
                        .eq(FlightUavStationDO::getFlightStationNo, flightStationNo)
        );
        if (flightUavStationDO == null) {
            LogUtil.error("获取飞行无人机站点失败，站点不存在: {}", flightStationNo);
            throw new BizException(BizErrorCode.FLIGHT_STATION_NOT_FOUND.getCode(), BizErrorCode.FLIGHT_STATION_NOT_FOUND.getDesc());
        }

        FlightUavStationDTO flightUavStationDTO = flightUavStationConvert.convert(flightUavStationDO);
        
        // 添加飞行器信息和机型信息
        if (flightUavStationDTO != null) {
            List<FlightUavStationDTO> dtoList = new ArrayList<>();
            dtoList.add(flightUavStationDTO);
            enrichWithFlightUavAndBmInfo(dtoList);
            flightUavStationDTO = dtoList.get(0);
        }

        return flightUavStationDTO;
    }

    @Override
    public CommonPage<FlightUavStationDTO> pageQueryFlightUavStation(FlightUavStationQueryDTO queryDTO) {
        // 如果不需要按飞行器型号过滤，使用普通查询
        if (CollectionUtils.isEmpty(queryDTO.getFlightUavBmList())
                && StringUtils.isBlank(queryDTO.getFlightUavSn())
                && queryDTO.getFlightUavFlyType() == null
                && StringUtils.isBlank(queryDTO.getFlightUavBmModelNo())) {
            return pageQueryFlightUavStationWithoutBmFilter(queryDTO);
        }

        // 需要按飞行器型号过滤，使用关联查询
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        List<FlightUavStationDO> flightUavStationDOList = flightUavStationRepository.queryStationsByFlightUavBm(
                queryDTO.getFlightStationNo(),
                queryDTO.getCompanyUserNo(),
                queryDTO.getTenantId(),
                queryDTO.getFlightUavNo(),
                queryDTO.getStatus() != null ? queryDTO.getStatus().getStatus() : null,
                queryDTO.getMinRadius(),
                queryDTO.getMaxRadius(),
                queryDTO.getFlightUavBmList(),
                queryDTO.getFlightUavSn(),
                queryDTO.getName(),
                queryDTO.getProvinceCode(),
                queryDTO.getCityCode(),
                queryDTO.getDistrictCode(),
                queryDTO.getFlightUavFlyType() != null ? queryDTO.getFlightUavFlyType().getType() : null,
                queryDTO.getFlightUavBmModelNo()
        );

        if (CollectionUtils.isEmpty(flightUavStationDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<FlightUavStationDTO> flightUavStationDTOList = flightUavStationConvert.convertList(flightUavStationDOList);

        // 添加飞行器信息和机型信息
        enrichWithFlightUavAndBmInfo(flightUavStationDTOList);

        PageInfo<FlightUavStationDTO> pageInfo = new PageInfo<>(flightUavStationDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), flightUavStationDTOList);
    }

    /**
     * 不需要按飞行器型号过滤的查询
     */
    private CommonPage<FlightUavStationDTO> pageQueryFlightUavStationWithoutBmFilter(FlightUavStationQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<FlightUavStationDO> queryWrapper = Wrappers.lambdaQuery(FlightUavStationDO.class)
                .eq(StringUtils.isNotBlank(queryDTO.getFlightStationNo()), FlightUavStationDO::getFlightStationNo, queryDTO.getFlightStationNo())
                .eq(StringUtils.isNotBlank(queryDTO.getCompanyUserNo()), FlightUavStationDO::getCompanyUserNo, queryDTO.getCompanyUserNo())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), FlightUavStationDO::getTenantId, queryDTO.getTenantId())
                .eq(StringUtils.isNotBlank(queryDTO.getFlightUavNo()), FlightUavStationDO::getFlightUavNo, queryDTO.getFlightUavNo())
                .like(StringUtils.isNotBlank(queryDTO.getName()), FlightUavStationDO::getName, queryDTO.getName())
                .eq(StringUtils.isNotBlank(queryDTO.getProvinceCode()), FlightUavStationDO::getProvinceCode, queryDTO.getProvinceCode())
                .eq(StringUtils.isNotBlank(queryDTO.getCityCode()), FlightUavStationDO::getCityCode, queryDTO.getCityCode())
                .eq(StringUtils.isNotBlank(queryDTO.getDistrictCode()), FlightUavStationDO::getDistrictCode, queryDTO.getDistrictCode())
                .eq(queryDTO.getStatus() != null, FlightUavStationDO::getStatus,
                        queryDTO.getStatus() != null ? queryDTO.getStatus().getStatus() : null)
                .ge(queryDTO.getMinRadius() != null, FlightUavStationDO::getRadius, queryDTO.getMinRadius())
                .le(queryDTO.getMaxRadius() != null, FlightUavStationDO::getRadius, queryDTO.getMaxRadius())
                .orderByDesc(FlightUavStationDO::getGmtCreated);

        List<FlightUavStationDO> flightUavStationDOList = flightUavStationRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(flightUavStationDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<FlightUavStationDTO> flightUavStationDTOList = flightUavStationConvert.convertList(flightUavStationDOList);

        // 添加飞行器信息和机型信息
        enrichWithFlightUavAndBmInfo(flightUavStationDTOList);

        PageInfo<FlightUavStationDTO> pageInfo = new PageInfo<>(flightUavStationDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), flightUavStationDTOList);
    }

    /**
     * 添加飞行器信息和机型信息
     *
     * @param flightUavStationDTOList 机巢列表
     */
    private void enrichWithFlightUavAndBmInfo(List<FlightUavStationDTO> flightUavStationDTOList) {
        if (CollectionUtils.isEmpty(flightUavStationDTOList)) {
            return;
        }

        // 收集所有关联的飞行器编号
        List<String> flightUavNos = flightUavStationDTOList.stream()
                .map(FlightUavStationDTO::getFlightUavNo)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(flightUavNos)) {
            return;
        }

        // 获取所有飞行器信息
        Map<String, FlightUavDTO> flightUavMap = new HashMap<>();
        Map<String, FlightUavBmDTO> flightUavBmMap = new HashMap<>();

        for (String flightUavNo : flightUavNos) {
            FlightUavDTO flightUavDTO = flightUavService.getFlightUavByNo(flightUavNo);
            if (flightUavDTO != null) {
                flightUavMap.put(flightUavNo, flightUavDTO);

                // 获取机型信息
                if (StringUtils.isNotBlank(flightUavDTO.getFlightUavBmNo())) {
                    FlightUavBmDTO flightUavBmDTO = flightUavBmService.getFlightUavBmByNo(flightUavDTO.getFlightUavBmNo());
                    if (flightUavBmDTO != null) {
                        flightUavBmMap.put(flightUavDTO.getFlightUavBmNo(), flightUavBmDTO);
                    }
                }
            }
        }

        // 设置飞行器和机型信息
        for (FlightUavStationDTO stationDTO : flightUavStationDTOList) {
            if (StringUtils.isNotBlank(stationDTO.getFlightUavNo())) {
                FlightUavDTO flightUavDTO = flightUavMap.get(stationDTO.getFlightUavNo());
                if (flightUavDTO != null) {
                    FlightUavVO flightUavVO = flightUavConvert.convertToVO(flightUavDTO);
                    stationDTO.setFlightUav(flightUavVO);

                    // 设置机型信息
                    if (StringUtils.isNotBlank(flightUavDTO.getFlightUavBmNo())) {
                        FlightUavBmDTO flightUavBmDTO = flightUavBmMap.get(flightUavDTO.getFlightUavBmNo());
                        if (flightUavBmDTO != null) {
                            FlightUavBmVO flightUavBmVO = flightUavBmConvert.convertToVO(flightUavBmDTO);
                            stationDTO.setFlightUavBm(flightUavBmVO);
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<FlightUavStationDTO> getFlightUavStationsByCompanyUserNo(String companyUserNo) {
        if (StringUtils.isBlank(companyUserNo)) {
            LogUtil.error("获取服务商站点失败，服务商编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        List<FlightUavStationDO> flightUavStationDOList = flightUavStationRepository.list(
                Wrappers.lambdaQuery(FlightUavStationDO.class)
                        .eq(FlightUavStationDO::getCompanyUserNo, companyUserNo)
                        .orderByDesc(FlightUavStationDO::getGmtCreated)
        );

        List<FlightUavStationDTO> flightUavStationDTOList = flightUavStationConvert.convertList(flightUavStationDOList);
        
        // 添加飞行器信息和机型信息
        if (!CollectionUtils.isEmpty(flightUavStationDTOList)) {
            enrichWithFlightUavAndBmInfo(flightUavStationDTOList);
        }

        return flightUavStationDTOList;
    }

    @Override
    public boolean deleteFlightUavStation(Integer id) {
        if (id == null) {
            LogUtil.error("删除飞行无人机站点失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查站点是否存在
        FlightUavStationDO existFlightUavStation = flightUavStationRepository.getById(id);
        if (existFlightUavStation == null) {
            LogUtil.error("删除飞行无人机站点失败，站点不存在: {}", id);
            throw new BizException(BizErrorCode.FLIGHT_STATION_NOT_FOUND.getCode(), BizErrorCode.FLIGHT_STATION_NOT_FOUND.getDesc());
        }

        boolean success = flightUavStationRepository.removeById(id);
        if (!success) {
            LogUtil.error("删除飞行无人机站点失败: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("删除飞行无人机站点成功: {}", id);
        return true;
    }

    @Override
    public CommonPage<StationDistanceVO> pageQueryStationsByPolygon(StationDistanceQueryDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());

        // 如果需要按机型型号过滤，但不包含多边形，使用机型过滤查询
        if (StringUtils.isNotBlank(queryDTO.getFlightUavBmModelNo()) && StringUtils.isBlank(queryDTO.getPolygonWkt())) {
            Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
            return pageQueryStationsWithBmModelFilter(queryDTO, page);
        }
        
        // 如果有飞行器型号列表筛选条件，但不包含机型型号过滤，且不包含多边形，使用机型型号列表过滤查询
        if (!CollectionUtils.isEmpty(queryDTO.getFlightUavBmList()) && 
            StringUtils.isBlank(queryDTO.getFlightUavBmModelNo()) && 
            StringUtils.isBlank(queryDTO.getPolygonWkt())) {
            Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
            return pageQueryStationsWithBmListFilter(queryDTO, page);
        }

        // 如果polygonWkt为空，使用普通查询
        if (StringUtils.isBlank(queryDTO.getPolygonWkt())) {
            return pageQueryStationsWithoutPolygon(queryDTO);
        }

        try {
            // 验证WKT格式
            WKTReader reader = new WKTReader(new GeometryFactory(new PrecisionModel(), SRID));
            reader.read(queryDTO.getPolygonWkt());

            // 如果需要按机型型号过滤且包含多边形，使用组合查询
            if (StringUtils.isNotBlank(queryDTO.getFlightUavBmModelNo())) {
                return pageQueryStationsWithPolygonAndBmModel(queryDTO);
            }
            
            // 如果有飞行器型号列表筛选条件，使用多边形和型号列表查询
            if (!CollectionUtils.isEmpty(queryDTO.getFlightUavBmList())) {
                // 使用PostgreSQL的空间能力查询数据，并加入型号列表过滤
                List<FlightUavStationDO> stations = flightUavStationRepository.findStationsInPolygonOrderByDistance(
                        queryDTO.getPolygonWkt(),
                        queryDTO.getTenantId(),
                        queryDTO.getStationStatus(),
                        queryDTO.getFlightUavBmList(),
                        queryDTO.getPageNum(),
                        queryDTO.getPageSize()
                );

                // 获取总数
                long total = flightUavStationRepository.countStationsInPolygon(
                        queryDTO.getPolygonWkt(),
                        queryDTO.getTenantId(),
                        queryDTO.getStationStatus(),
                        queryDTO.getFlightUavBmList()
                );

                // 转换为VO
                List<StationDistanceVO> voList = convertToStationDistanceVOList(stations);

                // 计算总页数
                int pages = (int) ((total + queryDTO.getPageSize() - 1) / queryDTO.getPageSize());

                return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pages, total, voList);
            }

            // 使用PostgreSQL的空间能力查询数据
            List<FlightUavStationDO> stations = flightUavStationRepository.findStationsInPolygonOrderByDistance(
                    queryDTO.getPolygonWkt(),
                    queryDTO.getTenantId(),
                    queryDTO.getStationStatus(),
                    null,
                    queryDTO.getPageNum(),
                    queryDTO.getPageSize()
            );

            // 获取总数
            long total = flightUavStationRepository.countStationsInPolygon(
                    queryDTO.getPolygonWkt(),
                    queryDTO.getTenantId(),
                    queryDTO.getStationStatus(),
                    null
            );

            // 转换为VO
            List<StationDistanceVO> voList = convertToStationDistanceVOList(stations);

            // 计算总页数
            int pages = (int) ((total + queryDTO.getPageSize() - 1) / queryDTO.getPageSize());

            return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pages, total, voList);

        } catch (ParseException e) {
            LogUtil.error("解析WKT字符串失败: {}", e.getMessage());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "无效的WKT格式");
        }
    }

    /**
     * 使用多边形和机型型号过滤条件查询站点
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    private CommonPage<StationDistanceVO> pageQueryStationsWithPolygonAndBmModel(StationDistanceQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 先查询在多边形内的站点
        List<FlightUavStationDO> stationsInPolygon = flightUavStationRepository.findStationsInPolygonOrderByDistance(
                queryDTO.getPolygonWkt(),
                queryDTO.getTenantId(),
                queryDTO.getStationStatus(),
                queryDTO.getFlightUavBmList(),
                1, // 设置大页码，获取所有站点
                Integer.MAX_VALUE
        );

        if (CollectionUtils.isEmpty(stationsInPolygon)) {
            return CommonPage.buildEmptyPage();
        }

        // 提取多边形内站点的ID列表
        List<Integer> stationIds = stationsInPolygon.stream()
                .map(FlightUavStationDO::getId)
                .collect(Collectors.toList());

        // 构建 EXISTS 子查询，实现连接 flight_uav_station, flight_uav 和 flight_uav_bm 表的效果
        LambdaQueryWrapper<FlightUavStationDO> queryWrapper = Wrappers.lambdaQuery(FlightUavStationDO.class)
                .in(FlightUavStationDO::getId, stationIds)
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), FlightUavStationDO::getTenantId, queryDTO.getTenantId())
                .eq(StringUtils.isNotBlank(queryDTO.getStationStatus()), FlightUavStationDO::getStatus, queryDTO.getStationStatus())
                .eq(StringUtils.isNotBlank(queryDTO.getProvinceCode()), FlightUavStationDO::getProvinceCode, queryDTO.getProvinceCode())
                .eq(StringUtils.isNotBlank(queryDTO.getCityCode()), FlightUavStationDO::getCityCode, queryDTO.getCityCode())
                .eq(StringUtils.isNotBlank(queryDTO.getDistrictCode()), FlightUavStationDO::getDistrictCode, queryDTO.getDistrictCode());

        // 添加 flight_uav_bm_model_no 过滤条件
        StringBuilder existsClause = new StringBuilder();
        existsClause.append("SELECT 1 FROM flight_uav f ");
        existsClause.append("JOIN flight_uav_bm bm ON f.flight_uav_bm_no = bm.flight_uav_bm_no ");
        existsClause.append("WHERE f.flight_uav_no = flight_uav_station.flight_uav_no ");
        existsClause.append("AND bm.flight_uav_bm_model_no LIKE '%").append(queryDTO.getFlightUavBmModelNo()).append("%' ");
        
        // 添加型号编号列表过滤条件
        if (!CollectionUtils.isEmpty(queryDTO.getFlightUavBmList())) {
            existsClause.append("AND f.flight_uav_bm_no IN (");
            existsClause.append(queryDTO.getFlightUavBmList().stream()
                    .map(s -> "'" + s + "'")
                    .collect(Collectors.joining(",")));
            existsClause.append(") ");
        }

        queryWrapper.exists(existsClause.toString());

        // 按原始多边形查询的距离排序
        Map<Integer, Double> distanceMap = new HashMap<>();
        for (FlightUavStationDO station : stationsInPolygon) {
            distanceMap.put(station.getId(), station.getDistanceMeters());
        }

        List<FlightUavStationDO> filteredStations = flightUavStationRepository.list(queryWrapper);

        if (CollectionUtils.isEmpty(filteredStations)) {
            return CommonPage.buildEmptyPage();
        }

        // 设置距离字段
        for (FlightUavStationDO station : filteredStations) {
            station.setDistanceMeters(distanceMap.getOrDefault(station.getId(), 0.0));
        }

        // 按距离排序
        filteredStations.sort(Comparator.comparing(FlightUavStationDO::getDistanceMeters));

        // 执行分页
        int startIndex = (queryDTO.getPageNum() - 1) * queryDTO.getPageSize();
        int endIndex = Math.min(startIndex + queryDTO.getPageSize(), filteredStations.size());
        if (startIndex >= filteredStations.size()) {
            return CommonPage.buildEmptyPage();
        }

        List<FlightUavStationDO> pagedStations = filteredStations.subList(startIndex, endIndex);

        // 转换为VO
        List<StationDistanceVO> voList = convertToStationDistanceVOList(pagedStations);

        // 计算总页数
        int total = filteredStations.size();
        int pages = (int) ((total + queryDTO.getPageSize() - 1) / queryDTO.getPageSize());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pages, (long) total, voList);
    }

    /**
     * 不使用多边形时查询站点
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    private CommonPage<StationDistanceVO> pageQueryStationsWithoutPolygon(StationDistanceQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 检查是否需要联表查询
        if (StringUtils.isNotBlank(queryDTO.getFlightUavBmModelNo())) {
            // 需要联表查询 flight_uav_bm 表
            return pageQueryStationsWithBmModelFilter(queryDTO, page);
        }
        
        // 检查是否需要根据型号列表过滤
        if (!CollectionUtils.isEmpty(queryDTO.getFlightUavBmList())) {
            // 使用飞行器型号列表过滤
            return pageQueryStationsWithBmListFilter(queryDTO, page);
        }

        // 不需要联表查询，使用普通查询
        LambdaQueryWrapper<FlightUavStationDO> queryWrapper = Wrappers.lambdaQuery(FlightUavStationDO.class)
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), FlightUavStationDO::getTenantId, queryDTO.getTenantId())
                .eq(StringUtils.isNotBlank(queryDTO.getStationStatus()), FlightUavStationDO::getStatus, queryDTO.getStationStatus())
                .eq(StringUtils.isNotBlank(queryDTO.getProvinceCode()), FlightUavStationDO::getProvinceCode, queryDTO.getProvinceCode())
                .eq(StringUtils.isNotBlank(queryDTO.getCityCode()), FlightUavStationDO::getCityCode, queryDTO.getCityCode())
                .eq(StringUtils.isNotBlank(queryDTO.getDistrictCode()), FlightUavStationDO::getDistrictCode, queryDTO.getDistrictCode())
                .orderByDesc(FlightUavStationDO::getGmtCreated);

        List<FlightUavStationDO> stations = flightUavStationRepository.list(queryWrapper);

        if (CollectionUtils.isEmpty(stations)) {
            return CommonPage.buildEmptyPage();
        }

        // 转换为VO
        List<StationDistanceVO> voList = convertToStationDistanceVOList(stations);

        // 设置分页信息
        PageInfo<FlightUavStationDO> pageInfo = new PageInfo<>(stations);
        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), voList);
    }

    /**
     * 使用飞行器型号列表过滤条件查询站点
     *
     * @param queryDTO 查询条件
     * @param page     分页对象
     * @return 分页查询结果
     */
    private CommonPage<StationDistanceVO> pageQueryStationsWithBmListFilter(StationDistanceQueryDTO queryDTO, Page<Object> page) {
        // 构建 EXISTS 子查询，实现连接 flight_uav_station 和 flight_uav 表的效果
        LambdaQueryWrapper<FlightUavStationDO> queryWrapper = Wrappers.lambdaQuery(FlightUavStationDO.class)
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), FlightUavStationDO::getTenantId, queryDTO.getTenantId())
                .eq(StringUtils.isNotBlank(queryDTO.getStationStatus()), FlightUavStationDO::getStatus, queryDTO.getStationStatus())
                .eq(StringUtils.isNotBlank(queryDTO.getProvinceCode()), FlightUavStationDO::getProvinceCode, queryDTO.getProvinceCode())
                .eq(StringUtils.isNotBlank(queryDTO.getCityCode()), FlightUavStationDO::getCityCode, queryDTO.getCityCode())
                .eq(StringUtils.isNotBlank(queryDTO.getDistrictCode()), FlightUavStationDO::getDistrictCode, queryDTO.getDistrictCode());

        // 添加 flight_uav_bm_no 列表过滤条件
        StringBuilder existsClause = new StringBuilder();
        existsClause.append("SELECT 1 FROM flight_uav f ");
        existsClause.append("WHERE f.flight_uav_no = flight_uav_station.flight_uav_no ");
        
        if (!CollectionUtils.isEmpty(queryDTO.getFlightUavBmList())) {
            existsClause.append("AND f.flight_uav_bm_no IN (");
            existsClause.append(queryDTO.getFlightUavBmList().stream()
                    .map(s -> "'" + s + "'")
                    .collect(Collectors.joining(",")));
            existsClause.append(") ");
        }

        queryWrapper.exists(existsClause.toString());
        queryWrapper.orderByDesc(FlightUavStationDO::getGmtCreated);

        List<FlightUavStationDO> stations = flightUavStationRepository.list(queryWrapper);

        if (CollectionUtils.isEmpty(stations)) {
            return CommonPage.buildEmptyPage();
        }

        // 转换为VO
        List<StationDistanceVO> voList = convertToStationDistanceVOList(stations);

        // 设置分页信息
        PageInfo<FlightUavStationDO> pageInfo = new PageInfo<>(stations);
        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), voList);
    }

    /**
     * 使用飞行器型号过滤条件查询站点
     *
     * @param queryDTO 查询条件
     * @param page     分页对象
     * @return 分页查询结果
     */
    private CommonPage<StationDistanceVO> pageQueryStationsWithBmModelFilter(StationDistanceQueryDTO queryDTO, Page<Object> page) {
        // 构建 EXISTS 子查询，实现连接 flight_uav_station, flight_uav 和 flight_uav_bm 表的效果
        LambdaQueryWrapper<FlightUavStationDO> queryWrapper = Wrappers.lambdaQuery(FlightUavStationDO.class)
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), FlightUavStationDO::getTenantId, queryDTO.getTenantId())
                .eq(StringUtils.isNotBlank(queryDTO.getStationStatus()), FlightUavStationDO::getStatus, queryDTO.getStationStatus())
                .eq(StringUtils.isNotBlank(queryDTO.getProvinceCode()), FlightUavStationDO::getProvinceCode, queryDTO.getProvinceCode())
                .eq(StringUtils.isNotBlank(queryDTO.getCityCode()), FlightUavStationDO::getCityCode, queryDTO.getCityCode())
                .eq(StringUtils.isNotBlank(queryDTO.getDistrictCode()), FlightUavStationDO::getDistrictCode, queryDTO.getDistrictCode());

        // 添加 flight_uav_bm_model_no 过滤条件
        StringBuilder existsClause = new StringBuilder();
        existsClause.append("SELECT 1 FROM flight_uav f ");
        existsClause.append("JOIN flight_uav_bm bm ON f.flight_uav_bm_no = bm.flight_uav_bm_no ");
        existsClause.append("WHERE f.flight_uav_no = flight_uav_station.flight_uav_no ");

        if (StringUtils.isNotBlank(queryDTO.getFlightUavBmModelNo())) {
            existsClause.append("AND bm.flight_uav_bm_model_no LIKE '%").append(queryDTO.getFlightUavBmModelNo()).append("%' ");
        }
        
        // 添加型号编号列表过滤条件
        if (!CollectionUtils.isEmpty(queryDTO.getFlightUavBmList())) {
            existsClause.append("AND f.flight_uav_bm_no IN (");
            existsClause.append(queryDTO.getFlightUavBmList().stream()
                    .map(s -> "'" + s + "'")
                    .collect(Collectors.joining(",")));
            existsClause.append(") ");
        }

        queryWrapper.exists(existsClause.toString());
        queryWrapper.orderByDesc(FlightUavStationDO::getGmtCreated);

        List<FlightUavStationDO> stations = flightUavStationRepository.list(queryWrapper);

        if (CollectionUtils.isEmpty(stations)) {
            return CommonPage.buildEmptyPage();
        }

        // 转换为VO
        List<StationDistanceVO> voList = convertToStationDistanceVOList(stations);

        // 设置分页信息
        PageInfo<FlightUavStationDO> pageInfo = new PageInfo<>(stations);
        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), voList);
    }

    /**
     * 转换站点DO到StationDistanceVO
     *
     * @param stations 站点DO列表
     * @return VO列表
     */
    private List<StationDistanceVO> convertToStationDistanceVOList(List<FlightUavStationDO> stations) {
        if (CollectionUtils.isEmpty(stations)) {
            return Collections.emptyList();
        }

        List<StationDistanceVO> voList = new ArrayList<>();
        
        // 收集所有关联的飞行器编号
        List<String> flightUavNos = stations.stream()
                .map(FlightUavStationDO::getFlightUavNo)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        
        // 获取飞行器信息和机型信息的Map
        Map<String, FlightUavDTO> flightUavMap = new HashMap<>();
        Map<String, FlightUavBmDTO> flightUavBmMap = new HashMap<>();
        
        if (!CollectionUtils.isEmpty(flightUavNos)) {
            // 获取所有相关的飞行器信息
            for (String flightUavNo : flightUavNos) {
                FlightUavDTO flightUavDTO = flightUavService.getFlightUavByNo(flightUavNo);
                if (flightUavDTO != null) {
                    flightUavMap.put(flightUavNo, flightUavDTO);
                    
                    // 获取机型信息
                    if (StringUtils.isNotBlank(flightUavDTO.getFlightUavBmNo())) {
                        FlightUavBmDTO flightUavBmDTO = flightUavBmService.getFlightUavBmByNo(flightUavDTO.getFlightUavBmNo());
                        if (flightUavBmDTO != null) {
                            flightUavBmMap.put(flightUavDTO.getFlightUavBmNo(), flightUavBmDTO);
                        }
                    }
                }
            }
        }
        
        for (FlightUavStationDO station : stations) {
            StationDistanceVO vo = new StationDistanceVO();
            vo.setId(station.getId());
            vo.setStationNo(station.getFlightStationNo());
            vo.setStationName(station.getName());
            vo.setStationStatus(station.getStatus());
            
            // 设置省市区编码
            vo.setProvinceCode(station.getProvinceCode());
            vo.setCityCode(station.getCityCode());
            vo.setDistrictCode(station.getDistrictCode());
            
            if (station.getCoordinate() != null) {
                vo.setLatitude(new BigDecimal(station.getCoordinate().getY()));
                vo.setLongitude(new BigDecimal(station.getCoordinate().getX()));
            }
            
            vo.setStationAddress(String.format("%s%s%s%s", 
                station.getProvince() != null ? station.getProvince() : "", 
                station.getCity() != null ? station.getCity() : "", 
                station.getDistrict() != null ? station.getDistrict() : "", 
                station.getAddress() != null ? station.getAddress() : ""));
            vo.setTenantId(station.getTenantId());
            
            // 设置距离 (如果有的话)
            vo.setDistance(station.getDistanceMeters());
            
            // 设置飞行器编号
            vo.setFlightUavNo(station.getFlightUavNo());
            
            // 设置飞行器和机型信息
            if (StringUtils.isNotBlank(station.getFlightUavNo())) {
                FlightUavDTO flightUavDTO = flightUavMap.get(station.getFlightUavNo());
                if (flightUavDTO != null) {
                    // 转换为VO并设置
                    FlightUavVO flightUavVO = flightUavConvert.convertToVO(flightUavDTO);
                    vo.setFlightUav(flightUavVO);
                    
                    // 设置机型信息
                    if (StringUtils.isNotBlank(flightUavDTO.getFlightUavBmNo())) {
                        FlightUavBmDTO flightUavBmDTO = flightUavBmMap.get(flightUavDTO.getFlightUavBmNo());
                        if (flightUavBmDTO != null) {
                            FlightUavBmVO flightUavBmVO = flightUavBmConvert.convertToVO(flightUavBmDTO);
                            vo.setFlightUavBm(flightUavBmVO);
                        }
                    }
                }
            }
            
            voList.add(vo);
        }
        
        return voList;
    }

    /**
     * 校验飞行无人机站点参数
     *
     * @param flightUavStationDTO 飞行无人机站点DTO
     */
    private void validateFlightUavStationParams(FlightUavStationDTO flightUavStationDTO) {
        // NEST02: 校验机巢名称
        if (StringUtils.isBlank(flightUavStationDTO.getName())) {
            LogUtil.error("飞行无人机站点校验失败，机巢名称为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入机巢名称");
        }

        // NEST03: 校验机巢名称长度
        if (flightUavStationDTO.getName().length() > 30) {
            LogUtil.error("飞行无人机站点校验失败，机巢名称过长: {}", flightUavStationDTO.getName());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "机巢名称不能超过30字符");
        }

        // NEST04: 校验机巢名称是否在服务商内重复
        FlightUavStationDO existFlightUavStation = flightUavStationRepository.getOne(
                Wrappers.lambdaQuery(FlightUavStationDO.class)
                        .eq(FlightUavStationDO::getName, flightUavStationDTO.getName())
                        .eq(StringUtils.isNotBlank(flightUavStationDTO.getCompanyUserNo()),
                                FlightUavStationDO::getCompanyUserNo, flightUavStationDTO.getCompanyUserNo())
                        .eq(FlightUavStationDO::getIsDeleted, false)
        );

        // 如果是更新操作，需要排除自身ID
        if (existFlightUavStation != null &&
                (flightUavStationDTO.getId() == null || !flightUavStationDTO.getId().equals(existFlightUavStation.getId()))) {
            LogUtil.error("飞行无人机站点校验失败，机巢名称已存在: {}", flightUavStationDTO.getName());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "该机巢名称已存在！");
        }

        // NEST07: 校验部署地址
        if (StringUtils.isBlank(flightUavStationDTO.getAddress())) {
            LogUtil.error("飞行无人机站点校验失败，部署地址为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入部署地址");
        }

        // 校验行政区划编码
        if (StringUtils.isNotBlank(flightUavStationDTO.getProvinceCode()) && flightUavStationDTO.getProvinceCode().length() > 20) {
            LogUtil.error("飞行无人机站点校验失败，省份编码过长: {}", flightUavStationDTO.getProvinceCode());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "省份编码不能超过20字符");
        }

        if (StringUtils.isNotBlank(flightUavStationDTO.getCityCode()) && flightUavStationDTO.getCityCode().length() > 20) {
            LogUtil.error("飞行无人机站点校验失败，城市编码过长: {}", flightUavStationDTO.getCityCode());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "城市编码不能超过20字符");
        }

        if (StringUtils.isNotBlank(flightUavStationDTO.getDistrictCode()) && flightUavStationDTO.getDistrictCode().length() > 20) {
            LogUtil.error("飞行无人机站点校验失败，区县编码过长: {}", flightUavStationDTO.getDistrictCode());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "区县编码不能超过20字符");
        }

        // NEST11: 校验经纬度
        if (StringUtils.isBlank(flightUavStationDTO.getCoordinate())) {
            LogUtil.error("飞行无人机站点校验失败，经纬度为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请选择机巢部署位置");
        }

        // 尝试解析WKT字符串获取经纬度
        Double longitude = null;
        Double latitude = null;

        try {
            // WKT格式通常是 "POINT(经度 纬度)"，需要解析出经纬度值
            String wkt = flightUavStationDTO.getCoordinate();
            if (wkt.startsWith("POINT")) {
                // 提取括号内的内容
                int startIdx = wkt.indexOf('(');
                int endIdx = wkt.indexOf(')');
                if (startIdx >= 0 && endIdx > startIdx) {
                    String coordsStr = wkt.substring(startIdx + 1, endIdx).trim();
                    String[] coords = coordsStr.split("\\s+");
                    if (coords.length >= 2) {
                        longitude = Double.parseDouble(coords[0]);
                        latitude = Double.parseDouble(coords[1]);
                    }
                }
            }
        } catch (Exception e) {
            LogUtil.error("飞行无人机站点校验失败，解析坐标失败: {}", e.getMessage());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "坐标格式不正确");
        }

        // NEST12: 校验经纬度范围
        if (longitude == null || latitude == null ||
                longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90) {
            LogUtil.error("飞行无人机站点校验失败，经纬度超出范围或格式错误");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "经纬度超出有效范围或格式错误");
        }

        // NEST15: 校验最大飞行半径
        if (flightUavStationDTO.getRadius() != null && flightUavStationDTO.getRadius().compareTo(BigDecimal.ZERO) <= 0) {
            LogUtil.error("飞行无人机站点校验失败，最大飞行半径非正数: {}", flightUavStationDTO.getRadius());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入大于0的正整数");
        }

        // NEST20: 校验机巢状态
        if (flightUavStationDTO.getStatus() == null) {
            LogUtil.error("飞行无人机站点校验失败，机巢状态为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请选择机巢状态");
        }
    }
} 