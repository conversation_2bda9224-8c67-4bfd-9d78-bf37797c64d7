package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.CategoryDO;
import com.deepinnet.skyflow.operationcenter.dto.CategoryDTO;
import com.deepinnet.skyflow.operationcenter.vo.CategoryVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 类目转换接口
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface CategoryConvert {

    CategoryConvert INSTANCE = Mappers.getMapper(CategoryConvert.class);

    /**
     * DO 转 DTO
     *
     * @param categoryDO DO 对象
     * @return DTO 对象
     */
    @Mapping(source = "gmtCreated", target = "createTime")
    CategoryDTO convert(CategoryDO categoryDO);

    /**
     * DTO 转 DO
     *
     * @param categoryDTO DTO 对象
     * @return DO 对象
     */
    @Mapping(source = "createTime", target = "gmtCreated")
    @Mapping(target = "gmtModified", ignore = true)
    CategoryDO convertToDO(CategoryDTO categoryDTO);

    /**
     * DTO List 转 DO List
     *
     * @param categoryDTOList DTO 列表
     * @return DO 列表
     */
    List<CategoryDO> convertToDOList(List<CategoryDTO> categoryDTOList);

    /**
     * DO List 转 DTO List
     *
     * @param categoryDOList DO 列表
     * @return DTO 列表
     */
    List<CategoryDTO> convertList(List<CategoryDO> categoryDOList);

    /**
     * DTO 转 VO
     *
     * @param categoryDTO DTO 对象
     * @return VO 对象
     */
    CategoryVO convertToVO(CategoryDTO categoryDTO);

    /**
     * DTO列表 转 VO列表
     *
     * @param categoryDTOList DTO 列表
     * @return VO 列表
     */
    List<CategoryVO> convertToVOList(List<CategoryDTO> categoryDTOList);
} 