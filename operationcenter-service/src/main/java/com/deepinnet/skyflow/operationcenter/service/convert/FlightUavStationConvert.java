package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavStationDO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavStationDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavStationStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.infra.util.WktUtil;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavStationVO;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 飞行无人机站点转换接口
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface FlightUavStationConvert {

    FlightUavStationConvert INSTANCE = Mappers.getMapper(FlightUavStationConvert.class);
    
    /**
     * 几何工厂，用于创建Point对象
     */
    GeometryFactory GEOMETRY_FACTORY = new GeometryFactory(new PrecisionModel(), 4326);

    /**
     * DO 转 DTO
     *
     * @param flightUavStationDO DO 对象
     * @return DTO 对象
     */
    @Mapping(source = "status", target = "status", qualifiedByName = "stringToStatusEnum")
    @Mapping(source = "coordinate", target = "coordinate", qualifiedByName = "pointToString")
    FlightUavStationDTO convert(FlightUavStationDO flightUavStationDO);

    /**
     * DTO 转 DO
     *
     * @param flightUavStationDTO DTO 对象
     * @return DO 对象
     */
    @Mapping(target = "gmtModified", ignore = true)
    @Mapping(source = "status", target = "status", qualifiedByName = "statusEnumToString")
    @Mapping(source = "coordinate", target = "coordinate", qualifiedByName = "stringToPoint")
    FlightUavStationDO convertToDO(FlightUavStationDTO flightUavStationDTO);

    /**
     * DTO List 转 DO List
     *
     * @param flightUavStationDTOList DTO 列表
     * @return DO 列表
     */
    List<FlightUavStationDO> convertToDOList(List<FlightUavStationDTO> flightUavStationDTOList);

    /**
     * DO List 转 DTO List
     *
     * @param flightUavStationDOList DO 列表
     * @return DTO 列表
     */
    List<FlightUavStationDTO> convertList(List<FlightUavStationDO> flightUavStationDOList);

    /**
     * DTO 转 VO
     *
     * @param flightUavStationDTO DTO 对象
     * @return VO 对象
     */
    FlightUavStationVO convertToVO(FlightUavStationDTO flightUavStationDTO);

    /**
     * DTO列表 转 VO列表
     *
     * @param flightUavStationDTOList DTO 列表
     * @return VO 列表
     */
    List<FlightUavStationVO> convertToVOList(List<FlightUavStationDTO> flightUavStationDTOList);
    
    /**
     * 字符串转枚举
     *
     * @param status 状态字符串
     * @return 状态枚举
     */
    @Named("stringToStatusEnum")
    default FlightUavStationStatusEnum stringToStatusEnum(String status) {
        if (status == null) {
            return null;
        }
        for (FlightUavStationStatusEnum statusEnum : FlightUavStationStatusEnum.values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 枚举转字符串
     *
     * @param statusEnum 状态枚举
     * @return 状态字符串
     */
    @Named("statusEnumToString")
    default String statusEnumToString(FlightUavStationStatusEnum statusEnum) {
        return statusEnum == null ? null : statusEnum.getStatus();
    }
    
    /**
     * Point对象转换为字符串，格式："x,y"
     *
     * @param point Point对象
     * @return 格式化的坐标字符串
     */
    @Named("pointToString")
    default String pointToString(Point point) {
        if (point == null) {
            return null;
        }
        return WktUtil.toWkt(point);
    }
    
    /**
     * 字符串转换为Point对象，格式："x,y"
     *
     * @param coordinateStr 坐标字符串
     * @return Point对象
     */
    @Named("stringToPoint")
    default Point stringToPoint(String coordinateStr) {
        if (coordinateStr == null || coordinateStr.trim().isEmpty()) {
            return null;
        }
        
        return WktUtil.toPoint(coordinateStr);
    }
} 