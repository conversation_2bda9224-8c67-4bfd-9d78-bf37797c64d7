package com.deepinnet.skyflow.operationcenter.service.flow.order;

import com.deepinnet.skyflow.operationcenter.dto.FlightOrderCreateDTO;
import com.deepinnet.skyflow.operationcenter.service.flow.context.FlightOrderCreateContext;
import com.yomahub.liteflow.annotation.LiteflowCmpDefine;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import com.yomahub.liteflow.enums.NodeTypeEnum;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@LiteflowComponent(value = "flightOrderTypeSwitchNode", name = "订单类型切换节点")
@LiteflowCmpDefine(value = NodeTypeEnum.SWITCH)
public class FlightOrderTypeSwitchNode {

    @LiteflowMethod(LiteFlowMethodEnum.PROCESS_SWITCH)
    public String process(NodeComponent bindCmp) {
        FlightOrderCreateContext contextBean = bindCmp.getContextBean(FlightOrderCreateContext.class);

        FlightOrderCreateDTO flightOrderCreateDTO = contextBean.getFlightOrderCreateDTO();

        return flightOrderCreateDTO.getOrderType();
    }
}
