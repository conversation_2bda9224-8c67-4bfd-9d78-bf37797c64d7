package com.deepinnet.skyflow.operationcenter.service.order;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.OrderQueryDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandSceneEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Description:
 * Date: 2025/5/26
 * Author: lijunheng
 */
@Service
public class FlightOrderHelper {

    @Resource
    private FlightOrderService flightOrderService;

    public FlightOrderVO getFlightOrder(FlightDemandDTO demand) {
        OrderQueryDTO orderQueryDTO = new OrderQueryDTO();
        orderQueryDTO.setOrderNo(demand.getFlightOrderNo());
        orderQueryDTO.setTenantId(demand.getTenantId());
        FlightDemandSceneEnum scene = demand.getScene();
        if (scene == FlightDemandSceneEnum.ONE_NET_UNIFIED_FLY) {
            orderQueryDTO.setOrderType(OrderTypeEnum.DEMAND_PLAN.getCode());
        } else {
            orderQueryDTO.setOrderType(OrderTypeEnum.NORMAL.getCode());
        }
        return flightOrderService.getFlightOrderByNo(orderQueryDTO);
    }
}
