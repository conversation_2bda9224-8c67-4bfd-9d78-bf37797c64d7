package com.deepinnet.skyflow.operationcenter.service.convert;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/10
 */
public class TimestampConverter {

    public static LocalDateTime toLocalDateTimeShanghai(long epochMilli) {
        return Instant
                .ofEpochMilli(epochMilli)
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();
    }

}
