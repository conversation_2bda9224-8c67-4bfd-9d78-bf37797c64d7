package com.deepinnet.skyflow.operationcenter.service.demand.impl.matching;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: 动态 SQL 实体，支持拷贝和 Builder 构建
 * Date: 2025/4/22
 * Author: lijunheng
 */
@Data
public class DynamicSqlEntity {
    private String select;
    private String from;
    private List<String> joinOnList;
    private List<String> whereList;
    private String groupBy;
    private String orderBy;
    private String limit;
    
    // 添加参数列表，用于存储预处理语句的参数
    private Map<String, Object> parameters;

    public DynamicSqlEntity() {
        this.joinOnList = new ArrayList<>();
        this.whereList = new ArrayList<>();
        this.parameters = new HashMap<>();
    }

    /**
     * 拷贝构造器，用于创建拷贝对象
     */
    public DynamicSqlEntity(DynamicSqlEntity other) {
        this.select = other.select;
        this.from = other.from;
        this.joinOnList = other.joinOnList != null ? new ArrayList<>(other.joinOnList) : new ArrayList<>();
        this.whereList = other.whereList != null ? new ArrayList<>(other.whereList) : new ArrayList<>();
        this.parameters = other.parameters != null ? new HashMap<>(other.parameters) : new HashMap<>();
        this.groupBy = other.groupBy;
        this.orderBy = other.orderBy;
        this.limit = other.limit;
    }

    /**
     * 添加参数
     *
     * @param param 查询参数
     * @return this
     */
    public void addParameter(String paramName, Object param) {
        parameters.put(paramName, param);
    }

    /**
     * 获取所有参数
     *
     * @return 参数数组
     */
    public Map<String, Object> getParametersMap() {
        return parameters;
    }

    /**
     * 拼接成完整 SQL
     */
    public String buildSql() {
        StringBuilder sb = new StringBuilder();
        if (select != null) sb.append(select).append(" ");
        if (from != null) sb.append(from).append(" ");
        for (String join : joinOnList) {
            sb.append(join).append(" ");
        }
        if (!whereList.isEmpty()) {
            sb.append("WHERE ");
            sb.append(String.join(" AND ", whereList)).append(" ");
        }
        if (groupBy != null) sb.append(groupBy).append(" ");
        if (orderBy != null) sb.append(orderBy).append(" ");
        if (limit != null) sb.append(limit);
        return sb.toString().trim();
    }
}

