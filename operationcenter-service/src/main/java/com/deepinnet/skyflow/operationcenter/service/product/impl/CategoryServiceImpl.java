package com.deepinnet.skyflow.operationcenter.service.product.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.CategoryDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.CategoryRepository;
import com.deepinnet.skyflow.operationcenter.dto.CategoryDTO;
import com.deepinnet.skyflow.operationcenter.dto.CategoryQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.product.CategoryService;
import com.deepinnet.skyflow.operationcenter.service.convert.CategoryConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 类目服务实现类
 *
 * <AUTHOR>
 */
@Service
public class CategoryServiceImpl implements CategoryService {

    @Resource
    private CategoryRepository categoryRepository;

    @Resource
    private CategoryConvert categoryConvert;

    @Override
    public String saveCategory(CategoryDTO categoryDTO) {
        // 检查类目编号是否已存在
        CategoryDO existCategory = categoryRepository.getOne(
                Wrappers.lambdaQuery(CategoryDO.class)
                        .eq(CategoryDO::getCategoryNo, categoryDTO.getCategoryNo())
        );
        if (existCategory != null) {
            LogUtil.error("保存类目失败，类目编号已存在: {}", categoryDTO.getCategoryNo());
            throw new BizException(BizErrorCode.CATEGORY_ALREADY_EXISTS.getCode(), BizErrorCode.CATEGORY_ALREADY_EXISTS.getDesc());
        }

        CategoryDO categoryDO = categoryConvert.convertToDO(categoryDTO);
        categoryDO.setGmtCreated(LocalDateTime.now());
        categoryDO.setGmtModified(LocalDateTime.now());
        categoryDO.setId(null);
        
        boolean success = categoryRepository.save(categoryDO);
        if (!success) {
            LogUtil.error("保存类目失败: {}", categoryDTO.getCategoryNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        LogUtil.info("保存类目成功: {}", categoryDO.getId());
        return categoryDO.getCategoryNo();
    }

    @Override
    public boolean updateCategory(CategoryDTO categoryDTO) {
        if (categoryDTO.getId() == null) {
            LogUtil.error("更新类目失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查类目是否存在
        CategoryDO existCategory = categoryRepository.getById(categoryDTO.getId());
        if (existCategory == null) {
            LogUtil.error("更新类目失败，类目不存在: {}", categoryDTO.getId());
            throw new BizException(BizErrorCode.CATEGORY_NOT_FOUND.getCode(), BizErrorCode.CATEGORY_NOT_FOUND.getDesc());
        }

        // 如果更新了类目编号，需要检查是否与其他类目冲突
        if (!existCategory.getCategoryNo().equals(categoryDTO.getCategoryNo())) {
            CategoryDO conflictCategory = categoryRepository.getOne(
                    Wrappers.lambdaQuery(CategoryDO.class)
                            .eq(CategoryDO::getCategoryNo, categoryDTO.getCategoryNo())
                            .ne(CategoryDO::getId, categoryDTO.getId())
            );
            if (conflictCategory != null) {
                LogUtil.error("更新类目失败，类目编号已被其他类目使用: {}", categoryDTO.getCategoryNo());
                throw new BizException(BizErrorCode.CATEGORY_ALREADY_EXISTS.getCode(), BizErrorCode.CATEGORY_ALREADY_EXISTS.getDesc());
            }
        }

        CategoryDO categoryDO = categoryConvert.convertToDO(categoryDTO);
        categoryDO.setGmtModified(LocalDateTime.now());
        
        boolean success = categoryRepository.updateById(categoryDO);
        if (!success) {
            LogUtil.error("更新类目失败: {}", categoryDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        LogUtil.info("更新类目成功: {}", categoryDO.getId());
        return true;
    }

    @Override
    public CategoryDTO getCategoryById(Integer id) {
        if (id == null) {
            LogUtil.error("获取类目失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        CategoryDO categoryDO = categoryRepository.getById(id);
        if (categoryDO == null) {
            LogUtil.error("获取类目失败，类目不存在: {}", id);
            throw new BizException(BizErrorCode.CATEGORY_NOT_FOUND.getCode(), BizErrorCode.CATEGORY_NOT_FOUND.getDesc());
        }

        return categoryConvert.convert(categoryDO);
    }

    @Override
    public CategoryDTO getCategoryByNo(String categoryNo) {
        if (StringUtils.isBlank(categoryNo)) {
            LogUtil.error("获取类目失败，类目编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        CategoryDO categoryDO = categoryRepository.getOne(
                Wrappers.lambdaQuery(CategoryDO.class)
                        .eq(CategoryDO::getCategoryNo, categoryNo)
        );
        if (categoryDO == null) {
            LogUtil.error("获取类目失败，类目不存在: {}", categoryNo);
            throw new BizException(BizErrorCode.CATEGORY_NOT_FOUND.getCode(), BizErrorCode.CATEGORY_NOT_FOUND.getDesc());
        }

        return categoryConvert.convert(categoryDO);
    }

    @Override
    public CommonPage<CategoryDTO> pageQueryCategory(CategoryQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<CategoryDO> queryWrapper = Wrappers.lambdaQuery(CategoryDO.class)
                .eq(StringUtils.isNotBlank(queryDTO.getCategoryNo()), CategoryDO::getCategoryNo, queryDTO.getCategoryNo())
                .like(StringUtils.isNotBlank(queryDTO.getCategoryName()), CategoryDO::getCategoryName, queryDTO.getCategoryName())
                .eq(StringUtils.isNotBlank(queryDTO.getParentCategoryNo()), CategoryDO::getParentCategoryNo, queryDTO.getParentCategoryNo())
                .eq(queryDTO.getCategoryLevel() != null, CategoryDO::getCategoryLevel, queryDTO.getCategoryLevel())
                .eq(StringUtils.isNotBlank(queryDTO.getType()), CategoryDO::getType, queryDTO.getType())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), CategoryDO::getTenantId, queryDTO.getTenantId())
                .orderByDesc(CategoryDO::getGmtCreated);

        List<CategoryDO> categoryDOList = categoryRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(categoryDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<CategoryDTO> categoryDTOList = categoryConvert.convertList(categoryDOList);
        PageInfo<CategoryDTO> pageInfo = new PageInfo<>(categoryDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), categoryDTOList);
    }

    @Override
    public boolean deleteCategory(Integer id) {
        if (id == null) {
            LogUtil.error("删除类目失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查类目是否存在
        CategoryDO existCategory = categoryRepository.getById(id);
        if (existCategory == null) {
            LogUtil.error("删除类目失败，类目不存在: {}", id);
            throw new BizException(BizErrorCode.CATEGORY_NOT_FOUND.getCode(), BizErrorCode.CATEGORY_NOT_FOUND.getDesc());
        }

        boolean success = categoryRepository.removeById(id);
        if (!success) {
            LogUtil.error("删除类目失败: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("删除类目成功: {}", id);
        return true;
    }
} 