package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightEventsDO;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightEventStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightEventTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.infra.util.WktUtil;
import org.locationtech.jts.geom.Point;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

/**
 * 飞行事件记录转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface FlightEventsConvert {

    /**
     * 将 DO 转换为 DTO
     *
     * @param flightEventsDO DO对象
     * @return DTO对象
     */
    @Mapping(source = "eventPoint", target = "eventPoint", qualifiedByName = "pointToPointCoordinate")
    @Mapping(source = "eventType", target = "eventType", qualifiedByName = "eventTypeStringToEnum")
    @Mapping(source = "status", target = "status", qualifiedByName = "eventStatusStringToEnum")
    FlightEventsDTO convert(FlightEventsDO flightEventsDO);

    /**
     * 将 DTO 转换为 DO
     *
     * @param flightEventsDTO DTO对象
     * @return DO对象
     */
    @Mapping(source = "eventPoint", target = "eventPoint", qualifiedByName = "pointCoordinateToPoint")
    @Mapping(source = "eventType", target = "eventType", qualifiedByName = "eventTypeEnumToString")
    @Mapping(source = "status", target = "status", qualifiedByName = "eventStatusEnumToString")
    FlightEventsDO convertToDO(FlightEventsDTO flightEventsDTO);

    /**
     * 批量将 DO 转换为 DTO
     *
     * @param flightEventsDOList DO对象列表
     * @return DTO对象列表
     */
    List<FlightEventsDTO> convertList(List<FlightEventsDO> flightEventsDOList);

    @Named("pointToPointCoordinate")
    default PointCoordinate pointToPointCoordinate(Point point) {
        if (point == null) {
            return null;
        }
        return new PointCoordinate(point.getX(), point.getY());
    }

    @Named("pointCoordinateToPoint")
    default Point pointCoordinateToPoint(PointCoordinate pointCoordinate) {
        if (pointCoordinate == null) {
            return null;
        }
        return WktUtil.toPoint(pointCoordinate.getLongitude(), pointCoordinate.getLatitude());
    }

    @Named("eventTypeStringToEnum")
    default FlightEventTypeEnum eventTypeStringToEnum(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        return FlightEventTypeEnum.valueOf(value);
    }

    @Named("eventStatusStringToEnum")
    default FlightEventStatusEnum eventStatusStringToEnum(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        return FlightEventStatusEnum.valueOf(value);
    }

    /**
     * FlightDemandMatchStatusEnum 转 String
     */
    @Named("eventTypeEnumToString")
    default String eventTypeEnumToString(FlightEventTypeEnum value) {
        if (value == null) {
            return null;
        }
        return value.name();
    }

    @Named("eventStatusEnumToString")
    default String eventStatusEnumToString(FlightEventStatusEnum value) {
        if (value == null) {
            return null;
        }
        return value.name();
    }
} 