package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.ContentDetailDO;
import com.deepinnet.skyflow.operationcenter.dto.ContentDetailDTO;
import com.deepinnet.skyflow.operationcenter.vo.ContentDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 首页内容转换接口
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface ContentDetailConvert {

    ContentDetailConvert INSTANCE = Mappers.getMapper(ContentDetailConvert.class);

    /**
     * DO 转 DTO
     *
     * @param contentDetailDO DO 对象
     * @return DTO 对象
     */
    ContentDetailDTO convert(ContentDetailDO contentDetailDO);

    /**
     * DTO 转 DO
     *
     * @param contentDetailDTO DTO 对象
     * @return DO 对象
     */
    ContentDetailDO convertToDO(ContentDetailDTO contentDetailDTO);

    /**
     * DTO List 转 DO List
     *
     * @param contentDetailDTOList DTO 列表
     * @return DO 列表
     */
    List<ContentDetailDO> convertToDOList(List<ContentDetailDTO> contentDetailDTOList);

    /**
     * DO List 转 DTO List
     *
     * @param contentDetailDOList DO 列表
     * @return DTO 列表
     */
    List<ContentDetailDTO> convertList(List<ContentDetailDO> contentDetailDOList);

    /**
     * DTO 转 VO
     *
     * @param contentDetailDTO DTO 对象
     * @return VO 对象
     */
    ContentDetailVO convertToVO(ContentDetailDTO contentDetailDTO);

    /**
     * DTO列表 转 VO列表
     *
     * @param contentDetailDTOList DTO 列表
     * @return VO 列表
     */
    List<ContentDetailVO> convertToVOList(List<ContentDetailDTO> contentDetailDTOList);
} 