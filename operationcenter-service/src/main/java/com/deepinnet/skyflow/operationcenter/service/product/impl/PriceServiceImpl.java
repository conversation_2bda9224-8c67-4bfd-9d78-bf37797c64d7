package com.deepinnet.skyflow.operationcenter.service.product.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.PriceDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightProductDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.PriceRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightProductRepository;
import com.deepinnet.skyflow.operationcenter.dto.PriceDTO;
import com.deepinnet.skyflow.operationcenter.dto.PriceQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.product.PriceService;
import com.deepinnet.skyflow.operationcenter.service.convert.PriceConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 价格服务实现类
 *
 * <AUTHOR>
 */
@Service
public class PriceServiceImpl implements PriceService {

    @Resource
    private PriceRepository priceRepository;

    @Resource
    private FlightProductRepository flightProductRepository;

    @Resource
    private PriceConvert priceConvert;

    @Override
    public Integer savePrice(PriceDTO priceDTO) {
        // 不限次数时，validityTimes设置为-1
        if (priceDTO.getValidityTimes() != null && priceDTO.getValidityTimes() == 0) {
            priceDTO.setValidityTimes(-1);  // 标识为不限次数
        }
        
        PriceDO priceDO = priceConvert.convertToDO(priceDTO);
        priceDO.setGmtCreated(LocalDateTime.now());
        priceDO.setGmtModified(LocalDateTime.now());
        priceDO.setId(null);
        boolean success = priceRepository.save(priceDO);
        if (!success) {
            LogUtil.error("保存价格失败: {}", priceDTO.getProductNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        // 保存成功后，更新产品的最低价格
        updateProductMinPrice(priceDTO.getProductNo());
        
        LogUtil.info("保存价格成功: {}", priceDO.getId());
        return priceDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSavePrice(List<PriceDTO> priceDTOList) {
        if (CollectionUtils.isEmpty(priceDTOList)) {
            LogUtil.warn("批量保存价格失败: 价格列表为空");
            return true;
        }
        
        List<PriceDO> priceDOList = new ArrayList<>(priceDTOList.size());
        LocalDateTime now = LocalDateTime.now();
        String productNo = null;
        
        for (PriceDTO priceDTO : priceDTOList) {
            if (StringUtils.isBlank(priceDTO.getProductNo())) {
                LogUtil.error("批量保存价格失败: 价格中缺少产品编号");
                throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
            }
            
            // 记录产品编号，用于后续更新最低价格
            if (productNo == null) {
                productNo = priceDTO.getProductNo();
            }
            
            // 不限次数时，validityTimes设置为-1
            if (priceDTO.getValidityTimes() != null && priceDTO.getValidityTimes() == 0) {
                priceDTO.setValidityTimes(-1);  // 标识为不限次数
            }
            
            PriceDO priceDO = priceConvert.convertToDO(priceDTO);
            priceDO.setGmtCreated(now);
            priceDO.setGmtModified(now);
            priceDO.setId(null);
            priceDOList.add(priceDO);
        }
        
        boolean success = priceRepository.saveBatch(priceDOList);
        if (!success) {
            LogUtil.error("批量保存价格失败: 共 {} 条价格", priceDTOList.size());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        // 保存成功后，更新产品的最低价格
        if (productNo != null) {
            updateProductMinPrice(productNo);
        }
        
        LogUtil.info("批量保存价格成功: 共 {} 条价格", priceDTOList.size());
        return true;
    }

    @Override
    public boolean updatePrice(PriceDTO priceDTO) {
        if (priceDTO.getId() == null) {
            LogUtil.error("更新价格失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查价格是否存在
        PriceDO existPrice = priceRepository.getById(priceDTO.getId());
        if (existPrice == null) {
            LogUtil.error("更新价格失败，价格不存在: {}", priceDTO.getId());
            throw new BizException(BizErrorCode.PRICE_NOT_FOUND.getCode(), BizErrorCode.PRICE_NOT_FOUND.getDesc());
        }
        
        // 不限次数时，validityTimes设置为-1
        if (priceDTO.getValidityTimes() != null && priceDTO.getValidityTimes() == 0) {
            priceDTO.setValidityTimes(-1);  // 标识为不限次数
        }

        PriceDO priceDO = priceConvert.convertToDO(priceDTO);
        priceDO.setGmtModified(LocalDateTime.now());
        
        boolean success = priceRepository.updateById(priceDO);
        if (!success) {
            LogUtil.error("更新价格失败: {}", priceDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        // 更新成功后，同步更新产品的最低价格
        updateProductMinPrice(priceDTO.getProductNo());
        
        LogUtil.info("更新价格成功: {}", priceDO.getId());
        return true;
    }
    
    /**
     * 更新产品的最低价格
     *
     * @param productNo 产品编号
     */
    private void updateProductMinPrice(String productNo) {
        if (StringUtils.isBlank(productNo)) {
            LogUtil.warn("更新产品最低价格失败，产品编号为空");
            return;
        }
        
        try {
            // 查询该产品所有价格中的最低基础价格
            List<PriceDO> priceList = priceRepository.list(
                    Wrappers.lambdaQuery(PriceDO.class)
                            .eq(PriceDO::getProductNo, productNo)
                            .orderByAsc(PriceDO::getBasePrice)
            );
            
            if (CollectionUtils.isEmpty(priceList)) {
                LogUtil.warn("产品编号 {} 下没有价格，无法更新最低价格", productNo);
                return;
            }
            
            // 获取最低价格
            BigDecimal minPrice = priceList.get(0).getBasePrice();
            
            // 更新产品的最低价格
            FlightProductDO productDO = new FlightProductDO();
            productDO.setProductMinPrice(minPrice);
            productDO.setGmtModified(LocalDateTime.now());
            
            boolean success = flightProductRepository.update(
                    productDO,
                    Wrappers.lambdaQuery(FlightProductDO.class)
                            .eq(FlightProductDO::getProductNo, productNo)
            );
            
            if (success) {
                LogUtil.info("更新产品 {} 的最低价格为 {} 成功", productNo, minPrice);
            } else {
                LogUtil.warn("更新产品 {} 的最低价格失败", productNo);
            }
        } catch (Exception e) {
            LogUtil.error("更新产品最低价格异常", e);
        }
    }

    @Override
    public PriceDTO getPriceById(Integer id) {
        if (id == null) {
            LogUtil.error("获取价格失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        PriceDO priceDO = priceRepository.getById(id);
        if (priceDO == null) {
            LogUtil.error("获取价格失败，价格不存在: {}", id);
            throw new BizException(BizErrorCode.PRICE_NOT_FOUND.getCode(), BizErrorCode.PRICE_NOT_FOUND.getDesc());
        }

        return priceConvert.convert(priceDO);
    }

    @Override
    public List<PriceDTO> getPricesByProductNo(String productNo) {
        if (StringUtils.isBlank(productNo)) {
            LogUtil.error("获取价格失败，产品编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        List<PriceDO> priceDOList = priceRepository.list(
                Wrappers.lambdaQuery(PriceDO.class)
                        .eq(PriceDO::getProductNo, productNo)
                        .orderByDesc(PriceDO::getGmtCreated)
        );
        
        return priceConvert.convertList(priceDOList);
    }

    @Override
    public CommonPage<PriceDTO> pageQueryPrice(PriceQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<PriceDO> queryWrapper = Wrappers.lambdaQuery(PriceDO.class)
                .eq(StringUtils.isNotBlank(queryDTO.getProductNo()), PriceDO::getProductNo, queryDTO.getProductNo())
                .eq(StringUtils.isNotBlank(queryDTO.getBillingUnit()), PriceDO::getBillingUnit, queryDTO.getBillingUnit())
                .eq(queryDTO.getValidityPeriod() != null, PriceDO::getValidityPeriod, 
                    queryDTO.getValidityPeriod() != null ? queryDTO.getValidityPeriod().getType() : null)
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), PriceDO::getTenantId, queryDTO.getTenantId())
                .orderByDesc(PriceDO::getGmtCreated);

        List<PriceDO> priceDOList = priceRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(priceDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<PriceDTO> priceDTOList = priceConvert.convertList(priceDOList);
        PageInfo<PriceDTO> pageInfo = new PageInfo<>(priceDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), priceDTOList);
    }

    @Override
    public boolean deletePrice(Integer id) {
        if (id == null) {
            LogUtil.error("删除价格失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查价格是否存在
        PriceDO existPrice = priceRepository.getById(id);
        if (existPrice == null) {
            LogUtil.error("删除价格失败，价格不存在: {}", id);
            throw new BizException(BizErrorCode.PRICE_NOT_FOUND.getCode(), BizErrorCode.PRICE_NOT_FOUND.getDesc());
        }

        boolean success = priceRepository.removeById(id);
        if (!success) {
            LogUtil.error("删除价格失败: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("删除价格成功: {}", id);
        return true;
    }

    @Override
    public boolean deletePricesByProductNo(String productNo) {
        if (StringUtils.isBlank(productNo)) {
            LogUtil.error("根据产品编号删除价格失败，产品编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 查询该产品编号下的所有价格
        List<PriceDO> priceList = priceRepository.list(
                Wrappers.lambdaQuery(PriceDO.class)
                        .eq(PriceDO::getProductNo, productNo)
        );
        
        if (CollectionUtils.isEmpty(priceList)) {
            LogUtil.info("产品编号 {} 下没有价格，无需删除", productNo);
            return true;
        }
        
        // 删除所有价格
        boolean success = priceRepository.remove(
                Wrappers.lambdaQuery(PriceDO.class)
                        .eq(PriceDO::getProductNo, productNo)
        );
        
        if (!success) {
            LogUtil.error("根据产品编号删除价格失败: {}", productNo);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        LogUtil.info("根据产品编号删除价格成功，共删除 {} 条价格: {}", priceList.size(), productNo);
        return true;
    }
} 