package com.deepinnet.skyflow.operationcenter.service.product;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmQueryDTO;

import java.util.List;

/**
 * 飞行无人机品牌型号服务接口
 *
 * <AUTHOR>
 */
public interface FlightUavBmService {

    /**
     * 保存飞行无人机品牌型号
     *
     * @param flightUavBmDTO 飞行无人机品牌型号数据
     * @return 已保存的飞行无人机品牌型号ID
     */
    String saveFlightUavBm(FlightUavBmDTO flightUavBmDTO);

    /**
     * 更新飞行无人机品牌型号
     *
     * @param flightUavBmDTO 飞行无人机品牌型号数据
     * @return 更新是否成功
     */
    boolean updateFlightUavBm(FlightUavBmDTO flightUavBmDTO);

    /**
     * 根据ID获取飞行无人机品牌型号
     *
     * @param id 飞行无人机品牌型号ID
     * @return 飞行无人机品牌型号数据
     */
    FlightUavBmDTO getFlightUavBmById(Integer id);

    /**
     * 根据型号编码获取飞行无人机品牌型号
     *
     * @param flightUavBmNo 型号编码
     * @return 飞行无人机品牌型号数据
     */
    FlightUavBmDTO getFlightUavBmByNo(String flightUavBmNo);

    /**
     * 根据产品编号获取飞行无人机品牌型号
     *
     * @param productNo 产品编号
     * @return 飞行无人机品牌型号数据
     */
    FlightUavBmDTO getFlightUavBmByProductNo(String productNo);

    /**
     * 分页查询飞行无人机品牌型号
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    CommonPage<FlightUavBmDTO> pageQueryFlightUavBm(FlightUavBmQueryDTO queryDTO);

    /**
     * 根据机型编码列表查询机型信息
     * @param uavBmNoList
     * @return
     */
    List<FlightUavBmDTO> listFlightUavBm(List<String> uavBmNoList);

    /**
     * 删除飞行无人机品牌型号
     *
     * @param id 飞行无人机品牌型号ID
     * @return 删除是否成功
     */
    boolean deleteFlightUavBm(Integer id);
} 