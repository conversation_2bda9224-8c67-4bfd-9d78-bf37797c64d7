package com.deepinnet.skyflow.operationcenter.service.validator;

import com.deepinnet.digitaltwin.common.error.ErrorCode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.RoutineInspectionFlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.enums.*;
import com.deepinnet.skyflow.operationcenter.service.convert.TimestampConverter;
import com.deepinnet.skyflow.operationcenter.service.order.FlightOrderHelper;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderFlyingInfoVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderProductUsageVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Objects;

/**
 * Description:
 * Date: 2025/4/17
 * Author: lijunheng
 */
@Component
public class RoutineInspectionFlightDemandValidator implements FlightDemandValidator {

    @Resource
    private FlightOrderHelper flightOrderHelper;

    @Override
    public boolean supports(FlightDemandTypeEnum typeEnum) {
        return FlightDemandTypeEnum.ROUTINE_INSPECTION == typeEnum;
    }

    @Override
    public void validate(FlightDemandDTO demand) {
        FlightOrderVO flightOrder = flightOrderHelper.getFlightOrder(demand);

        if (flightOrder == null) {
            throw new IllegalArgumentException("低空需求订单未找到: " + demand.getFlightOrderNo());
        }

        //校验订单状态，必须是进行中的订单才允许创建需求单
        if (!Objects.equals(OrderStatusEnum.IN_PROGRESS.getCode(), flightOrder.getStatus())) {
            throw new BizException(ErrorCode.ILLEGAL_PARAMS.getCode(), "只能是进行中状态的订单可以创建需求");
        }
        // 需求有效期校验
        validateDemandPeriod(demand, flightOrder);
    }

    /**
     * 校验需求有效期
     * 1、如果是包月或包年服务，则需求有效期只能在订单有效期之间进行选择，且需求开始时间大于等于当天
     * 2、如果是按次服务，则需求开始时间大于等于当天，需求结束时间不限制
     */
    private void validateDemandPeriod(FlightDemandDTO demand, FlightOrderVO flightOrder) {
        RoutineInspectionFlightDemandDTO routineInspectionDetail = demand.getRoutineInspectionDetail();
        LocalDate today = LocalDate.now();

        // 检查需求开始时间必须大于等于当天
        if (routineInspectionDetail.getDemandStartTime() == null || routineInspectionDetail.getDemandStartTime().isBefore(today)) {
            throw new BizException(ErrorCode.ILLEGAL_PARAMS.getCode(), "开始时间必须大于等于当天");
        }

        //判断需求来源
        if (demand.getScene() == FlightDemandSceneEnum.ONE_NET_UNIFIED_FLY) {
            FlightOrderFlyingInfoVO flyingInfo = flightOrder.getFlyingInfo();
            validateDemandPeriod(flyingInfo.getValidityPeriodStart(), flyingInfo.getValidityPeriodEnd(), routineInspectionDetail);
        } else {
            // 获取订单使用信息，暂时先取第一个，如果后面订单里多个产品的服务范围（周期、次数）类型不一致的话再处理，@家驹
            FlightOrderProductUsageVO productUsage = flightOrder.getProductUsageList().get(0);

            // 如果是包月或包年服务
            if (!Objects.equals(productUsage.getValidityPeriodType(), ValidityPeriodEnum.SINGLE.getType())) {
                // 需求结束时间不能为空
                if (routineInspectionDetail.getDemandEndTime() == null) {
                    throw new BizException(ErrorCode.ILLEGAL_PARAMS.getCode(), "非按次的需求结束时间不能为空");
                }
                validateDemandPeriod(productUsage.getValidityPeriodStart(), productUsage.getValidityPeriodEnd(), routineInspectionDetail);

            }
            // 如果是按次服务，仅要求开始时间大于等于当天，结束时间可以不限制
        }


    }

    private void validateDemandPeriod(Long periodStartTime, Long periodEndTime, RoutineInspectionFlightDemandDTO routineInspectionDetail) {
        // 需求有效期需要在订单有效期内
        LocalDate orderStartDate = TimestampConverter.toLocalDateTimeShanghai(periodStartTime).toLocalDate();
        LocalDate orderEndDate = TimestampConverter.toLocalDateTimeShanghai(periodEndTime).toLocalDate();

        if (routineInspectionDetail.getDemandStartTime().isBefore(orderStartDate)) {
            throw new BizException(ErrorCode.ILLEGAL_PARAMS.getCode(), "需求的开始时间不能早于需求订单的开始时间");
        }

        if (routineInspectionDetail.getDemandEndTime().isAfter(orderEndDate)) {
            throw new BizException(ErrorCode.ILLEGAL_PARAMS.getCode(), "需求的结束时间不能晚于需求订单的结束时间");
        }
    }
}
