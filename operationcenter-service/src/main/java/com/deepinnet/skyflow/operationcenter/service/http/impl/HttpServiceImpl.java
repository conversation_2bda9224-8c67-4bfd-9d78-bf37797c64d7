package com.deepinnet.skyflow.operationcenter.service.http.impl;

import com.alibaba.fastjson2.JSON;
import com.deepinnet.skyflow.operationcenter.common.constants.OpenApiConstant;
import com.deepinnet.skyflow.operationcenter.dto.openapi.*;
import com.deepinnet.skyflow.operationcenter.service.http.HttpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * HTTP服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HttpServiceImpl implements HttpService {

    @Resource
    private RestTemplate restTemplate;

    @Override
    public Object fetchData(Object request) {
        if (request instanceof GeoCodingRequest) {
            return handleGeoCodingRequest((GeoCodingRequest) request);
        } else if (request instanceof ReverseGeoCodingRequest) {
            return handleReverseGeoCodingRequest((ReverseGeoCodingRequest) request);
        } else {
            log.error("不支持的请求类型: {}", request.getClass().getName());
            return null;
        }
    }

    /**
     * 处理地理编码请求
     *
     * @param request 地理编码请求
     * @return 地理编码响应
     */
    private Geocoding handleGeoCodingRequest(GeoCodingRequest request) {
        try {
            GeocodingParam params = request.getUrlParams();
            if (params == null) {
                log.error("地理编码请求参数为空");
                return createEmptyGeocoding();
            }

            // 获取参数
            String address = params.getAddress();
            String city = params.getCity();
            String key = params.getKey();

            if (address == null || address.isEmpty()) {
                log.error("地理编码地址为空");
                return createEmptyGeocoding();
            }


            // 构建URL
            String url = String.format(OpenApiConstant.GEOCODE_URL,
                    address, key != null ? key : OpenApiConstant.USER_KEY, city);
            
            // 请求API
            log.info("请求地理编码API: {}", url);
            long start = System.currentTimeMillis();
            String response = restTemplate.getForObject(url, String.class);
            log.info("地理编码API请求成功, 耗时: {}ms", System.currentTimeMillis() - start);
            
            if (response == null) {
                log.error("地理编码API响应为空");
                return createEmptyGeocoding();
            }
            
            // 解析响应
            return JSON.parseObject(response, Geocoding.class);
        } catch (Exception e) {
            log.error("请求地理编码API失败: {}", e.getMessage());
            return createEmptyGeocoding();
        }
    }

    /**
     * 处理逆地理编码请求
     *
     * @param request 逆地理编码请求
     * @return 逆地理编码响应
     */
    private ReverseGeocoding handleReverseGeoCodingRequest(ReverseGeoCodingRequest request) {
        try {
            ReverseGeocodingParam params = request.getUrlParams();
            if (params == null) {
                log.error("逆地理编码请求参数为空");
                return createEmptyReverseGeocoding();
            }

            // 获取参数
            String location = params.getLocation();
            String key = params.getKey();

            if (location == null || location.isEmpty()) {
                log.error("逆地理编码坐标为空");
                return createEmptyReverseGeocoding();
            }
            
            // 构建URL
            String url = String.format(OpenApiConstant.REVERSE_GEOCODING_URL, 
                    location, key != null ? key : OpenApiConstant.USER_KEY);
            
            // 请求API
            log.info("请求逆地理编码API: {}", url);
            long start = System.currentTimeMillis();
            String response = restTemplate.getForObject(url, String.class);
            log.info("逆地理编码API请求成功, 耗时: {}ms", System.currentTimeMillis() - start);
            
            if (response == null) {
                log.error("逆地理编码API响应为空");
                return createEmptyReverseGeocoding();
            }
            
            // 解析响应
            return JSON.parseObject(response, ReverseGeocoding.class);
        } catch (Exception e) {
            log.error("请求逆地理编码API失败: {}", e.getMessage());
            return createEmptyReverseGeocoding();
        }
    }

    /**
     * 创建空的地理编码响应
     *
     * @return 空的地理编码响应
     */
    private Geocoding createEmptyGeocoding() {
        Geocoding geocoding = new Geocoding();
        geocoding.setStatus("0");
        geocoding.setInfo("失败");
        return geocoding;
    }

    /**
     * 创建空的逆地理编码响应
     *
     * @return 空的逆地理编码响应
     */
    private ReverseGeocoding createEmptyReverseGeocoding() {
        ReverseGeocoding reverseGeocoding = new ReverseGeocoding();
        reverseGeocoding.setStatus("0");
        reverseGeocoding.setInfo("失败");
        return reverseGeocoding;
    }
} 