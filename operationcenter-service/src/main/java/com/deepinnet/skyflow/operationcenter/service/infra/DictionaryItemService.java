package com.deepinnet.skyflow.operationcenter.service.infra;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryItemDTO;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryItemQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryTreeDTO;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryItemBatchOperationDTO;

import java.util.List;

/**
 * 字典项服务接口
 *
 * <AUTHOR>
 */
public interface DictionaryItemService {

    /**
     * 保存字典项
     *
     * @param dictionaryItemDTO 字典项数据
     * @return 已保存的字典项ID
     */
    Long saveDictionaryItem(DictionaryItemDTO dictionaryItemDTO);

    /**
     * 批量保存字典项
     *
     * @param dictionaryItemDTOList 字典项数据列表
     * @return 是否保存成功
     */
    boolean batchSaveDictionaryItems(List<DictionaryItemDTO> dictionaryItemDTOList);

    /**
     * 更新字典项
     *
     * @param dictionaryItemDTO 字典项数据
     * @return 更新是否成功
     */
    boolean updateDictionaryItem(DictionaryItemDTO dictionaryItemDTO);

    /**
     * 根据ID获取字典项
     *
     * @param id 字典项ID
     * @return 字典项数据
     */
    DictionaryItemDTO getDictionaryItemById(Long id);

    /**
     * 根据字典ID和编码获取字典项
     *
     * @param dictionaryId 字典ID
     * @param code 字典项编码
     * @return 字典项数据
     */
    DictionaryItemDTO getDictionaryItemByCode(Long dictionaryId, String code);

    /**
     * 根据字典ID获取字典项列表
     *
     * @param dictionaryId 字典ID
     * @return 字典项列表
     */
    List<DictionaryItemDTO> listDictionaryItemsByDictionaryId(Long dictionaryId);

    /**
     * 分页查询字典项
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    CommonPage<DictionaryItemDTO> pageQueryDictionaryItems(DictionaryItemQueryDTO queryDTO);

    /**
     * 删除字典项
     *
     * @param id 字典项ID
     * @return 删除是否成功
     */
    boolean deleteDictionaryItem(Long id);

    /**
     * 根据字典ID删除所有字典项
     *
     * @param dictionaryId 字典ID
     * @return 删除是否成功
     */
    boolean deleteDictionaryItemsByDictionaryId(Long dictionaryId);

    /**
     * 根据字典类型和租户ID查询字典树
     *
     * @param type 字典类型
     * @param tenantId 租户ID
     * @return 字典树列表
     */
    List<DictionaryTreeDTO> getDictionaryTreeByTypeAndTenantId(String type, String tenantId);

    /**
     * 根据租户ID、字典类型和父编码查询字典项列表
     *
     * @param tenantId 租户ID
     * @param type 字典类型
     * @param parentCode 父编码
     * @return 字典项列表
     */
    List<DictionaryItemDTO> listDictionaryItemsByTypeAndParentCode(String tenantId, String type, String parentCode);

    /**
     * 批量操作字典项（删除和新增）
     *
     * @param batchOperationDTO 批量操作参数
     * @return 操作是否成功
     */
    boolean batchOperateDictionaryItems(DictionaryItemBatchOperationDTO batchOperationDTO);
} 