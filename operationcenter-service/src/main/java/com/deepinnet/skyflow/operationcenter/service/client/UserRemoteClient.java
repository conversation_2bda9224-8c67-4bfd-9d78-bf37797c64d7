package com.deepinnet.skyflow.operationcenter.service.client;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.UserClient;
import com.deepinnet.infra.api.dto.DataAccessDTO;
import com.deepinnet.infra.api.dto.UserDetailDTO;
import com.deepinnet.infra.api.dto.UserMemberDTO;
import com.deepinnet.infra.api.dto.UserQueryDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/10
 */
@Component
public class UserRemoteClient {

    @Resource
    private UserClient userClient;

    public List<UserDetailDTO> batchQueryUser(List<String> userNos) {
        UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setUserNos(userNos);

        Result<List<UserDetailDTO>> userInfoByUserNo = userClient.getUserDetailList(userQueryDTO);

        if (!userInfoByUserNo.isSuccess()) {
            LogUtil.error("UserRemoteClient.batchQueryUser: userInfoByUserNo = {}", userNos);
            throw new BizException(userInfoByUserNo.getErrorCode(), userInfoByUserNo.getErrorDesc());
        }

        return userInfoByUserNo.getData();
    }

    public DataAccessDTO getAvailableQueryData() {
        Result<DataAccessDTO> availableQueryData = userClient.getAvailableQueryData();

        if (!availableQueryData.isSuccess()) {
            LogUtil.error("UserRemoteClient.getAvailableQueryData: availableQueryData = {}", availableQueryData);
            throw new BizException(availableQueryData.getErrorCode(), availableQueryData.getErrorDesc());
        }

        return availableQueryData.getData();
    }

}
