package com.deepinnet.skyflow.operationcenter.service.demand.impl.matching;

import com.deepinnet.digitaltwin.common.util.JsonConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Description:
 * Date: 2025/4/22
 * Author: lijunheng
 */
@Slf4j
@Component
public class DynamicSqlInvokeHelper {

    @Resource
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    /**
     * 执行查询（使用预处理语句）
     * 优先使用 DynamicSqlEntity 中的参数进行预处理查询
     *
     * @param dynamicSqlEntity SQL实体
     * @param clazz            返回类型
     * @param <T>              返回类型泛型
     * @return 查询结果
     */
    @SuppressWarnings("unchecked")
    public <T> T queryForObject(DynamicSqlEntity dynamicSqlEntity, Class<T> clazz) {
        try {
            String sql = dynamicSqlEntity.buildSql();
            Map<String, Object> params = dynamicSqlEntity.getParametersMap();
            log.debug("queryForObject sql:{}, params:{}", sql, params);

            SqlParameterSource sqlParameterSource = new MapSqlParameterSource(params);
            // 如果是基础类型 / 包装类 / String
            if (clazz.isPrimitive() || JsonConvertUtil.isWrapperType(clazz) || clazz == String.class) {
                return namedParameterJdbcTemplate.queryForObject(sql, sqlParameterSource, clazz);
            }

            // 如果是 Map（通用对象映射）
            if (Map.class.isAssignableFrom(clazz)) {
                List<Map<String, Object>> list = namedParameterJdbcTemplate.queryForList(sql, params);
                return list.isEmpty() ? null : (T) list.get(0);
            }

            // 默认当作复杂对象进行封装
            return namedParameterJdbcTemplate.queryForObject(sql, sqlParameterSource, BeanPropertyRowMapper.newInstance(clazz));
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    public <T> List<T> queryForList(DynamicSqlEntity dynamicSqlEntity, Class<T> clazz) {
        try {
            String sql = dynamicSqlEntity.buildSql();
            Map<String, Object> params = dynamicSqlEntity.getParametersMap();
            log.debug("queryList sql:{}, params:{}", sql, params);
            SqlParameterSource sqlParameterSource = new MapSqlParameterSource(params);
            if (Map.class.isAssignableFrom(clazz)) {
                return (List<T>) namedParameterJdbcTemplate.queryForList(sql, sqlParameterSource);
            }
            if (String.class == clazz || JsonConvertUtil.isWrapperType(clazz)) {
                return namedParameterJdbcTemplate.query(sql, sqlParameterSource, new SingleColumnRowMapper<>(clazz));
            }
            return namedParameterJdbcTemplate.query(sql, sqlParameterSource, new BeanPropertyRowMapper<>(clazz));
        } catch (EmptyResultDataAccessException e) {
            return new ArrayList<>(0);
        }
    }

    /**
     * 判断是否存在（使用预处理语句）
     *
     * @param dynamicSqlEntity SQL实体
     * @return 是否存在
     */
    public boolean exist(DynamicSqlEntity dynamicSqlEntity) {
        DynamicSqlEntity existSqlEntity = new DynamicSqlEntity(dynamicSqlEntity);
        existSqlEntity.setSelect("select 1");
        existSqlEntity.setLimit("limit 1");
        Integer invoke = queryForObject(existSqlEntity, Integer.class);
        return invoke != null;
    }
}
