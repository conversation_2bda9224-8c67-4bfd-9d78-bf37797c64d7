package com.deepinnet.skyflow.operationcenter.service.user.impl;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.UserClient;
import com.deepinnet.skyflow.operationcenter.service.user.UserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> wong
 * @create 2025/4/21 17:36
 * @Description
 */
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private UserClient userClient;

    @Override
    public Boolean checkUserInfoStatus() {
        Result<Boolean> res = userClient.checkUserInfoStatus();
        if (!res.isSuccess()) {
            throw new BizException(res.getErrorCode(), res.getErrorDesc());
        }

        return res.getData();
    }
}
