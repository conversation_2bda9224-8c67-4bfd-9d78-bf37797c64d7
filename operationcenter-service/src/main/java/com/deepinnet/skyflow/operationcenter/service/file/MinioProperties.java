package com.deepinnet.skyflow.operationcenter.service.file;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * MinIO配置类
 */
@Data
@ConfigurationProperties(prefix = "minio")
public class MinioProperties {

    /**
     * MinIO服务地址
     */
    private String endpoint;

    /**
     * MinIO用户名
     */
    private String accessKey;

    /**
     * MinIO密码
     */
    private String secretKey;

    /**
     * 默认存储桶名称
     */
    private String bucketName;

    /**
     * 获取默认存储桶名称
     */
    public String getDefaultBucketName() {
        return bucketName;
    }
} 