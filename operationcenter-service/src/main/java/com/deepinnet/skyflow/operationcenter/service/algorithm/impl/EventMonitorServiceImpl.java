package com.deepinnet.skyflow.operationcenter.service.algorithm.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.util.JsonConvertUtil;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsStatDTO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightEventsDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightEventsRepository;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsStatQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.algorithm.EventMonitorService;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightEventsConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 算法检测事件服务实现类
 *
 * <AUTHOR>
 */
@Service
public class EventMonitorServiceImpl implements EventMonitorService {

    @Resource
    private FlightEventsRepository flightEventsRepository;

    @Resource
    private FlightEventsConvert flightEventsConvert;

    @Override
    public String saveEvent(FlightEventsDTO flightEventsDTO) {
        // 转换为DO并保存
        FlightEventsDO flightEventsDO = flightEventsConvert.convertToDO(flightEventsDTO);
        boolean success = flightEventsRepository.save(flightEventsDO);
        if (!success) {
            LogUtil.error("保存飞行事件失败: {}", flightEventsDTO.getEventName());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("保存飞行事件成功: {}", flightEventsDO.getId());
        return flightEventsDO.getId();
    }

    @Override
    public void saveEventBatch(List<FlightEventsDTO> flightEventsDTOList) {
        if (CollectionUtil.isNotEmpty(flightEventsDTOList)) {
            // 转换为DO列表
            List<FlightEventsDO> flightEventsDOList = flightEventsDTOList.stream()
                    .map(dto -> flightEventsConvert.convertToDO(dto))
                    .collect(Collectors.toList());

            boolean success = flightEventsRepository.saveBatch(flightEventsDOList);
            if (!success) {
                LogUtil.error("批量保存飞行事件失败");
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
            }

            LogUtil.info("批量保存飞行事件成功, 数量: {}", flightEventsDOList.size());
        }
    }

    @Override
    public FlightEventsDTO getEventById(String id) {
        if (id == null) {
            LogUtil.error("获取飞行事件失败，ID为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightEventsDO flightEventsDO = flightEventsRepository.getById(id);
        if (flightEventsDO == null) {
            LogUtil.error("获取飞行事件失败，事件不存在: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        return flightEventsConvert.convert(flightEventsDO);
    }

    @Override
    public CommonPage<FlightEventsDTO> pageQuery(FlightEventsQueryDTO queryDTO) {
        // 创建分页对象
        Page<FlightEventsDO> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<FlightEventsDO> wrapper = Wrappers.lambdaQuery(FlightEventsDO.class)
                .eq(StringUtils.isNotBlank(queryDTO.getId()), FlightEventsDO::getId, queryDTO.getId())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), FlightEventsDO::getTenantId, queryDTO.getTenantId())
                .eq(queryDTO.getEventType() != null, FlightEventsDO::getEventType, queryDTO.getEventType() == null ? null : queryDTO.getEventType().name())
                .eq(queryDTO.getStatus() != null, FlightEventsDO::getStatus, queryDTO.getStatus() == null ? null : queryDTO.getStatus().name())
                .eq(StringUtils.isNotBlank(queryDTO.getFlightTaskCode()), FlightEventsDO::getFlightTaskCode, queryDTO.getFlightTaskCode())
                .eq(StringUtils.isNotBlank(queryDTO.getLicensePlate()), FlightEventsDO::getLicensePlate, queryDTO.getLicensePlate())
                .like(StringUtils.isNotBlank(queryDTO.getEventName()), FlightEventsDO::getEventName, queryDTO.getEventName())
                .like(StringUtils.isNotBlank(queryDTO.getEventLocation()), FlightEventsDO::getEventLocation, queryDTO.getEventLocation())
                .ge(queryDTO.getStartTime() != null, FlightEventsDO::getEventTime, queryDTO.getStartTime())
                .le(queryDTO.getEndTime() != null, FlightEventsDO::getEventTime, queryDTO.getEndTime())
                .orderByDesc(FlightEventsDO::getEventTime);

        // 执行查询
        List<FlightEventsDO> resultPage = flightEventsRepository.list(wrapper);

        if (CollectionUtils.isEmpty(resultPage)) {
            return CommonPage.buildEmptyPage();
        }

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), page.getPages(), page.getTotal(), flightEventsConvert.convertList(resultPage));
    }

    @Override
    public List<FlightEventsDTO> queryListByFlightTaskCode(String flightTaskCode) {
        if (StrUtil.isBlank(flightTaskCode)) {
            LogUtil.error("查询飞行事件列表失败，飞行任务ID为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 构建查询条件
        LambdaQueryWrapper<FlightEventsDO> wrapper = Wrappers.lambdaQuery(FlightEventsDO.class)
                .eq(FlightEventsDO::getFlightTaskCode, flightTaskCode)
                .orderByDesc(FlightEventsDO::getEventTime);

        // 执行查询
        List<FlightEventsDO> flightEventsDOList = flightEventsRepository.list(wrapper);

        // 转换结果
        return flightEventsConvert.convertList(flightEventsDOList);
    }

    @Override
    public List<FlightEventsStatDTO> queryFlightEventsStat(FlightEventsStatQueryDTO queryDTO) {
        if (queryDTO == null || queryDTO.getStartTime() == null) {
            LogUtil.error("查询飞行事件统计失败，开始时间为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 构建查询条件
        QueryWrapper<FlightEventsDO> wrapper = new QueryWrapper<>();
        wrapper.select("event_type", "count(*) as total_num")
                .ge("event_time", queryDTO.getStartTime())
                .eq("tenant_id", queryDTO.getTenantId())
                .groupBy("event_type")
                .orderByDesc("total_num");

        // 执行查询获取所有事件
        List<Map<String, Object>> statList = flightEventsRepository.listMaps(wrapper);
        return statList.stream().map(statObj -> (FlightEventsStatDTO)JsonConvertUtil.parseJson(JsonConvertUtil.toJsonStr(statObj), FlightEventsStatDTO.class)).collect(Collectors.toList());
    }
}
