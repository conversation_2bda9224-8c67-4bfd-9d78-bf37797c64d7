package com.deepinnet.skyflow.operationcenter.service.context;

/**
 * Description: 用户权限范围
 * Date: 2025/5/21
 * Author: lijunheng
 */
public class DataPermissionContext {

    private static final ThreadLocal<UserDataPermissionEntity> DATA_PERMISSION_THREADLOCAL = new ThreadLocal<>();

    /**
     * 请求刚进来时执行
     *
     * @param userDataPermissionEntity
     */
    public static final void set(UserDataPermissionEntity userDataPermissionEntity) {
        DATA_PERMISSION_THREADLOCAL.set(userDataPermissionEntity);
    }

    public static void disableDataPermission() {
        UserDataPermissionEntity userDataPermissionEntity = DATA_PERMISSION_THREADLOCAL.get();
        if (userDataPermissionEntity != null) {
            userDataPermissionEntity.setEnableDataPermission(Boolean.FALSE);
        }
    }

    public static void enableDataPermission() {
        UserDataPermissionEntity userDataPermissionEntity = DATA_PERMISSION_THREADLOCAL.get();
        if (userDataPermissionEntity != null) {
            userDataPermissionEntity.setEnableDataPermission(Boolean.TRUE);
        }
    }

    public static boolean isEnableDataPermission() {
        UserDataPermissionEntity userDataPermissionEntity = DATA_PERMISSION_THREADLOCAL.get();
        if (userDataPermissionEntity == null) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE.equals(userDataPermissionEntity.getEnableDataPermission());
    }

    public static UserDataPermissionEntity get() {
        return DATA_PERMISSION_THREADLOCAL.get();
    }

    public static void clear() {
        DATA_PERMISSION_THREADLOCAL.remove();
    }
}
