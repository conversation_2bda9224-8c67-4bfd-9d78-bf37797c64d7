package com.deepinnet.skyflow.operationcenter.service.task.worker;

import cn.hutool.core.util.StrUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ScheduleTaskDO;
import com.deepinnet.skyflow.operationcenter.dal.enums.ScheduleTaskTypeEnum;
import com.deepinnet.skyflow.operationcenter.dto.OrderQueryDTO;
import com.deepinnet.skyflow.operationcenter.enums.OrderStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.order.FlightOrderService;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/23
 */

@Component
public class OrderCloseScheduleWorker extends AbstractBaseTaskWorker{

    @Resource
    private FlightOrderService flightOrderService;

    @Override
    public ScheduleTaskTypeEnum getTaskType() {
        return ScheduleTaskTypeEnum.CLOSE_ORDER;
    }

    @Override
    protected void doOtherBiz(ScheduleTaskDO scheduleTask) {

    }

    @Override
    protected List<String> needTerminatedErrorCode() {
        return List.of(BizErrorCode.ORDER_ALREADY_CLOSED.getCode(), BizErrorCode.ORDER_ALREADY_FINISHED.getCode());
    }

    @Override
    protected void checkBizStatus(Long taskId, String bizNo, String tenantId) {
        OrderQueryDTO orderQueryDTO = new OrderQueryDTO();
        orderQueryDTO.setOrderNo(bizNo);
        orderQueryDTO.setTenantId(tenantId);

        FlightOrderVO flightOrderByNo = flightOrderService.getFlightOrderByNo(orderQueryDTO);

        if (StrUtil.equals(OrderStatusEnum.FINISHED.getCode(), flightOrderByNo.getStatus())) {
            LogUtil.error("执行任务失败，业务单号：{}，状态：{}，任务id：{}，任务类型：{}", bizNo, OrderStatusEnum.IN_PROGRESS.getCode(), taskId, getTaskType());
            throw new BizException(BizErrorCode.ORDER_ALREADY_FINISHED.getCode());
        }

        if (StrUtil.equals(OrderStatusEnum.CLOSED.getCode(), flightOrderByNo.getStatus())) {
            LogUtil.error("执行任务失败，业务单号：{}，状态：{}，任务id：{}，任务类型：{}", bizNo, OrderStatusEnum.IN_PROGRESS.getCode(), taskId, getTaskType());
            throw new BizException(BizErrorCode.ORDER_ALREADY_CLOSED.getCode());
        }
    }

    @Override
    protected void updateBizStatus(String bizNo, String tenantId) {
        OrderQueryDTO orderQueryDTO = new OrderQueryDTO();
        orderQueryDTO.setOrderNo(bizNo);
        orderQueryDTO.setTenantId(tenantId);

        FlightOrderVO flightOrderByNo = flightOrderService.getFlightOrderByNo(orderQueryDTO);

        OrderStatusEnum pushStatus = StrUtil.equals(OrderStatusEnum.APPROVING.getCode(), flightOrderByNo.getStatus())
                ? OrderStatusEnum.CLOSED : OrderStatusEnum.FINISHED;

        flightOrderService.updateOrderStatus(bizNo, pushStatus.getCode(), tenantId);
    }
}
