package com.deepinnet.skyflow.operationcenter.service.task;

import com.deepinnet.skyflow.operationcenter.service.context.TenantContext;
import org.springframework.core.task.TaskDecorator;

/**
 * <p>
 *    通过 TaskDecorator 传递 ThreadLocal 中的 tenantId
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/24
 */
public class TenantContextTaskDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable runnable) {
        return () -> {
            try {
                TenantContext.disableTenantLine();
                runnable.run();
            } finally {
                TenantContext.clear();
            }
        };
    }

}
