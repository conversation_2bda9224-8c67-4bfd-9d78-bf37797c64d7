package com.deepinnet.skyflow.operationcenter.service.product.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightUavRepository;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBatchOperationDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavGroupQueryDTO;
import com.deepinnet.skyflow.operationcenter.enums.BizTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavBmService;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavService;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavGroupVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavVO;
import com.deepinnet.tenant.TenantIdUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 飞行无人机服务实现类
 *
 * <AUTHOR>
 */
@Service
public class FlightUavServiceImpl implements FlightUavService {

    @Resource
    private FlightUavRepository flightUavRepository;

    @Resource
    private FlightUavConvert flightUavConvert;

    @Resource
    private FlightUavBmService flightUavBmService;

    @Override
    public String saveFlightUav(FlightUavDTO flightUavDTO) {
        // 参数校验
        validateFlightUavParams(flightUavDTO);

        // 生成飞行无人机编号
        String flightUavNo = IdGenerateUtil.getId(BizTypeEnum.FLIGHT_UAV.getType());
        flightUavDTO.setFlightUavNo(flightUavNo);

        FlightUavDO flightUavDO = flightUavConvert.convertToDO(flightUavDTO);
        flightUavDO.setGmtCreated(LocalDateTime.now());
        flightUavDO.setGmtModified(LocalDateTime.now());
        flightUavDO.setId(null);

        boolean success = flightUavRepository.save(flightUavDO);
        if (!success) {
            LogUtil.error("保存飞行无人机失败: {}", flightUavDTO.getFlightUavNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("保存飞行无人机成功: {}", flightUavDO.getId());
        return flightUavDO.getFlightUavNo();
    }

    @Override
    public boolean updateFlightUav(FlightUavDTO flightUavDTO) {
        if (flightUavDTO.getId() == null) {
            LogUtil.error("更新飞行无人机失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 参数校验
        validateFlightUavParams(flightUavDTO);

        // 检查飞行无人机是否存在
        FlightUavDO existFlightUav = flightUavRepository.getById(flightUavDTO.getId());
        if (existFlightUav == null) {
            LogUtil.error("更新飞行无人机失败，飞行无人机不存在: {}", flightUavDTO.getId());
            throw new BizException(BizErrorCode.FLIGHT_UAV_NOT_FOUND.getCode(), BizErrorCode.FLIGHT_UAV_NOT_FOUND.getDesc());
        }

        FlightUavDO flightUavDO = flightUavConvert.convertToDO(flightUavDTO);
        flightUavDO.setGmtModified(LocalDateTime.now());

        boolean success = flightUavRepository.updateById(flightUavDO);
        if (!success) {
            LogUtil.error("更新飞行无人机失败: {}", flightUavDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("更新飞行无人机成功: {}", flightUavDO.getId());
        return true;
    }

    @Override
    public FlightUavDTO getFlightUavById(Integer id) {
        if (id == null) {
            LogUtil.error("获取飞行无人机失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightUavDO flightUavDO = flightUavRepository.getById(id);
        if (flightUavDO == null) {
            LogUtil.error("获取飞行无人机失败，飞行无人机不存在: {}", id);
            return null;
        }

        return flightUavConvert.convert(flightUavDO);
    }

    @Override
    public FlightUavDTO getFlightUavByNo(String flightUavNo) {
        if (StringUtils.isBlank(flightUavNo)) {
            LogUtil.error("获取飞行无人机失败，无人机编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightUavDO flightUavDO = flightUavRepository.getOne(
                Wrappers.lambdaQuery(FlightUavDO.class)
                        .eq(FlightUavDO::getFlightUavNo, flightUavNo)
        );
        
        if (flightUavDO == null) {
            return null;
        }

        return flightUavConvert.convert(flightUavDO);
    }
    
    @Override
    public List<FlightUavDTO> getFlightUavListBySupplierUserNo(String supplierUserNo) {
        if (StringUtils.isBlank(supplierUserNo)) {
            LogUtil.error("获取飞行无人机列表失败，服务商编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        List<FlightUavDO> flightUavDOList = flightUavRepository.list(
                Wrappers.lambdaQuery(FlightUavDO.class)
                        .eq(FlightUavDO::getSupplierUserNo, supplierUserNo)
                        .orderByDesc(FlightUavDO::getGmtCreated)
        );
        
        return flightUavConvert.convertList(flightUavDOList);
    }

    @Override
    public List<FlightUavGroupVO> getUavGroupByModel(String supplierUserNo) {
        FlightUavGroupQueryDTO queryDTO = new FlightUavGroupQueryDTO();
        queryDTO.setSupplierUserNo(supplierUserNo);
        return getUavGroupByModel(queryDTO);
    }

    @Override
    public List<FlightUavGroupVO> getUavGroupByModel(FlightUavGroupQueryDTO queryDTO) {
        if (queryDTO == null || StringUtils.isBlank(queryDTO.getSupplierUserNo())) {
            LogUtil.error("获取按机型分组的飞行无人机列表失败，服务商编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        // 1. 获取该服务商下的所有飞行无人机
        List<FlightUavDTO> flightUavDTOList = getFlightUavListBySupplierUserNo(queryDTO.getSupplierUserNo());
        if (CollectionUtils.isEmpty(flightUavDTOList)) {
            return new ArrayList<>();
        }
        
        // 2. 转换成VO对象
        List<FlightUavVO> flightUavVOList = flightUavConvert.convertToVOList(flightUavDTOList);
        
        // 3. 获取所有涉及到的机型编号
        List<String> modelNoList = flightUavDTOList.stream()
                .map(FlightUavDTO::getFlightUavBmNo)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        
        if (modelNoList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 4. 查询所有涉及到的机型信息
        List<FlightUavBmDTO> modelList = flightUavBmService.listFlightUavBm(modelNoList);
        
        // 5. 根据查询条件筛选机型
        if (!CollectionUtils.isEmpty(modelList)) {
            if (StringUtils.isNotBlank(queryDTO.getFlightUavBmName())) {
                modelList = modelList.stream()
                        .filter(model -> model.getFlightUavBmName() != null && 
                                model.getFlightUavBmName().contains(queryDTO.getFlightUavBmName()))
                        .collect(Collectors.toList());
            }
            
            if (StringUtils.isNotBlank(queryDTO.getFlightUavBmModelNo())) {
                modelList = modelList.stream()
                        .filter(model -> model.getFlightUavBmModelNo() != null && 
                                model.getFlightUavBmModelNo().contains(queryDTO.getFlightUavBmModelNo()))
                        .collect(Collectors.toList());
            }
            
            // 获取满足条件的机型编号
            List<String> filteredModelNos = modelList.stream()
                    .map(FlightUavBmDTO::getFlightUavBmNo)
                    .collect(Collectors.toList());
            
            // 如果过滤后列表为空，直接返回空结果
            if (filteredModelNos.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 筛选出满足条件的无人机
            flightUavVOList = flightUavVOList.stream()
                    .filter(uav -> filteredModelNos.contains(uav.getFlightUavBmNo()))
                    .collect(Collectors.toList());
        }
        
        Map<String, FlightUavBmDTO> modelMap;
        if (!CollectionUtils.isEmpty(modelList)) {
            modelMap = modelList.stream()
                    .collect(Collectors.toMap(FlightUavBmDTO::getFlightUavBmNo, model -> model, (a, b) -> a));
        } else {
            modelMap = new HashMap<>();
        }

        // 6. 按机型分组飞行无人机
        Map<String, List<FlightUavVO>> groupedMap = flightUavVOList.stream()
                .filter(uav -> StringUtils.isNotBlank(uav.getFlightUavBmNo()))
                .collect(Collectors.groupingBy(FlightUavVO::getFlightUavBmNo));
        
        // 7. 构建分组结果
        List<FlightUavGroupVO> result = new ArrayList<>();
        groupedMap.forEach((modelNo, uavList) -> {
            FlightUavGroupVO groupVO = new FlightUavGroupVO();
            groupVO.setFlightUavBmNo(modelNo);
            groupVO.setUavList(uavList);
            
            // 设置所有机型信息
            FlightUavBmDTO modelDTO = modelMap.get(modelNo);
            if (modelDTO != null) {
                groupVO.setId(modelDTO.getId());
                groupVO.setFlightUavBmNo(modelDTO.getFlightUavBmNo());
                groupVO.setFlightUavBmDesc(modelDTO.getFlightUavBmDesc());
                groupVO.setFlightUavBmName(modelDTO.getFlightUavBmName());
                groupVO.setFlightUavBmModelNo(modelDTO.getFlightUavBmModelNo());
                groupVO.setFlightUavCrewType(modelDTO.getFlightUavCrewType());
                groupVO.setFlightUavWeightClassfication(modelDTO.getFlightUavWeightClassfication());
                groupVO.setFlightUavFlyType(modelDTO.getFlightUavFlyType());
                groupVO.setFlightUavMaxCarrierWeight(modelDTO.getFlightUavMaxCarrierWeight());
                groupVO.setFlightUavMaxFlyMinute(modelDTO.getFlightUavMaxFlyMinute());
                groupVO.setFlightUavMaxFlyRange(modelDTO.getFlightUavMaxFlyRange());
                groupVO.setFlightUavMaxFlyHeight(modelDTO.getFlightUavMaxFlyHeight());
                groupVO.setFlightUavRadius(modelDTO.getFlightUavRadius());
                groupVO.setFlightUavSupportVedio(modelDTO.getFlightUavSupportVedio());
                groupVO.setFlightUavCameraPixel(modelDTO.getFlightUavCameraPixel());
                groupVO.setFlightUavAppliedScenarios(modelDTO.getFlightUavAppliedScenarios());
                groupVO.setFlightUavPictures(modelDTO.getFlightUavPictures());
                groupVO.setTenantId(modelDTO.getTenantId());
            }
            
            result.add(groupVO);
        });
        
        return result;
    }

    @Override
    public CommonPage<FlightUavDTO> pageQueryFlightUav(FlightUavQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<FlightUavDO> queryWrapper = Wrappers.lambdaQuery(FlightUavDO.class)
                .eq(StringUtils.isNotBlank(queryDTO.getFlightUavNo()), FlightUavDO::getFlightUavNo, queryDTO.getFlightUavNo())
                .in(!CollectionUtils.isEmpty(queryDTO.getFlightUavNoList()), FlightUavDO::getFlightUavNo, queryDTO.getFlightUavNoList())
                .like(StringUtils.isNotBlank(queryDTO.getFlightUavName()), FlightUavDO::getFlightUavName, queryDTO.getFlightUavName())
                .eq(StringUtils.isNotBlank(queryDTO.getFlightStationNo()), FlightUavDO::getFlightStationNo, queryDTO.getFlightStationNo())
                .eq(StringUtils.isNotBlank(queryDTO.getFlightUavSn()), FlightUavDO::getFlightUavSn, queryDTO.getFlightUavSn())
                .eq(StringUtils.isNotBlank(queryDTO.getFlightUavBmNo()), FlightUavDO::getFlightUavBmNo, queryDTO.getFlightUavBmNo())
                .eq(StringUtils.isNotBlank(queryDTO.getSupplierUserNo()), FlightUavDO::getSupplierUserNo, queryDTO.getSupplierUserNo())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), FlightUavDO::getTenantId, queryDTO.getTenantId())
                .orderByDesc(FlightUavDO::getGmtCreated);

        List<FlightUavDO> flightUavDOList = flightUavRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(flightUavDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<FlightUavDTO> flightUavDTOList = flightUavConvert.convertList(flightUavDOList);
        PageInfo<FlightUavDTO> pageInfo = new PageInfo<>(flightUavDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), flightUavDTOList);
    }

    @Override
    public boolean deleteFlightUav(Integer id) {
        if (id == null) {
            LogUtil.error("删除飞行无人机失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查飞行无人机是否存在
        FlightUavDO existFlightUav = flightUavRepository.getById(id);
        if (existFlightUav == null) {
            LogUtil.error("删除飞行无人机失败，飞行无人机不存在: {}", id);
            return false;
        }

        boolean success = flightUavRepository.removeById(id);
        if (!success) {
            LogUtil.error("删除飞行无人机失败: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("删除飞行无人机成功: {}", id);
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchOperateFlightUavs(FlightUavBatchOperationDTO batchOperationDTO) {
        if (batchOperationDTO == null) {
            LogUtil.error("批量操作飞行无人机失败，参数为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        String supplierUserNo = batchOperationDTO.getSupplierUserNo();
        if (StringUtils.isBlank(supplierUserNo)) {
            LogUtil.error("批量操作飞行无人机失败，服务商编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        // 处理删除操作
        List<Integer> deleteIds = batchOperationDTO.getDeleteIds();
        if (!CollectionUtils.isEmpty(deleteIds)) {
            for (Integer id : deleteIds) {
                try {
                    // 检查是否为当前服务商的无人机
                    FlightUavDO flightUavDO = flightUavRepository.getById(id);
                    if (flightUavDO != null && supplierUserNo.equals(flightUavDO.getSupplierUserNo())) {
                        boolean success = flightUavRepository.removeById(id);
                        if (!success) {
                            LogUtil.error("批量删除飞行无人机失败，ID: {}", id);
                            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "删除飞行无人机失败，ID: " + id);
                        }
                    } else {
                        LogUtil.error("批量删除飞行无人机失败，无权限或不存在，ID: {}", id);
                    }
                } catch (Exception e) {
                    LogUtil.error("批量删除飞行无人机出错，ID: {}, 错误: {}", id, e.getMessage());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "删除飞行无人机失败，ID: " + id);
                }
            }
        }
        
        // 处理更新操作
        List<FlightUavDTO> updateItems = batchOperationDTO.getUpdateItems();
        if (!CollectionUtils.isEmpty(updateItems)) {
            // 收集所有SN码用于检查
            List<String> snList = new ArrayList<>();
            Map<String, Integer> snIdMap = new HashMap<>();
            
            for (FlightUavDTO item : updateItems) {
                if (item.getId() == null) {
                    LogUtil.error("批量更新飞行无人机失败，缺少ID: {}", item.getFlightUavSn());
                    throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "更新飞行无人机缺少ID");
                }
                
                // 检查是否为当前服务商的无人机
                FlightUavDO existFlightUav = flightUavRepository.getById(item.getId());
                if (existFlightUav == null) {
                    LogUtil.error("批量更新飞行无人机失败，无人机不存在: {}", item.getId());
                    throw new BizException(BizErrorCode.NOT_FOUND.getCode(), "飞行无人机不存在，ID: " + item.getId());
                }
                
                if (!supplierUserNo.equals(existFlightUav.getSupplierUserNo())) {
                    LogUtil.error("批量更新飞行无人机失败，无权限: {}", item.getId());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "无权限更新该飞行无人机，ID: " + item.getId());
                }
                
                // 如果更新了SN码，需要检查重复
                String sn = item.getFlightUavSn();
                if (StringUtils.isNotBlank(sn) && !sn.equals(existFlightUav.getFlightUavSn())) {
                    if (snList.contains(sn)) {
                        LogUtil.error("批量更新飞行无人机失败，请求中存在重复的SN码: {}", sn);
                        throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请求中存在重复的SN码: " + sn);
                    }
                    snList.add(sn);
                    snIdMap.put(sn, item.getId());
                }
            }
            
            // 检查SN码是否已存在于数据库（排除自身ID）
            if (!snList.isEmpty()) {
                List<FlightUavDO> existingUavs = flightUavRepository.list(
                        Wrappers.lambdaQuery(FlightUavDO.class)
                                .in(FlightUavDO::getFlightUavSn, snList)
                );
                
                if (!CollectionUtils.isEmpty(existingUavs)) {
                    for (FlightUavDO existingUav : existingUavs) {
                        String existingSn = existingUav.getFlightUavSn();
                        Integer updateId = snIdMap.get(existingSn);
                        
                        // 如果存在SN码相同但ID不同的记录，说明SN码冲突
                        if (!existingUav.getId().equals(updateId)) {
                            LogUtil.error("批量更新飞行无人机失败，SN码已存在: {}", existingSn);
                            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "SN码已存在: " + existingSn);
                        }
                    }
                }
            }
            
            // 执行更新操作
            for (FlightUavDTO item : updateItems) {
                try {
                    // 确保设置了服务商编号
                    item.setSupplierUserNo(supplierUserNo);
                    boolean success = updateFlightUav(item);
                    if (!success) {
                        LogUtil.error("批量更新飞行无人机失败，ID: {}", item.getId());
                        throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "更新飞行无人机失败，ID: " + item.getId());
                    }
                } catch (Exception e) {
                    LogUtil.error("批量更新飞行无人机出错，ID: {}, 错误: {}", item.getId(), e.getMessage());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "更新飞行无人机失败，ID: " + item.getId());
                }
            }
        }
        
        // 处理新增操作
        List<FlightUavDTO> createItems = batchOperationDTO.getCreateItems();
        if (!CollectionUtils.isEmpty(createItems)) {
            List<String> snList = new ArrayList<>();
            
            // 先检查SN码是否重复
            for (FlightUavDTO item : createItems) {
                // 设置租户id
                item.setTenantId(TenantIdUtil.getTenantId());
                String sn = item.getFlightUavSn();
                if (StringUtils.isNotBlank(sn)) {
                    if (snList.contains(sn)) {
                        LogUtil.error("批量新增飞行无人机失败，请求中存在重复的SN码: {}", sn);
                        throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请求中存在重复的SN码: " + sn);
                    }
                    snList.add(sn);
                }
            }
            
            // 检查SN码是否已存在于数据库
            if (!snList.isEmpty()) {
                List<FlightUavDO> existingUavs = flightUavRepository.list(
                        Wrappers.lambdaQuery(FlightUavDO.class)
                                .eq(FlightUavDO::getSupplierUserNo, supplierUserNo)
                                .in(FlightUavDO::getFlightUavSn, snList)
                );
                
                if (!CollectionUtils.isEmpty(existingUavs)) {
                    String existingSn = existingUavs.get(0).getFlightUavSn();
                    LogUtil.error("批量新增飞行无人机失败，SN码已存在: {}", existingSn);
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "SN码已存在: " + existingSn);
                }
            }
            
            // 执行新增操作
            for (FlightUavDTO item : createItems) {
                try {
                    // 确保设置了服务商编号
                    item.setSupplierUserNo(supplierUserNo);
                    saveFlightUav(item);
                } catch (Exception e) {
                    LogUtil.error("批量新增飞行无人机出错，SN: {}, 错误: {}", item.getFlightUavSn(), e.getMessage());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "新增飞行无人机失败，SN: " + item.getFlightUavSn());
                }
            }
        }
        
        return true;
    }

    /**
     * 校验飞行无人机参数
     *
     * @param flightUavDTO 飞行无人机DTO
     */
    private void validateFlightUavParams(FlightUavDTO flightUavDTO) {
        // UAV06: 校验机型是否选择
        if (StringUtils.isBlank(flightUavDTO.getFlightUavBmNo())) {
            LogUtil.error("飞行无人机校验失败，未选择机型");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请选择一个机型");
        }
        
        // UAV12: 校验飞行器名称
        if (StringUtils.isBlank(flightUavDTO.getFlightUavName())) {
            LogUtil.error("飞行无人机校验失败，飞行器名称为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入飞行器名称");
        }
        
        // UAV13: 校验飞行器名称长度
        if (flightUavDTO.getFlightUavName().length() > 30) {
            LogUtil.error("飞行无人机校验失败，飞行器名称过长: {}", flightUavDTO.getFlightUavName());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "飞行器名称不能超过30字符");
        }
        
        // UAV15: 校验唯一产品识别码
        if (StringUtils.isBlank(flightUavDTO.getFlightUavSn())) {
            LogUtil.error("飞行无人机校验失败，唯一识别码为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入唯一识别码");
        }
        
        // UAV17: 校验唯一产品识别码长度
        if (flightUavDTO.getFlightUavSn().length() > 30) {
            LogUtil.error("飞行无人机校验失败，唯一识别码过长: {}", flightUavDTO.getFlightUavSn());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "识别码不能超过30字符");
        }
        
        // UAV16: 校验唯一产品识别码是否重复
        FlightUavDO existFlightUav = flightUavRepository.getOne(
                Wrappers.lambdaQuery(FlightUavDO.class)
                        .eq(FlightUavDO::getFlightUavSn, flightUavDTO.getFlightUavSn())
                        .eq(FlightUavDO::getIsDeleted, false)
        );
        
        // 如果是更新操作，需要排除自身ID
        if (existFlightUav != null && 
            (flightUavDTO.getId() == null || !flightUavDTO.getId().equals(existFlightUav.getId()))) {
            LogUtil.error("飞行无人机校验失败，唯一识别码已存在: {}", flightUavDTO.getFlightUavSn());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "该识别码已存在！");
        }
    }
} 