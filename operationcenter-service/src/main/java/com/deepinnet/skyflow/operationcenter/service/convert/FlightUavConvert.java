package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavDO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavDTO;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 飞行无人机数据转换
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface FlightUavConvert {

    FlightUavConvert INSTANCE = Mappers.getMapper(FlightUavConvert.class);

    /**
     * DO 转 DTO
     *
     * @param flightUavDO 飞行无人机DO
     * @return 飞行无人机DTO
     */
    FlightUavDTO convert(FlightUavDO flightUavDO);

    /**
     * DTO 转 DO
     *
     * @param flightUavDTO 飞行无人机DTO
     * @return 飞行无人机DO
     */
    @Mapping(target = "gmtModified", ignore = true)
    FlightUavDO convertToDO(FlightUavDTO flightUavDTO);

    /**
     * DTO 转 VO
     *
     * @param flightUavDTO 飞行无人机DTO
     * @return 飞行无人机VO
     */
    FlightUavVO convertToVO(FlightUavDTO flightUavDTO);

    /**
     * DTO列表转换为VO列表
     *
     * @param flightUavDTOList 飞行无人机DTO列表
     * @return 飞行无人机VO列表
     */
    List<FlightUavVO> convertToVOList(List<FlightUavDTO> flightUavDTOList);

    /**
     * DO列表转换为DTO列表
     *
     * @param flightUavDOList 飞行无人机DO列表
     * @return 飞行无人机DTO列表
     */
    List<FlightUavDTO> convertList(List<FlightUavDO> flightUavDOList);

} 