package com.deepinnet.skyflow.operationcenter.service.demand;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.DemandSyncPlanBindDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandQueryDTO;

/**
 * 飞行需求服务接口
 *
 * <AUTHOR>
 */
public interface FlightDemandService {

    /**
     * 保存飞行需求
     *
     * @param flightDemandDTO 飞行需求数据
     * @return 已保存的飞行需求编码
     */
    String saveFlightDemand(FlightDemandDTO flightDemandDTO);

    /**
     * 根据需求编号获取飞行需求
     *
     * @param demandNo 需求编号
     * @return 飞行需求数据
     */
    FlightDemandDTO getFlightDemandByNo(String demandNo);

    /**
     * 分页查询飞行需求
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    CommonPage<FlightDemandDTO> pageQueryFlightDemand(FlightDemandQueryDTO queryDTO);

    /**
     * 更新飞行需求匹配状态
     *
     * @param flightDemandDTO
     */
    void updateFlightDemandStatus(FlightDemandDTO flightDemandDTO);

    /**
     * 保存飞行需求与计划绑定关系
     *
     * @param relationDTO
     * @return
     */
    Boolean saveDemandPlanBind(DemandSyncPlanBindDTO relationDTO);

    /**
     * 根据计划ID获取飞行需求
     *
     * @param planId
     * @return
     */
    FlightDemandDTO getFlightDemandByPlanId(String planId);
}