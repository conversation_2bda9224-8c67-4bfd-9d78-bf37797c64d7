package com.deepinnet.skyflow.operationcenter.service.flow.context;

import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *   订单查询context
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderApproveContext {

    private String orderNo;

    private String userNo;

    private String approvalUserNo;

    private String approvalUserName;

    private String remark;

    private String tenantId;

    private String approveStatus;

    private String orderType;

    private FlightOrderVO flightOrder;

}
