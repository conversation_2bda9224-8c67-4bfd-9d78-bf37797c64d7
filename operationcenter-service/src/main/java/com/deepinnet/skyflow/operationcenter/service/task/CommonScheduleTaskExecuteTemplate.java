package com.deepinnet.skyflow.operationcenter.service.task;


import cn.hutool.json.JSONUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ScheduleTaskDO;
import com.deepinnet.skyflow.operationcenter.dal.enums.ScheduleTaskTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.ScheduleTaskStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.context.TenantContext;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.task.worker.AbstractBaseTaskWorker;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR> wong
 * @create 2022-11-30 21:18
 * <p>
 * 定时任务执行通用模板类
 */

@Component
public class CommonScheduleTaskExecuteTemplate implements InitializingBean {

    @Resource(name = "scheduleTaskExecutor")
    private ThreadPoolTaskExecutor executor;

    @Resource
    private ScheduleTaskService scheduleTaskService;

    @Resource
    private Map<String, AbstractBaseTaskWorker> taskWorkerMap;

    private static final Integer LIMIT_COUNT = 30;

    private final Map<ScheduleTaskTypeEnum, AbstractBaseTaskWorker> scheduleTypeWorkerMap = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() {
        taskWorkerMap
                .values()
                .parallelStream()
                .forEach(taskWorker -> scheduleTypeWorkerMap.put(taskWorker.getTaskType(), taskWorker));
    }

    @Scheduled(fixedDelay = 60000)
    public void process() {
        try {
            TenantContext.disableTenantLine();
            // 捞取任务
            List<ScheduleTaskDO> allNeedExecuteTasks = getNeedExecuteTasks();
            if (CollectionUtils.isEmpty(allNeedExecuteTasks)) {
                return;
            }

            // 将任务提交给线程池处理
            for (ScheduleTaskDO needExecuteTask : allNeedExecuteTasks) {
                // 塞入tenant-id
                TenantContextHolder.setTenantId(needExecuteTask.getTenantId());
                ScheduleTaskTypeEnum taskTypeEnum = ScheduleTaskTypeEnum.getByType(needExecuteTask.getTaskType());
                if (taskTypeEnum == null) {
                    LogUtil.error("定时任务模板类处理异常，原因：任务类型不存在，任务id:{}", needExecuteTask.getId());
                    throw new BizException(BizErrorCode.ILLEGAL_SCHEDULE_TASK_TYPE.getCode(), BizErrorCode.ILLEGAL_SCHEDULE_TASK_TYPE.getDesc());
                }

                AbstractBaseTaskWorker taskWorker = scheduleTypeWorkerMap.get(taskTypeEnum);
                if (taskWorker == null) {
                    LogUtil.error("无法找到当前任务类型对应的worker，请及时排查问题，当前的任务为：{}", JSONUtil.toJsonStr(needExecuteTask));
                    continue;
                }

                executor.submit(() -> taskWorker.doCommit(needExecuteTask));
            }
        } finally {
            TenantContext.clear();
        }


    }

    private List<ScheduleTaskDO> getNeedExecuteTasks() {
        return scheduleTaskService.listTasks(
                ScheduleTaskTypeEnum.SUPPORT_TASK_TYPE_LIST,
                Lists.newArrayList(ScheduleTaskStatusEnum.WAIT_EXECUTE.getStatus(), ScheduleTaskStatusEnum.FAIL.getStatus()),
                System.currentTimeMillis(),
                LIMIT_COUNT);
    }
}
