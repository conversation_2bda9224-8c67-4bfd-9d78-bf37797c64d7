package com.deepinnet.skyflow.operationcenter.service.event;

import com.deepinnet.skyflow.operationcenter.dto.FlightEventsDTO;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Description:
 * Date: 2025/5/9
 * Author: lijunheng
 */
@Getter
public class FlightMonitorEventCreateEvent extends ApplicationEvent {

    private FlightEventsDTO flightEventsDTO;

    public FlightMonitorEventCreateEvent(FlightEventsDTO flightEventsDTO) {
        super(flightEventsDTO);
        this.flightEventsDTO = flightEventsDTO;
    }
}
