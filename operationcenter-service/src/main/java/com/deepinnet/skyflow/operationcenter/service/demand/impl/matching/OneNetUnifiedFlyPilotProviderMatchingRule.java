package com.deepinnet.skyflow.operationcenter.service.demand.impl.matching;

import cn.hutool.core.map.MapUtil;
import com.deepinnet.infra.api.enums.FlightServiceTypeEnum;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Description:
 * Date: 2025/4/19
 * Author: lijunheng
 */
@Component
public class OneNetUnifiedFlyPilotProviderMatchingRule {

    @Resource
    private DynamicSqlInvokeHelper dynamicSqlInvokeHelper;

    public String matchProvider(FlightDemandDTO demand) {
        //判断是否有符合条件的服务商
        DynamicSqlEntity queryAllMatchSqlEntity = queryAllMatchingProviders(demand);
        boolean exist = dynamicSqlInvokeHelper.exist(queryAllMatchSqlEntity);
        if (!exist) {
            return null;
        }
        DynamicSqlEntity filterWhiteSql = filterWhiteListProviders(queryAllMatchSqlEntity, demand);
        DynamicSqlEntity bestMatchProviderSql = findDemandLeastProvider(filterWhiteSql);
        Map bestMatchProvider = dynamicSqlInvokeHelper.queryForObject(bestMatchProviderSql, Map.class);
        if (MapUtil.isEmpty(bestMatchProvider)) {
            //直接找非白名单内的
            DynamicSqlEntity demandLeastProvider = findDemandLeastProvider(queryAllMatchSqlEntity);
            bestMatchProvider = dynamicSqlInvokeHelper.queryForObject(demandLeastProvider, Map.class);
        }
        return (String) bestMatchProvider.get("serviceProviderNo");
    }

    private DynamicSqlEntity queryAllMatchingProviders(FlightDemandDTO demand) {
        DynamicSqlEntity dynamicSqlEntity = new DynamicSqlEntity();
        dynamicSqlEntity.setFrom("from supplier_flight_service supplier");

        List<String> joinOnList = dynamicSqlEntity.getJoinOnList();
        joinOnList.add("join product_service_supplier pss on pss.user_no = supplier.user_no");

        List<String> whereList = dynamicSqlEntity.getWhereList();
        whereList.add("supplier.service_code = :serviceCode");
        whereList.add("supplier.tenant_id = :tenantId and pss.tenant_id = :tenantId");
        whereList.add("pss.approval_status = 1");
        whereList.add("pss.is_deleted = :deleted");

        // 添加参数值
        dynamicSqlEntity.addParameter("serviceCode", FlightServiceTypeEnum.DRONE_PILOT.getCode());
        dynamicSqlEntity.addParameter("tenantId", demand.getTenantId());
        dynamicSqlEntity.addParameter("deleted", false);

        return dynamicSqlEntity;
    }

    /**
     * 找到白名单中最满足需求的服务商
     *
     * @return
     */
    private DynamicSqlEntity filterWhiteListProviders(DynamicSqlEntity dynamicSqlEntity, FlightDemandDTO demand) {
        //将第一步查询出来的所有满足条件的服务商于白名单进行对比
        DynamicSqlEntity filterWhiteSqlEntity = new DynamicSqlEntity(dynamicSqlEntity);

        if (!demand.getIsMergeDemand()) {
            List<String> joinOnList = filterWhiteSqlEntity.getJoinOnList();
            joinOnList.add("join white_list white on white.supplier_user_no = supplier.user_no");
            List<String> whereList = filterWhiteSqlEntity.getWhereList();

            // 使用预处理参数
            whereList.add("white.customer_user_no = :publisherNo");
            filterWhiteSqlEntity.addParameter("publisherNo", demand.getPublisherNo());

            whereList.add("white.tenant_id = :tenantId and white.is_deleted = :deleted");
        }

        return filterWhiteSqlEntity;
    }

    /**
     * 有多个服务商，选择需求数量最少的服务商进行需求匹配
     *
     * @return
     */
    private DynamicSqlEntity findDemandLeastProvider(DynamicSqlEntity dynamicSqlEntity) {
        //选择需求数量最少的服务商
        DynamicSqlEntity findBestMatchingSqlEntity = new DynamicSqlEntity(dynamicSqlEntity);
        List<String> joinOnList = findBestMatchingSqlEntity.getJoinOnList();
        joinOnList.add("left join flight_demand_match_service_provider fdmsp " +
                "on fdmsp.service_provider_no = supplier.user_no " +
                "and fdmsp.tenant_id = :tenantId");
        findBestMatchingSqlEntity.setGroupBy("group by supplier.user_no");
        findBestMatchingSqlEntity.setSelect("select supplier.user_no as serviceProviderNo, count(distinct fdmsp.id) as num");
        findBestMatchingSqlEntity.setOrderBy("order by num asc");
        findBestMatchingSqlEntity.setLimit("limit 1");
        return findBestMatchingSqlEntity;
    }
}
