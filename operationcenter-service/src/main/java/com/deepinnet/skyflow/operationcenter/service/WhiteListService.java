package com.deepinnet.skyflow.operationcenter.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListBatchOperationDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListGroupQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListBatchReplaceDTO;
import com.deepinnet.skyflow.operationcenter.vo.WhiteListGroupVO;

/**
 * 白名单服务接口
 *
 * <AUTHOR>
 */
public interface WhiteListService {

    /**
     * 保存白名单
     *
     * @param whiteListDTO 白名单数据
     * @return 已保存的白名单ID
     */
    Integer saveWhiteList(WhiteListDTO whiteListDTO);

    /**
     * 更新白名单
     *
     * @param whiteListDTO 白名单数据
     * @return 更新是否成功
     */
    boolean updateWhiteList(WhiteListDTO whiteListDTO);

    /**
     * 根据ID获取白名单
     *
     * @param id 白名单ID
     * @return 白名单数据
     */
    WhiteListDTO getWhiteListById(Integer id);

    /**
     * 根据客户编号和服务商编号获取白名单
     *
     * @param customerUserNo 客户编号
     * @param supplierUserNo 服务商编号
     * @return 白名单数据
     */
    WhiteListDTO getWhiteListByCustomerAndSupplier(String customerUserNo, String supplierUserNo);

    /**
     * 分页查询白名单
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    CommonPage<WhiteListDTO> pageQueryWhiteList(WhiteListQueryDTO queryDTO);
    
    /**
     * 按客户分组分页查询白名单
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    CommonPage<WhiteListGroupVO> pageQueryWhiteListGroupByCustomer(WhiteListGroupQueryDTO queryDTO);

    /**
     * 删除白名单
     *
     * @param id 白名单ID
     * @return 删除是否成功
     */
    boolean deleteWhiteList(Integer id);
    
    /**
     * 批量操作白名单（删除、新增和更新）
     *
     * @param batchOperationDTO 批量操作数据
     * @return 操作是否成功
     */
    boolean batchOperateWhiteLists(WhiteListBatchOperationDTO batchOperationDTO);
    
    /**
     * 按客户编号批量覆盖白名单（先删除该客户所有记录，再批量新增）
     *
     * @param batchReplaceDTO 批量覆盖数据
     * @return 操作是否成功
     */
    boolean batchReplaceWhiteListsByCustomer(WhiteListBatchReplaceDTO batchReplaceDTO);
} 