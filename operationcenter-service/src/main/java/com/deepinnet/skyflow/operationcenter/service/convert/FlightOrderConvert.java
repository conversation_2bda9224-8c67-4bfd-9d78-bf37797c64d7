package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.*;
import com.deepinnet.skyflow.operationcenter.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 飞行订单转换接口
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface FlightOrderConvert {

    List<FlightOrderApprovalVO> convertToFlightOrderApprovalListVO(List<FlightOrderApprovalDO> doList);
    
    FlightOrderApprovalVO convertToFlightOrderApprovalVO(FlightOrderApprovalDO flightOrderApprovalDO);

    @Mapping(source = "validityPeriodStart", target = "validityPeriodStart", qualifiedByName = "localDateTimeToLong")
    @Mapping(source = "validityPeriodEnd", target = "validityPeriodEnd", qualifiedByName = "localDateTimeToLong")
    FlightOrderFlyingInfoVO convertToFlightOrderFlyingInfoVO(FlightOrderFlyingInfoDO orderFlyingInfoDO);
    
    List<FlightOrderFileVO> convertToFlightOrderFileList(List<FlightOrderFileDO> flightOrderFileDOList);
    
    FlightOrderFileVO convertToFlightOrderFileVO(FlightOrderFileDO flightOrderFileDO);

    List<FlightOrderVO> convertToFlightOrderVOList(List<FlightOrderDO> flightOrderDOList);

    /**
     * DO 转 VO
     *
     * @param flightOrderDO DO 对象
     * @return VO 对象
     */
    @Mapping(source = "orderTime", target = "orderTime", qualifiedByName = "localDateTimeToLong")
    FlightOrderVO convertToVO(FlightOrderDO flightOrderDO);

    /**
     * DO 转 ProductUsageVO
     *
     * @param flightOrderProductUsageDO DO 对象
     * @return ProductUsageVO 对象
     */
    @Mapping(source = "validityPeriodStart", target = "validityPeriodStart", qualifiedByName = "localDateTimeToLong")
    @Mapping(source = "validityPeriodEnd", target = "validityPeriodEnd", qualifiedByName = "localDateTimeToLong")
    FlightOrderProductUsageVO convertToProductUsageVO(FlightOrderProductUsageDO flightOrderProductUsageDO);

    /**
     * DO List 转 ProductUsageVO List
     *
     * @param flightOrderProductUsageDOList DO 列表
     * @return ProductUsageVO 列表
     */
    List<FlightOrderProductUsageVO> convertToProductUsageVOList(List<FlightOrderProductUsageDO> flightOrderProductUsageDOList);

    @Named("localDateTimeToLong")
    default Long localDateTimeToLong(LocalDateTime time) {
        return time == null ? null : time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
} 