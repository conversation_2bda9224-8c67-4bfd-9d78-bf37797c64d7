package com.deepinnet.skyflow.operationcenter.service.infra.util;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * Description:
 * Date: 2024/11/19
 * Author: lijunheng
 */
@AllArgsConstructor
@Getter
public enum CoordinateReferenceSystemNameEnum {

    WEB_MERCATOR("EPSG:3857", 3857),
    WGS_84("EPSG:4326", 4326),
    GRS_80("EPSG:4490", 4490),
    ;

    private final String crsName;
    private final Integer srid;


    public static CoordinateReferenceSystemNameEnum getByCrsName(String crsName) {
        if (StrUtil.isBlank(crsName)) {
            throw new IllegalArgumentException("CRSName cannot be null or empty");
        }

        for (CoordinateReferenceSystemNameEnum value : CoordinateReferenceSystemNameEnum.values()) {
            if (Objects.equals(value.getCrsName(), crsName)) {
                return value;
            }
        }

        throw new IllegalArgumentException("No matching CRS found for name: " + crsName);
    }

}
