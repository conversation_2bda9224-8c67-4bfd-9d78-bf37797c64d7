package com.deepinnet.skyflow.operationcenter.service.validator;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import org.springframework.stereotype.Component;

/**
 * Description:
 * Date: 2025/4/27
 * Author: lijunheng
 */
@Component
public class EmergencyResponseFlightDemandValidator implements FlightDemandValidator {

    @Override
    public boolean supports(FlightDemandTypeEnum typeEnum) {
        return FlightDemandTypeEnum.EMERGENCY_RESPONSE == typeEnum;
    }

    @Override
    public void validate(FlightDemandDTO demand) {
    }
}
