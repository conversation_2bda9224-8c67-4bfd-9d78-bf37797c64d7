package com.deepinnet.skyflow.operationcenter.service.product;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportDetailDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportRecordDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportRequestDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportResultDTO;
import com.deepinnet.skyflow.operationcenter.dto.PageQueryDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 飞行无人机品牌型号导入服务接口
 *
 * <AUTHOR>
 */
public interface FlightUavBmImportService {

    /**
     * 导入飞行无人机品牌型号
     *
     * @param file 文件
     * @param requestDTO 请求DTO
     * @return 导入结果
     */
    FlightUavBmImportResultDTO importFlightUavBm(MultipartFile file, FlightUavBmImportRequestDTO requestDTO);

    /**
     * 获取导入记录
     *
     * @param importBatchNo 导入批次号
     * @return 导入记录
     */
    FlightUavBmImportRecordDTO getImportRecord(String importBatchNo);

    /**
     * 分页查询导入记录
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    CommonPage<FlightUavBmImportRecordDTO> pageQueryImportRecord(PageQueryDTO queryDTO);

    /**
     * 获取导入详情
     *
     * @param importBatchNo 导入批次号
     * @return 导入详情列表
     */
    List<FlightUavBmImportDetailDTO> listImportDetail(String importBatchNo);

    /**
     * 分页查询导入详情
     *
     * @param importBatchNo 导入批次号
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    CommonPage<FlightUavBmImportDetailDTO> pageQueryImportDetail(String importBatchNo, PageQueryDTO queryDTO);
} 