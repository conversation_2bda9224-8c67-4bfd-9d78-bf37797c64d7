package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightPlanPageQuery;
import com.deepinnet.skyflow.operationcenter.dto.FlightPlanPageQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * <p>
 * 飞行数据转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface FlightConverter {

    FlightPlanPageQuery toFlightPlanPageQuery(FlightPlanPageQueryDTO dto);
    
    /**
     * 将 FlightPlanDO 转换为 FlightPlanVO
     *
     * @param planDO 飞行计划DO
     * @return FlightPlanVO 飞行计划VO
     */
    @Mapping(source = "missionId", target = "missionId")
    @Mapping(source = "planId", target = "planId")
    @Mapping(source = "status", target = "status")
    @Mapping(source = "planedTakeoffTime", target = "planedTakeoffTime")
    @Mapping(source = "planedLandingTime", target = "planedLandingTime")
    @Mapping(source = "operatorUser", target = "operatorUser")
    @Mapping(source = "operatorPhone", target = "operatorPhone")
    @Mapping(target = "realtimeUavInfoList", ignore = true)
    @Mapping(target = "uavInfo", ignore = true)
    @Mapping(target = "landingAerodrome", ignore = true)
    FlightPlanVO toFlightPlanVO(FlightPlanDO planDO);
    
    /**
     * 将 FlightPlanDO 列表转换为 FlightPlanVO 列表
     *
     * @param planDOList 飞行计划DO列表
     * @return List<FlightPlanVO> 飞行计划VO列表
     */
    List<FlightPlanVO> toFlightPlanVOList(List<FlightPlanDO> planDOList);
} 