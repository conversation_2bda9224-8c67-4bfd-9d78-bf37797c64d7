package com.deepinnet.skyflow.operationcenter.service.infra.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.DictionaryDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.DictionaryRepository;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryDTO;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.infra.DictionaryItemService;
import com.deepinnet.skyflow.operationcenter.service.infra.DictionaryService;
import com.deepinnet.skyflow.operationcenter.service.convert.DictionaryConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 字典服务实现类
 *
 * <AUTHOR>
 */
@Service
public class DictionaryServiceImpl implements DictionaryService {

    @Resource
    private DictionaryRepository dictionaryRepository;

    @Resource
    private DictionaryItemService dictionaryItemService;

    @Resource
    private DictionaryConvert dictionaryConvert;

    @Override
    public Long saveDictionary(DictionaryDTO dictionaryDTO) {
        dictionaryDTO.setId(null);
        // 检查是否已存在同名字典
        DictionaryDO existDictionary = dictionaryRepository.getOne(
                Wrappers.lambdaQuery(DictionaryDO.class)
                        .eq(DictionaryDO::getName, dictionaryDTO.getName())
                        .eq(StringUtils.isNotBlank(dictionaryDTO.getTenantId()), DictionaryDO::getTenantId, dictionaryDTO.getTenantId())
        );
        if (existDictionary != null) {
            LogUtil.error("保存字典失败，字典名称已存在: {}", dictionaryDTO.getName());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典名称已存在");
        }

        DictionaryDO dictionaryDO = dictionaryConvert.convertToDO(dictionaryDTO);
        dictionaryDO.setGmtCreated(LocalDateTime.now());
        dictionaryDO.setGmtModified(LocalDateTime.now());
        dictionaryDO.setIsDeleted(0);
        
        boolean success = dictionaryRepository.save(dictionaryDO);
        if (!success) {
            LogUtil.error("保存字典失败: {}", dictionaryDTO.getName());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        LogUtil.info("保存字典成功: {}", dictionaryDO.getId());
        return dictionaryDO.getId();
    }

    @Override
    public boolean updateDictionary(DictionaryDTO dictionaryDTO) {
        if (dictionaryDTO.getId() == null) {
            LogUtil.error("更新字典失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查字典是否存在
        DictionaryDO existDictionary = dictionaryRepository.getById(dictionaryDTO.getId());
        if (existDictionary == null) {
            LogUtil.error("更新字典失败，字典不存在: {}", dictionaryDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典不存在");
        }

        // 检查是否存在同名字典
        if (!existDictionary.getName().equals(dictionaryDTO.getName())) {
            DictionaryDO conflictDictionary = dictionaryRepository.getOne(
                    Wrappers.lambdaQuery(DictionaryDO.class)
                            .eq(DictionaryDO::getName, dictionaryDTO.getName())
                            .eq(StringUtils.isNotBlank(dictionaryDTO.getTenantId()), DictionaryDO::getTenantId, dictionaryDTO.getTenantId())
                            .ne(DictionaryDO::getId, dictionaryDTO.getId())
            );
            if (conflictDictionary != null) {
                LogUtil.error("更新字典失败，字典名称已被其他字典使用: {}", dictionaryDTO.getName());
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典名称已存在");
            }
        }

        DictionaryDO dictionaryDO = dictionaryConvert.convertToDO(dictionaryDTO);
        dictionaryDO.setGmtModified(LocalDateTime.now());
        
        boolean success = dictionaryRepository.updateById(dictionaryDO);
        if (!success) {
            LogUtil.error("更新字典失败: {}", dictionaryDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        LogUtil.info("更新字典成功: {}", dictionaryDO.getId());
        return true;
    }

    @Override
    public DictionaryDTO getDictionaryById(Long id) {
        if (id == null) {
            LogUtil.error("获取字典失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        DictionaryDO dictionaryDO = dictionaryRepository.getById(id);
        if (dictionaryDO == null) {
            LogUtil.error("获取字典失败，字典不存在: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典不存在");
        }

        return dictionaryConvert.convert(dictionaryDO);
    }

    @Override
    public CommonPage<DictionaryDTO> pageQueryDictionary(DictionaryQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<DictionaryDO> queryWrapper = Wrappers.lambdaQuery(DictionaryDO.class)
                .eq(queryDTO.getDictionaryId() != null, DictionaryDO::getDictionaryId, queryDTO.getDictionaryId())
                .like(StringUtils.isNotBlank(queryDTO.getName()), DictionaryDO::getName, queryDTO.getName())
                .eq(StringUtils.isNotBlank(queryDTO.getType()), DictionaryDO::getType, queryDTO.getType())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), DictionaryDO::getTenantId, queryDTO.getTenantId())
                .orderByDesc(DictionaryDO::getGmtCreated);

        List<DictionaryDO> dictionaryDOList = dictionaryRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(dictionaryDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<DictionaryDTO> dictionaryDTOList = dictionaryConvert.convertList(dictionaryDOList);
        PageInfo<DictionaryDTO> pageInfo = new PageInfo<>(dictionaryDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), dictionaryDTOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDictionary(Long id) {
        if (id == null) {
            LogUtil.error("删除字典失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查字典是否存在
        DictionaryDO existDictionary = dictionaryRepository.getById(id);
        if (existDictionary == null) {
            LogUtil.error("删除字典失败，字典不存在: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "字典不存在");
        }

        // 先删除字典项
        dictionaryItemService.deleteDictionaryItemsByDictionaryId(id);

        // 再删除字典
        boolean success = dictionaryRepository.removeById(id);
        if (!success) {
            LogUtil.error("删除字典失败: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("删除字典成功: {}", id);
        return true;
    }
} 