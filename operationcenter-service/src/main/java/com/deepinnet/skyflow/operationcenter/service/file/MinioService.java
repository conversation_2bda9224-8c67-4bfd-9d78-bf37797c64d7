package com.deepinnet.skyflow.operationcenter.service.file;

import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * MinIO服务类
 */
@Slf4j
@Service
public class MinioService {

    @Resource
    private MinioClient minioClient;
    
    @Resource
    private MinioProperties minioProp;

    /**
     * 创建存储桶
     *
     * @param bucketName 存储桶名称
     */
    public void createBucket(String bucketName) {
        try {
            boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!bucketExists) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                log.info("创建存储桶 {} 成功", bucketName);
            }
        } catch (Exception e) {
            log.error("创建存储桶 {} 失败: {}", bucketName, e.getMessage(), e);
            throw new RuntimeException("创建存储桶失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有存储桶
     *
     * @return 存储桶列表
     */
    public List<String> listBuckets() {
        try {
            List<String> bucketNames = new ArrayList<>();
            List<Bucket> buckets = minioClient.listBuckets();
            for (Bucket bucket : buckets) {
                bucketNames.add(bucket.name());
            }
            return bucketNames;
        } catch (Exception e) {
            log.error("获取存储桶列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取存储桶列表失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件
     *
     * @param file       文件
     * @param objectName 对象名称
     * @return 文件访问路径
     */
    public String uploadFile(MultipartFile file, String objectName) {
        try {
            String bucketName = this.getDefaultBucketName();
            // 检查存储桶是否存在，不存在则创建
            createBucket(bucketName);

            // 上传文件
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(file.getInputStream(), file.getSize(), -1)
                            .contentType(file.getContentType())
                            .build()
            );

            // 获取文件访问URL
            return getFileUrl(bucketName, objectName);
        } catch (Exception e) {
            log.error("上传文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("上传文件失败: " + e.getMessage());
        }
    }

    /**
     * 上传字节数组
     *
     * @param bytes      字节数组
     * @param objectName 对象名称
     * @param contentType 内容类型
     * @return 文件访问路径
     */
    public String uploadBytes(byte[] bytes, String objectName, String contentType) {
        try {
            String bucketName = this.getDefaultBucketName();
            // 检查存储桶是否存在，不存在则创建
            createBucket(bucketName);

            // 上传文件
            try (ByteArrayInputStream bais = new ByteArrayInputStream(bytes)) {
                minioClient.putObject(
                        PutObjectArgs.builder()
                                .bucket(bucketName)
                                .object(objectName)
                                .stream(bais, bytes.length, -1)
                                .contentType(contentType)
                                .build()
                );
            }

            // 获取文件访问URL
            return getFileUrl(bucketName, objectName);
        } catch (Exception e) {
            log.error("上传字节数组失败: {}", e.getMessage(), e);
            throw new RuntimeException("上传字节数组失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 文件输入流
     */
    public InputStream downloadFile(String bucketName, String objectName) {
        try {
            return minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
        } catch (Exception e) {
            log.error("下载文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("下载文件失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件（使用默认存储桶）
     *
     * @param objectName 对象名称
     * @return 文件输入流
     */
    public InputStream downloadFile(String objectName) {
        return downloadFile(this.getDefaultBucketName(), objectName);
    }

    /**
     * 获取文件访问URL
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 文件访问URL
     */
    public String getFileUrl(String bucketName, String objectName) {
        return minioProp.getEndpoint() + "/" + bucketName + "/" + objectName;
    }

    /**
     * 获取文件访问URL（使用默认存储桶）
     *
     * @param objectName 对象名称
     * @return 文件访问URL
     */
    public String getFileUrl(String objectName) {
        return minioProp.getEndpoint() + "/" + this.getDefaultBucketName() + "/" + objectName;
    }

    /**
     * 获取文件访问URL，带有效期
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param duration 有效期
     * @param unit 时间单位
     * @return 文件访问URL
     */
    public String getFileUrl(String bucketName, String objectName, int duration, TimeUnit unit) {
        try {
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .method(Method.GET)
                            .expiry(duration, unit)
                            .build()
            );
        } catch (Exception e) {
            log.error("获取文件URL失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取文件URL失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     */
    public void deleteFile(String bucketName, String objectName) {
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
        } catch (Exception e) {
            log.error("删除文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("删除文件失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件（使用默认存储桶）
     *
     * @param objectName 对象名称
     */
    public void deleteFile(String objectName) {
        deleteFile(this.getDefaultBucketName(), objectName);
    }

    /**
     * 列出存储桶中的所有文件
     *
     * @param bucketName 存储桶名称
     * @return 文件列表
     */
    public List<String> listFiles(String bucketName) {
        try {
            List<String> fileNames = new ArrayList<>();
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucketName)
                            .build()
            );
            for (Result<Item> result : results) {
                Item item = result.get();
                if (!item.isDir()) {
                    fileNames.add(item.objectName());
                }
            }
            return fileNames;
        } catch (Exception e) {
            log.error("列出文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("列出文件失败: " + e.getMessage());
        }
    }

    /**
     * 列出默认存储桶中的所有文件
     *
     * @return 文件列表
     */
    public List<String> listFiles() {
        return listFiles(this.getDefaultBucketName());
    }

    /**
     * 检查文件是否存在
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 是否存在
     */
    public boolean isFileExist(String bucketName, String objectName) {
        try {
            minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查文件是否存在（使用默认存储桶）
     *
     * @param objectName 对象名称
     * @return 是否存在
     */
    public boolean isFileExist(String objectName) {
        return isFileExist(this.getDefaultBucketName(), objectName);
    }

    /**
     * 获取默认的桶名
     *
     * @return
     */
    public String getDefaultBucketName() {
        return minioProp.getDefaultBucketName();
    }

    public long getFileSize(String objectName) {
        try {
            StatObjectResponse statObjectResponse = minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(this.getDefaultBucketName())
                            .object(objectName)
                            .build()
            );
            return statObjectResponse.size();
        } catch (Exception e) {
            log.error("获取文件大小失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取文件大小失败: " + e.getMessage());
        }
    }
}