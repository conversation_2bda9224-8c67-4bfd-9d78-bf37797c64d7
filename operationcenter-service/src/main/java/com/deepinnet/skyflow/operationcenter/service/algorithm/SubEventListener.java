package com.deepinnet.skyflow.operationcenter.service.algorithm;

import cn.hutool.core.collection.CollectionUtil;
import com.deepinnet.digitaltwin.common.util.JsonConvertUtil;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsDTO;
import com.deepinnet.skyflow.operationcenter.service.context.TenantContext;
import com.deepinnet.skyflow.operationcenter.service.event.FlightMonitorEventCreateEvent;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.connection.stream.*;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.management.ManagementFactory;
import java.time.Duration;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Description: 监听算法侧的消息队列发送的事件
 * Date: 2025/5/8
 * Author: lijunheng
 */
@Slf4j
@Component
public class SubEventListener {

    @Value("${algorithm.redis.stream.topic}")
    private String stream;

    @Value("${algorithm.redis.stream.group}")
    private String group;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private ApplicationEventPublisher publisher;

    @Resource
    private EventMonitorService eventMonitorService;

    private static final ExecutorService executor = Executors.newSingleThreadExecutor();

    private final String consumer = ManagementFactory.getRuntimeMXBean().getName() + "-" + IdGenerateUtil.getId("skyFlowService");;

    @PostConstruct
    public void init() {
        try {
            redisTemplate.opsForStream().createGroup(stream, ReadOffset.from("0-0"), group);
        } catch (Exception ignored) {
            // group already exists
        }

        executor.submit(this::consume);
    }

    public void consume() {
        while (true) {
            List<MapRecord<String, Object, Object>> messages = redisTemplate.opsForStream().read(Consumer.from(group, consumer), StreamReadOptions.empty().block(Duration.ofSeconds(5)),
                            StreamOffset.create(stream, ReadOffset.lastConsumed()));

            if (CollectionUtil.isNotEmpty(messages)) {
                for (MapRecord<String, Object, Object> message : messages) {
                    log.info("消费消息: {}", message.getValue());
                    FlightEventsDTO flightEventsDTO = JsonConvertUtil.parseJson(JsonConvertUtil.toJsonStr(message.getValue()), FlightEventsDTO.class);
                    try {
                        //第一步就是先保存事件，里面有唯一性校验
                        TenantContext.disableTenantLine();
                        eventMonitorService.saveEvent(flightEventsDTO);
                        publisher.publishEvent(new FlightMonitorEventCreateEvent(flightEventsDTO));
                        redisTemplate.opsForStream().acknowledge(stream, group, message.getId());
                    } catch (DuplicateKeyException e) {
                        //数据库唯一约束触发，说明是重复事件，忽略，但是消息需要确认
                        log.warn("重复事件: {}", flightEventsDTO, e);
                        redisTemplate.opsForStream().acknowledge(stream, group, message.getId());
                    } catch (Exception e) {
                        //如果是其他错误，需要消息队列重试
                        log.error("处理事件失败，消息队列稍后发起重试: {}", flightEventsDTO, e);
                    } finally {
                        TenantContext.clear();
                    }
                }
            }
        }
    }
}
