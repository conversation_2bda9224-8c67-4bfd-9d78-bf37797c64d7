package com.deepinnet.skyflow.operationcenter.service.task;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/24
 */

@Configuration
public class ThreadPoolConfig {

    @Bean
    public TaskDecorator tenantContextTaskDecorator() {
        return new TenantContextTaskDecorator();
    }

    @Bean(name = "scheduleTaskExecutor")
    public ThreadPoolTaskExecutor scheduleTaskExecutor(TaskDecorator tenantContextTaskDecorator) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(15);
        executor.setThreadNamePrefix("schedule-task-exec-");
        executor.setTaskDecorator(tenantContextTaskDecorator);
        executor.initialize();
        return executor;
    }

    @Bean(name = "applicationEventTaskExecutor")
    public ThreadPoolExecutor applicationEventTaskExecutor() {
        return ExecutorBuilder.create()
                .setCorePoolSize(4)
                .setMaxPoolSize(8)
                .setWorkQueue(new LinkedBlockingQueue<>(1000))
                .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix("applicationEvent-task-exec-").build())
                .setHandler(new ThreadPoolExecutor.CallerRunsPolicy()) // 拒绝策略：由调用者线程执行
                .build();
    }
}
