package com.deepinnet.skyflow.operationcenter.service;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.ContentDetailDTO;

import java.util.List;

/**
 * 首页内容服务接口
 *
 * <AUTHOR>
 */
public interface ContentDetailService {

    /**
     * 保存内容
     *
     * @param contentDetailDTO 内容数据
     * @return 已保存的内容ID
     */
    Integer saveContent(ContentDetailDTO contentDetailDTO);

    /**
     * 更新内容
     *
     * @param contentDetailDTO 内容数据
     * @return 更新是否成功
     */
    boolean updateContent(ContentDetailDTO contentDetailDTO);

    /**
     * 根据ID获取内容
     *
     * @param id 内容ID
     * @return 内容数据
     */
    ContentDetailDTO getContentById(Integer id);

    /**
     * 获取内容列表
     *
     * @return 内容数据列表
     */
    List<ContentDetailDTO> listContent();

    /**
     * 删除内容
     *
     * @param id 内容ID
     * @return 删除是否成功
     */
    boolean deleteContent(Integer id);

    /**
     * 根据内容类型获取内容
     *
     * @param contentType 内容类型
     * @return 内容数据
     */
    ContentDetailDTO getContentByType(String contentType);
    
    /**
     * 创建或更新内容（根据租户ID和内容类型）
     * 如果指定租户和内容类型的记录已存在，则更新；否则创建新记录
     *
     * @param contentDetailDTO 内容数据
     * @return 内容ID
     */
    Integer createOrUpdateByTenantAndType(ContentDetailDTO contentDetailDTO);
} 