package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavBmImportDetailDO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportDetailDTO;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavBmImportDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 飞行无人机品牌型号导入明细转换接口
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface FlightUavBmImportDetailConvert {

    FlightUavBmImportDetailConvert INSTANCE = Mappers.getMapper(FlightUavBmImportDetailConvert.class);

    /**
     * DO 转 DTO
     *
     * @param importDetailDO DO 对象
     * @return DTO 对象
     */
    FlightUavBmImportDetailDTO convert(FlightUavBmImportDetailDO importDetailDO);

    /**
     * DTO 转 DO
     *
     * @param importDetailDTO DTO 对象
     * @return DO 对象
     */
    @Mapping(target = "gmtModified", ignore = true)
    FlightUavBmImportDetailDO convertToDO(FlightUavBmImportDetailDTO importDetailDTO);

    /**
     * DO List 转 DTO List
     *
     * @param importDetailDOList DO 列表
     * @return DTO 列表
     */
    List<FlightUavBmImportDetailDTO> convertList(List<FlightUavBmImportDetailDO> importDetailDOList);

    /**
     * DTO 转 VO
     *
     * @param importDetailDTO DTO 对象
     * @return VO 对象
     */
    FlightUavBmImportDetailVO convertToVO(FlightUavBmImportDetailDTO importDetailDTO);

    /**
     * DTO列表 转 VO列表
     *
     * @param importDetailDTOList DTO 列表
     * @return VO 列表
     */
    List<FlightUavBmImportDetailVO> convertToVOList(List<FlightUavBmImportDetailDTO> importDetailDTOList);
} 