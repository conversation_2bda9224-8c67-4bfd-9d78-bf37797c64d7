package com.deepinnet.skyflow.operationcenter.service.event;

import cn.hutool.core.collection.CollectionUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.infra.api.dto.UserDetailDTO;
import com.deepinnet.infra.api.dto.UserQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandMatchStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandSyncStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.client.FlightUserClient;
import com.deepinnet.skyflow.operationcenter.service.context.TenantContext;
import com.deepinnet.skyflow.operationcenter.service.demand.FlightDemandService;
import com.deepinnet.skyflow.operationcenter.service.demand.ServiceProviderClient;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:
 * Date: 2025/4/17
 * Author: lijunheng
 */
@Component
public class FlightDemandCreateEventListener implements ApplicationListener<FlightDemandCreateEvent> {

    private static final Logger logger = LoggerFactory.getLogger(FlightDemandCreateEventListener.class);

    @Resource
    private FlightDemandService flightDemandService;

    @Resource
    private ServiceProviderClient serviceProviderClient;

    @Resource
    private FlightUserClient userClient;

    @Override
    public void onApplicationEvent(FlightDemandCreateEvent event) {
        try {
            TenantContext.disableTenantLine();
            String flightDemandNo = event.getFlightDemandNo();
            logger.info("Received FlightDemandCreateEvent for demand: {}", flightDemandNo);

            // 获取需求详情
            FlightDemandDTO demandDTO = flightDemandService.getFlightDemandByNo(flightDemandNo);
            if (demandDTO == null) {
                logger.error("Flight demand not found with number: {}", flightDemandNo);
                return;
            }

            String matchedProvider = serviceProviderClient.matchServiceProviders(demandDTO);
            if (matchedProvider == null) {
                //  如果没有找到服务商，需求暂时搁置，保持“待分配”状态，后续看实际情况补充处理功能
                logger.warn("No service providers matched for demand: {}", flightDemandNo);
                demandDTO.setMatchStatus(FlightDemandMatchStatusEnum.MATCH_FAILED);
                flightDemandService.updateFlightDemandStatus(demandDTO);
                return;
            }

            //获取匹配满足条件的服务商
            UserQueryDTO userQueryDTO = new UserQueryDTO();
            userQueryDTO.setUserNos(List.of(matchedProvider));
            List<UserDetailDTO> userDetailList = userClient.getUserDetailList(userQueryDTO);
            if (CollectionUtil.isEmpty(userDetailList)) {
                throw new BizException(BizErrorCode.NOT_FOUND.getCode(), "未找到服务商信息");
            }
            UserDetailDTO supplierData = userDetailList.get(0);
            String providerNo = supplierData.getUserNo();
            demandDTO.setServiceProviderNo(providerNo);
            demandDTO.setServiceProviderName(supplierData.getUserName());
            demandDTO.setServiceProviderCompanyName(supplierData.getSupplierDetailDTO().getCompanyName());
            demandDTO.setMatchStatus(FlightDemandMatchStatusEnum.MATCHED);
            flightDemandService.updateFlightDemandStatus(demandDTO);

            //同步需求给服务商，并返回服务商方的唯一id
            try {
                String syncOrderNo = serviceProviderClient.syncDemandToProvider(demandDTO);
                logger.info("Successfully processed demand {}, assigned to provider {}, sync order: {}",
                        flightDemandNo, providerNo, syncOrderNo);
                //更新同步数据到库
                demandDTO.setSyncOrderNo(syncOrderNo);
                demandDTO.setSyncStatus(FlightDemandSyncStatusEnum.SYNCED);
                flightDemandService.updateFlightDemandStatus(demandDTO);
            } catch (Exception e) {
                logger.error("Failed to process demand {}, assigned to provider {}", flightDemandNo, providerNo, e);
                demandDTO.setSyncStatus(FlightDemandSyncStatusEnum.SYNC_FAILED);
                flightDemandService.updateFlightDemandStatus(demandDTO);
            }
        } finally {
            TenantContext.clear();
        }
    }
}
