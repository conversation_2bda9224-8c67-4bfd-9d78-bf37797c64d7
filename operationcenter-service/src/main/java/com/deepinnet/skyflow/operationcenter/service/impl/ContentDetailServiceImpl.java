package com.deepinnet.skyflow.operationcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ContentDetailDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.ContentDetailRepository;
import com.deepinnet.skyflow.operationcenter.dto.ContentDetailDTO;
import com.deepinnet.skyflow.operationcenter.service.ContentDetailService;
import com.deepinnet.skyflow.operationcenter.service.convert.ContentDetailConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.tenant.TenantIdUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 首页内容服务实现类
 *
 * <AUTHOR>
 */
@Service
public class ContentDetailServiceImpl implements ContentDetailService {

    @Resource
    private ContentDetailRepository contentDetailRepository;

    @Resource
    private ContentDetailConvert contentDetailConvert;

    @Override
    public Integer saveContent(ContentDetailDTO contentDetailDTO) {
        ContentDetailDO contentDetailDO = contentDetailConvert.convertToDO(contentDetailDTO);
        contentDetailDO.setGmtCreated(LocalDateTime.now());
        contentDetailDO.setGmtModified(LocalDateTime.now());
        contentDetailDO.setId(null);
        boolean success = contentDetailRepository.save(contentDetailDO);
        if (!success) {
            LogUtil.error("保存首页内容失败: {}", contentDetailDTO.getContentType());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        LogUtil.info("保存首页内容成功: {}", contentDetailDO.getId());
        return contentDetailDO.getId();
    }

    @Override
    public boolean updateContent(ContentDetailDTO contentDetailDTO) {
        if (contentDetailDTO.getId() == null) {
            LogUtil.error("更新首页内容失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查内容是否存在
        ContentDetailDO existContent = contentDetailRepository.getById(contentDetailDTO.getId());
        if (existContent == null) {
            LogUtil.error("更新首页内容失败，内容不存在: {}", contentDetailDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        ContentDetailDO contentDetailDO = contentDetailConvert.convertToDO(contentDetailDTO);
        contentDetailDO.setGmtModified(LocalDateTime.now());
        
        boolean success = contentDetailRepository.updateById(contentDetailDO);
        if (!success) {
            LogUtil.error("更新首页内容失败: {}", contentDetailDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        LogUtil.info("更新首页内容成功: {}", contentDetailDO.getId());
        return true;
    }

    @Override
    public ContentDetailDTO getContentById(Integer id) {
        if (id == null) {
            LogUtil.error("获取首页内容失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        ContentDetailDO contentDetailDO = contentDetailRepository.getById(id);
        if (contentDetailDO == null) {
            LogUtil.error("获取首页内容失败，内容不存在: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        return contentDetailConvert.convert(contentDetailDO);
    }

    @Override
    public List<ContentDetailDTO> listContent() {
        List<ContentDetailDO> contentDetailDOList = contentDetailRepository.list();
        return contentDetailConvert.convertList(contentDetailDOList);
    }

    @Override
    public boolean deleteContent(Integer id) {
        if (id == null) {
            LogUtil.error("删除首页内容失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查内容是否存在
        ContentDetailDO existContent = contentDetailRepository.getById(id);
        if (existContent == null) {
            LogUtil.error("删除首页内容失败，内容不存在: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        boolean success = contentDetailRepository.removeById(id);
        if (!success) {
            LogUtil.error("删除首页内容失败: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("删除首页内容成功: {}", id);
        return true;
    }

    @Override
    public ContentDetailDTO getContentByType(String contentType) {
        if (contentType == null) {
            LogUtil.error("获取首页内容失败，内容类型为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        LambdaQueryWrapper<ContentDetailDO> wrapper = Wrappers.lambdaQuery(ContentDetailDO.class)
                .eq(ContentDetailDO::getTenantId, TenantIdUtil.getTenantId())
                .eq(ContentDetailDO::getContentType, contentType)
                .eq(ContentDetailDO::getIsDeleted, false);

        ContentDetailDO contentDetailDO = contentDetailRepository.getOne(wrapper);
        if (contentDetailDO == null) {
            LogUtil.error("获取首页内容失败，内容不存在: {}", contentType);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        return contentDetailConvert.convert(contentDetailDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createOrUpdateByTenantAndType(ContentDetailDTO contentDetailDTO) {
        if (contentDetailDTO == null) {
            LogUtil.error("创建或更新首页内容失败，参数为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        String tenantId = contentDetailDTO.getTenantId();
        String contentType = contentDetailDTO.getContentType();
        
        if (StringUtils.isBlank(tenantId)) {
            LogUtil.error("创建或更新首页内容失败，租户ID为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "租户ID不能为空");
        }
        
        if (StringUtils.isBlank(contentType)) {
            LogUtil.error("创建或更新首页内容失败，内容类型为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "内容类型不能为空");
        }
        
        if (StringUtils.isBlank(contentDetailDTO.getContentJson())) {
            LogUtil.error("创建或更新首页内容失败，内容JSON为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "内容JSON不能为空");
        }
        
        // 查询是否已存在对应租户和内容类型的记录
        LambdaQueryWrapper<ContentDetailDO> queryWrapper = Wrappers.lambdaQuery(ContentDetailDO.class)
                .eq(ContentDetailDO::getTenantId, tenantId)
                .eq(ContentDetailDO::getContentType, contentType)
                .eq(ContentDetailDO::getIsDeleted, false);
        
        ContentDetailDO existingContent = contentDetailRepository.getOne(queryWrapper);
        
        if (existingContent != null) {
            // 更新现有记录
            LogUtil.info("根据租户ID和内容类型更新首页内容: tenantId={}, contentType={}, id={}", 
                    tenantId, contentType, existingContent.getId());
            
            ContentDetailDO contentDetailDO = contentDetailConvert.convertToDO(contentDetailDTO);
            contentDetailDO.setId(existingContent.getId());
            contentDetailDO.setGmtModified(LocalDateTime.now());
            
            boolean success = contentDetailRepository.updateById(contentDetailDO);
            if (!success) {
                LogUtil.error("根据租户ID和内容类型更新首页内容失败: tenantId={}, contentType={}", 
                        tenantId, contentType);
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "更新首页内容失败");
            }
            
            return existingContent.getId();
        } else {
            // 创建新记录
            LogUtil.info("根据租户ID和内容类型创建首页内容: tenantId={}, contentType={}", 
                    tenantId, contentType);
            
            ContentDetailDO contentDetailDO = contentDetailConvert.convertToDO(contentDetailDTO);
            contentDetailDO.setId(null);
            contentDetailDO.setGmtCreated(LocalDateTime.now());
            contentDetailDO.setGmtModified(LocalDateTime.now());
            
            boolean success = contentDetailRepository.save(contentDetailDO);
            if (!success) {
                LogUtil.error("根据租户ID和内容类型创建首页内容失败: tenantId={}, contentType={}", 
                        tenantId, contentType);
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "创建首页内容失败");
            }
            
            return contentDetailDO.getId();
        }
    }
} 