package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.DictionaryDO;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryDTO;
import com.deepinnet.skyflow.operationcenter.vo.DictionaryVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 字典对象转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface DictionaryConvert {

    DictionaryConvert INSTANCE = Mappers.getMapper(DictionaryConvert.class);

    /**
     * DO转DTO
     *
     * @param dictionaryDO DO对象
     * @return DTO对象
     */
    DictionaryDTO convert(DictionaryDO dictionaryDO);

    /**
     * DTO转DO
     *
     * @param dictionaryDTO DTO对象
     * @return DO对象
     */
    DictionaryDO convertToDO(DictionaryDTO dictionaryDTO);

    /**
     * DTO转VO
     *
     * @param dictionaryDTO DTO对象
     * @return VO对象
     */
    DictionaryVO convertToVO(DictionaryDTO dictionaryDTO);

    /**
     * DO列表转DTO列表
     *
     * @param dictionaryDOList DO列表
     * @return DTO列表
     */
    List<DictionaryDTO> convertList(List<DictionaryDO> dictionaryDOList);

    /**
     * DTO列表转VO列表
     *
     * @param dictionaryDTOList DTO列表
     * @return VO列表
     */
    List<DictionaryVO> convertToVOList(List<DictionaryDTO> dictionaryDTOList);
} 