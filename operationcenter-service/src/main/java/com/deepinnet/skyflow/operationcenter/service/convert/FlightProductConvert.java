package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightProductDO;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightProductTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.ProductServiceTypeEnum;
import com.deepinnet.skyflow.operationcenter.vo.FlightProductVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 飞行产品转换接口
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface FlightProductConvert {

    FlightProductConvert INSTANCE = Mappers.getMapper(FlightProductConvert.class);

    /**
     * DO 转 DTO
     *
     * @param flightProductDO DO 对象
     * @return DTO 对象
     */
    @Mapping(source = "productType", target = "productType", qualifiedByName = "stringToEnum")
    @Mapping(source = "productServiceType", target = "productServiceType", qualifiedByName = "stringToServiceTypeEnum")
    FlightProductDTO convert(FlightProductDO flightProductDO);

    /**
     * DTO 转 DO
     *
     * @param flightProductDTO DTO 对象
     * @return DO 对象
     */
    @Mapping(target = "gmtModified", ignore = true)
    @Mapping(source = "productType", target = "productType", qualifiedByName = "enumToString")
    @Mapping(source = "productServiceType", target = "productServiceType", qualifiedByName = "serviceTypeEnumToString")
    FlightProductDO convertToDO(FlightProductDTO flightProductDTO);

    /**
     * DTO List 转 DO List
     *
     * @param flightProductDTOList DTO 列表
     * @return DO 列表
     */
    List<FlightProductDO> convertToDOList(List<FlightProductDTO> flightProductDTOList);

    /**
     * DO List 转 DTO List
     *
     * @param flightProductDOList DO 列表
     * @return DTO 列表
     */
    List<FlightProductDTO> convertList(List<FlightProductDO> flightProductDOList);

    /**
     * DTO 转 VO
     *
     * @param flightProductDTO DTO 对象
     * @return VO 对象
     */
    FlightProductVO convertToVO(FlightProductDTO flightProductDTO);

    /**
     * DTO列表 转 VO列表
     *
     * @param flightProductDTOList DTO 列表
     * @return VO 列表
     */
    List<FlightProductVO> convertToVOList(List<FlightProductDTO> flightProductDTOList);
    
    /**
     * 字符串转枚举
     *
     * @param type 类型字符串
     * @return 产品类型枚举
     */
    @Named("stringToEnum")
    default FlightProductTypeEnum stringToEnum(String type) {
        if (type == null) {
            return null;
        }
        for (FlightProductTypeEnum typeEnum : FlightProductTypeEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 枚举转字符串
     *
     * @param typeEnum 产品类型枚举
     * @return 类型字符串
     */
    @Named("enumToString")
    default String enumToString(FlightProductTypeEnum typeEnum) {
        return typeEnum == null ? null : typeEnum.getType();
    }
    
    /**
     * 字符串转服务类型枚举
     *
     * @param type 服务类型字符串
     * @return 服务类型枚举
     */
    @Named("stringToServiceTypeEnum")
    default ProductServiceTypeEnum stringToServiceTypeEnum(String type) {
        if (type == null) {
            return ProductServiceTypeEnum.NORMAL_SERVICE;  // 默认为普通服务
        }
        return ProductServiceTypeEnum.getByCode(type);
    }

    /**
     * 服务类型枚举转字符串
     *
     * @param typeEnum 服务类型枚举
     * @return 服务类型字符串
     */
    @Named("serviceTypeEnumToString")
    default String serviceTypeEnumToString(ProductServiceTypeEnum typeEnum) {
        return typeEnum == null ? ProductServiceTypeEnum.NORMAL_SERVICE.getCode() : typeEnum.getCode();
    }
} 