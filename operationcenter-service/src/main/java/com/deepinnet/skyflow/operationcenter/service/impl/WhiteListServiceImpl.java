package com.deepinnet.skyflow.operationcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.WhiteListDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.WhiteListRepository;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListBatchOperationDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListGroupQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListBatchReplaceDTO;
import com.deepinnet.skyflow.operationcenter.service.WhiteListService;
import com.deepinnet.skyflow.operationcenter.service.convert.WhiteListConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.vo.WhiteListGroupVO;
import com.deepinnet.skyflow.operationcenter.vo.WhiteListVO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 白名单服务实现类
 *
 * <AUTHOR>
 */
@Service
public class WhiteListServiceImpl implements WhiteListService {

    @Resource
    private WhiteListRepository whiteListRepository;

    @Resource
    private WhiteListConvert whiteListConvert;

    @Override
    public Integer saveWhiteList(WhiteListDTO whiteListDTO) {
        // 检查客户编号和服务商编号是否已存在
        WhiteListDO existWhiteList = whiteListRepository.getOne(
                Wrappers.lambdaQuery(WhiteListDO.class)
                        .eq(WhiteListDO::getCustomerUserNo, whiteListDTO.getCustomerUserNo())
                        .eq(WhiteListDO::getSupplierUserNo, whiteListDTO.getSupplierUserNo())
        );
        if (existWhiteList != null) {
            LogUtil.error("保存白名单失败，该客户与服务商关系已存在: 客户编号={}, 服务商编号={}", 
                    whiteListDTO.getCustomerUserNo(), whiteListDTO.getSupplierUserNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "该客户与服务商关系已存在");
        }

        WhiteListDO whiteListDO = whiteListConvert.convertToDO(whiteListDTO);
        whiteListDO.setGmtCreated(LocalDateTime.now());
        whiteListDO.setGmtModified(LocalDateTime.now());
        whiteListDO.setId(null);
        whiteListDO.setIsDeleted(false);
        
        boolean success = whiteListRepository.save(whiteListDO);
        if (!success) {
            LogUtil.error("保存白名单失败: 客户编号={}, 服务商编号={}", 
                    whiteListDTO.getCustomerUserNo(), whiteListDTO.getSupplierUserNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        LogUtil.info("保存白名单成功: {}", whiteListDO.getId());
        return whiteListDO.getId();
    }

    @Override
    public boolean updateWhiteList(WhiteListDTO whiteListDTO) {
        if (whiteListDTO.getId() == null) {
            LogUtil.error("更新白名单失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查白名单是否存在
        WhiteListDO existWhiteList = whiteListRepository.getById(whiteListDTO.getId());
        if (existWhiteList == null) {
            LogUtil.error("更新白名单失败，白名单不存在: {}", whiteListDTO.getId());
            throw new BizException(BizErrorCode.NOT_FOUND.getCode(), "指定的白名单记录不存在");
        }

        // 如果更新了客户编号或服务商编号，需要检查是否与其他记录冲突
        if (!existWhiteList.getCustomerUserNo().equals(whiteListDTO.getCustomerUserNo()) ||
                !existWhiteList.getSupplierUserNo().equals(whiteListDTO.getSupplierUserNo())) {
            WhiteListDO conflictWhiteList = whiteListRepository.getOne(
                    Wrappers.lambdaQuery(WhiteListDO.class)
                            .eq(WhiteListDO::getCustomerUserNo, whiteListDTO.getCustomerUserNo())
                            .eq(WhiteListDO::getSupplierUserNo, whiteListDTO.getSupplierUserNo())
                            .ne(WhiteListDO::getId, whiteListDTO.getId())
            );
            if (conflictWhiteList != null) {
                LogUtil.error("更新白名单失败，该客户与服务商关系已被其他记录使用: 客户编号={}, 服务商编号={}", 
                        whiteListDTO.getCustomerUserNo(), whiteListDTO.getSupplierUserNo());
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "该客户与服务商关系已存在");
            }
        }

        WhiteListDO whiteListDO = whiteListConvert.convertToDO(whiteListDTO);
        whiteListDO.setGmtModified(LocalDateTime.now());
        
        boolean success = whiteListRepository.updateById(whiteListDO);
        if (!success) {
            LogUtil.error("更新白名单失败: {}", whiteListDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }
        
        LogUtil.info("更新白名单成功: {}", whiteListDO.getId());
        return true;
    }

    @Override
    public WhiteListDTO getWhiteListById(Integer id) {
        if (id == null) {
            LogUtil.error("获取白名单失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        WhiteListDO whiteListDO = whiteListRepository.getById(id);
        if (whiteListDO == null) {
            LogUtil.error("获取白名单失败，白名单不存在: {}", id);
            return null;
        }

        return whiteListConvert.convert(whiteListDO);
    }

    @Override
    public WhiteListDTO getWhiteListByCustomerAndSupplier(String customerUserNo, String supplierUserNo) {
        if (StringUtils.isBlank(customerUserNo) || StringUtils.isBlank(supplierUserNo)) {
            LogUtil.error("获取白名单失败，客户编号或服务商编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        WhiteListDO whiteListDO = whiteListRepository.getOne(
                Wrappers.lambdaQuery(WhiteListDO.class)
                        .eq(WhiteListDO::getCustomerUserNo, customerUserNo)
                        .eq(WhiteListDO::getSupplierUserNo, supplierUserNo)
        );
        
        if (whiteListDO == null) {
            return null;
        }

        return whiteListConvert.convert(whiteListDO);
    }

    @Override
    public CommonPage<WhiteListDTO> pageQueryWhiteList(WhiteListQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<WhiteListDO> queryWrapper = Wrappers.lambdaQuery(WhiteListDO.class)
                .eq(StringUtils.isNotBlank(queryDTO.getCustomerUserNo()), WhiteListDO::getCustomerUserNo, queryDTO.getCustomerUserNo())
                .eq(StringUtils.isNotBlank(queryDTO.getSupplierUserNo()), WhiteListDO::getSupplierUserNo, queryDTO.getSupplierUserNo())
                .like(StringUtils.isNotBlank(queryDTO.getCustomerUserName()), WhiteListDO::getCustomerUserName, queryDTO.getCustomerUserName())
                .like(StringUtils.isNotBlank(queryDTO.getSupplierUserName()), WhiteListDO::getSupplierUserName, queryDTO.getSupplierUserName())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), WhiteListDO::getTenantId, queryDTO.getTenantId())
                .orderByDesc(WhiteListDO::getGmtCreated);

        List<WhiteListDO> whiteListDOList = whiteListRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(whiteListDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<WhiteListDTO> whiteListDTOList = whiteListConvert.convertList(whiteListDOList);
        PageInfo<WhiteListDTO> pageInfo = new PageInfo<>(whiteListDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), whiteListDTOList);
    }

    @Override
    public boolean deleteWhiteList(Integer id) {
        if (id == null) {
            LogUtil.error("删除白名单失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查白名单是否存在
        WhiteListDO existWhiteList = whiteListRepository.getById(id);
        if (existWhiteList == null) {
            LogUtil.error("删除白名单失败，白名单不存在: {}", id);
            return false;
        }

        boolean success = whiteListRepository.removeById(id);
        if (!success) {
            LogUtil.error("删除白名单失败: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("删除白名单成功: {}", id);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchOperateWhiteLists(WhiteListBatchOperationDTO batchOperationDTO) {
        if (batchOperationDTO == null) {
            LogUtil.error("批量操作白名单失败，参数为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        // 处理删除操作
        List<Integer> deleteIds = batchOperationDTO.getDeleteIds();
        if (!CollectionUtils.isEmpty(deleteIds)) {
            for (Integer id : deleteIds) {
                try {
                    // 检查是否存在
                    WhiteListDO whiteListDO = whiteListRepository.getById(id);
                    if (whiteListDO != null) {
                        boolean success = whiteListRepository.removeById(id);
                        if (!success) {
                            LogUtil.error("批量删除白名单失败，ID: {}", id);
                            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "删除白名单失败，ID: " + id);
                        }
                    } else {
                        LogUtil.error("批量删除白名单失败，记录不存在，ID: {}", id);
                    }
                } catch (Exception e) {
                    LogUtil.error("批量删除白名单出错，ID: {}, 错误: {}", id, e.getMessage());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "删除白名单失败，ID: " + id);
                }
            }
        }
        
        // 处理更新操作
        List<WhiteListDTO> updateItems = batchOperationDTO.getUpdateItems();
        if (!CollectionUtils.isEmpty(updateItems)) {
            // 收集所有客户和服务商关系用于检查
            List<String> relationKeys = new ArrayList<>();
            Map<String, Integer> relationIdMap = new HashMap<>();
            
            for (WhiteListDTO item : updateItems) {
                if (item.getId() == null) {
                    LogUtil.error("批量更新白名单失败，缺少ID");
                    throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "更新白名单缺少ID");
                }
                
                // 检查白名单是否存在
                WhiteListDO existWhiteList = whiteListRepository.getById(item.getId());
                if (existWhiteList == null) {
                    LogUtil.error("批量更新白名单失败，白名单不存在: {}", item.getId());
                    throw new BizException(BizErrorCode.NOT_FOUND.getCode(), "白名单不存在，ID: " + item.getId());
                }
                
                // 如果更新了客户编号或服务商编号，需要检查重复
                String customerUserNo = item.getCustomerUserNo();
                String supplierUserNo = item.getSupplierUserNo();
                if (StringUtils.isNotBlank(customerUserNo) && StringUtils.isNotBlank(supplierUserNo)) {
                    String relationKey = customerUserNo + ":" + supplierUserNo;
                    if (!relationKey.equals(existWhiteList.getCustomerUserNo() + ":" + existWhiteList.getSupplierUserNo())) {
                        if (relationKeys.contains(relationKey)) {
                            LogUtil.error("批量更新白名单失败，请求中存在重复的客户服务商关系: 客户={}, 服务商={}", customerUserNo, supplierUserNo);
                            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), 
                                "请求中存在重复的客户服务商关系: 客户=" + customerUserNo + ", 服务商=" + supplierUserNo);
                        }
                        relationKeys.add(relationKey);
                        relationIdMap.put(relationKey, item.getId());
                    }
                }
            }
            
            // 检查客户服务商关系是否已存在于数据库（排除自身ID）
            if (!relationKeys.isEmpty()) {
                for (String relationKey : relationKeys) {
                    String[] parts = relationKey.split(":");
                    String customerUserNo = parts[0];
                    String supplierUserNo = parts[1];
                    Integer updateId = relationIdMap.get(relationKey);
                    
                    WhiteListDO existingWhiteList = whiteListRepository.getOne(
                            Wrappers.lambdaQuery(WhiteListDO.class)
                                    .eq(WhiteListDO::getCustomerUserNo, customerUserNo)
                                    .eq(WhiteListDO::getSupplierUserNo, supplierUserNo)
                    );
                    
                    // 如果存在关系相同但ID不同的记录，说明关系冲突
                    if (existingWhiteList != null && !existingWhiteList.getId().equals(updateId)) {
                        LogUtil.error("批量更新白名单失败，该客户与服务商关系已存在: 客户={}, 服务商={}", customerUserNo, supplierUserNo);
                        throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), 
                            "该客户与服务商关系已存在: 客户=" + customerUserNo + ", 服务商=" + supplierUserNo);
                    }
                }
            }
            
            // 执行更新操作
            for (WhiteListDTO item : updateItems) {
                try {
                    boolean success = updateWhiteList(item);
                    if (!success) {
                        LogUtil.error("批量更新白名单失败，ID: {}", item.getId());
                        throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "更新白名单失败，ID: " + item.getId());
                    }
                } catch (Exception e) {
                    LogUtil.error("批量更新白名单出错，ID: {}, 错误: {}", item.getId(), e.getMessage());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "更新白名单失败，ID: " + item.getId());
                }
            }
        }
        
        // 处理新增操作
        List<WhiteListDTO> createItems = batchOperationDTO.getCreateItems();
        if (!CollectionUtils.isEmpty(createItems)) {
            List<String> relationKeys = new ArrayList<>();
            
            // 先检查客户服务商关系是否重复
            for (WhiteListDTO item : createItems) {
                String customerUserNo = item.getCustomerUserNo();
                String supplierUserNo = item.getSupplierUserNo();
                
                if (StringUtils.isNotBlank(customerUserNo) && StringUtils.isNotBlank(supplierUserNo)) {
                    String relationKey = customerUserNo + ":" + supplierUserNo;
                    if (relationKeys.contains(relationKey)) {
                        LogUtil.error("批量新增白名单失败，请求中存在重复的客户服务商关系: 客户={}, 服务商={}", customerUserNo, supplierUserNo);
                        throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), 
                            "请求中存在重复的客户服务商关系: 客户=" + customerUserNo + ", 服务商=" + supplierUserNo);
                    }
                    relationKeys.add(relationKey);
                }
            }
            
            // 检查客户服务商关系是否已存在于数据库
            for (String relationKey : relationKeys) {
                String[] parts = relationKey.split(":");
                String customerUserNo = parts[0];
                String supplierUserNo = parts[1];
                
                WhiteListDO existingWhiteList = whiteListRepository.getOne(
                        Wrappers.lambdaQuery(WhiteListDO.class)
                                .eq(WhiteListDO::getCustomerUserNo, customerUserNo)
                                .eq(WhiteListDO::getSupplierUserNo, supplierUserNo)
                );
                
                if (existingWhiteList != null) {
                    LogUtil.error("批量新增白名单失败，该客户与服务商关系已存在: 客户={}, 服务商={}", customerUserNo, supplierUserNo);
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), 
                        "该客户与服务商关系已存在: 客户=" + customerUserNo + ", 服务商=" + supplierUserNo);
                }
            }
            
            // 执行新增操作
            for (WhiteListDTO item : createItems) {
                try {
                    saveWhiteList(item);
                } catch (Exception e) {
                    LogUtil.error("批量新增白名单出错，客户={}, 服务商={}, 错误: {}", 
                        item.getCustomerUserNo(), item.getSupplierUserNo(), e.getMessage());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), 
                        "新增白名单失败，客户=" + item.getCustomerUserNo() + ", 服务商=" + item.getSupplierUserNo());
                }
            }
        }
        
        return true;
    }

    @Override
    public CommonPage<WhiteListGroupVO> pageQueryWhiteListGroupByCustomer(WhiteListGroupQueryDTO queryDTO) {
        if (queryDTO == null) {
            LogUtil.error("按客户分组查询白名单失败，参数为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        // 计算分页参数
        int offset = (queryDTO.getPageNum() - 1) * queryDTO.getPageSize();
        int limit = queryDTO.getPageSize();
        
        // 查询按客户分组的数据
        List<Map<String, Object>> customerGroups = whiteListRepository.findDistinctCustomersWithPaging(
                queryDTO.getCustomerUserName(),
                queryDTO.getSupplierUserName(),
                queryDTO.getTenantId(),
                offset,
                limit
        );
        
        // 统计总客户数
        long totalCustomers = whiteListRepository.countDistinctCustomers(
                queryDTO.getCustomerUserName(),
                queryDTO.getSupplierUserName(),
                queryDTO.getTenantId()
        );
        
        if (CollectionUtils.isEmpty(customerGroups)) {
            return CommonPage.buildEmptyPage();
        }
        
        // 构建分组结果
        List<WhiteListGroupVO> groupVOList = new ArrayList<>(customerGroups.size());
        for (Map<String, Object> group : customerGroups) {
            String customerUserNo = (String) group.get("customer_user_no");
            
            WhiteListGroupVO groupVO = new WhiteListGroupVO();
            groupVO.setCustomerUserNo(customerUserNo);
            groupVO.setCustomerUserName((String) group.get("customer_user_name"));
            groupVO.setTenantId((String) group.get("tenant_id"));
            
            // 设置服务商总数
            Object totalSuppliers = group.get("total_suppliers");
            if (totalSuppliers instanceof Number) {
                groupVO.setTotalSuppliers(((Number) totalSuppliers).intValue());
            } else {
                groupVO.setTotalSuppliers(0);
            }
            
            // 查询该客户下的所有白名单
            List<WhiteListDO> whiteListDOList = whiteListRepository.findByCustomerUserNo(customerUserNo);
            if (!CollectionUtils.isEmpty(whiteListDOList)) {
                List<WhiteListDTO> whiteListDTOList = whiteListConvert.convertList(whiteListDOList);
                List<WhiteListVO> whiteListVOList = whiteListConvert.convertToVOList(whiteListDTOList);
                groupVO.setWhiteListItems(whiteListVOList);
            } else {
                groupVO.setWhiteListItems(new ArrayList<>());
            }
            
            groupVOList.add(groupVO);
        }
        
        // 计算总页数
        int pages = (int) ((totalCustomers + queryDTO.getPageSize() - 1) / queryDTO.getPageSize());
        
        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pages, totalCustomers, groupVOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchReplaceWhiteListsByCustomer(WhiteListBatchReplaceDTO batchReplaceDTO) {
        if (batchReplaceDTO == null) {
            LogUtil.error("按客户批量覆盖白名单失败，参数为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        String customerUserNo = batchReplaceDTO.getCustomerUserNo();
        if (StringUtils.isBlank(customerUserNo)) {
            LogUtil.error("按客户批量覆盖白名单失败，客户编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "客户编号不能为空");
        }
        
        List<WhiteListDTO> whiteListItems = batchReplaceDTO.getWhiteListItems();
        if (CollectionUtils.isEmpty(whiteListItems)) {
            LogUtil.error("按客户批量覆盖白名单失败，白名单列表为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "白名单列表不能为空");
        }
        
        // 检查所有白名单的客户编号是否一致
        for (WhiteListDTO whiteListDTO : whiteListItems) {
            if (!customerUserNo.equals(whiteListDTO.getCustomerUserNo())) {
                LogUtil.error("按客户批量覆盖白名单失败，部分白名单的客户编号与主客户编号不一致: {} vs {}", 
                        whiteListDTO.getCustomerUserNo(), customerUserNo);
                throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), 
                        "部分白名单的客户编号与主客户编号不一致");
            }
        }
        
        // 收集所有服务商编号，检查是否有重复
        List<String> supplierUserNos = new ArrayList<>();
        for (WhiteListDTO whiteListDTO : whiteListItems) {
            String supplierUserNo = whiteListDTO.getSupplierUserNo();
            if (StringUtils.isBlank(supplierUserNo)) {
                LogUtil.error("按客户批量覆盖白名单失败，服务商编号为空");
                throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "服务商编号不能为空");
            }
            
            if (supplierUserNos.contains(supplierUserNo)) {
                LogUtil.error("按客户批量覆盖白名单失败，存在重复的服务商编号: {}", supplierUserNo);
                throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), 
                        "存在重复的服务商编号: " + supplierUserNo);
            }
            
            supplierUserNos.add(supplierUserNo);
            
            // 如果没有设置客户名称，使用批量DTO中的客户名称
            if (StringUtils.isBlank(whiteListDTO.getCustomerUserName()) && 
                    StringUtils.isNotBlank(batchReplaceDTO.getCustomerUserName())) {
                whiteListDTO.setCustomerUserName(batchReplaceDTO.getCustomerUserName());
            }
        }
        
        try {
            // 1. 先删除该客户的所有白名单
            boolean deleteSuccess = whiteListRepository.deleteByCustomerUserNo(customerUserNo);

            
            // 2. 批量新增白名单
            for (WhiteListDTO whiteListDTO : whiteListItems) {
                try {
                    saveWhiteList(whiteListDTO);
                } catch (Exception e) {
                    LogUtil.error("按客户批量覆盖白名单失败，新增白名单记录失败: {} -> {}, 错误: {}", 
                            customerUserNo, whiteListDTO.getSupplierUserNo(), e.getMessage());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), 
                            "新增白名单记录失败" );
                }
            }
            
            return true;
        } catch (Exception e) {
            LogUtil.error("按客户批量覆盖白名单失败: {}, 错误: {}", customerUserNo, e.getMessage());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), 
                    "按客户批量覆盖白名单失败");
        }
    }
} 