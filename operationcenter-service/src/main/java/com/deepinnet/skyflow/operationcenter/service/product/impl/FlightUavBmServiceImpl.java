package com.deepinnet.skyflow.operationcenter.service.product.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavBmDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderProductUsageDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightProductDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightUavBmRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightUavRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderProductUsageRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightProductRepository;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmQueryDTO;
import com.deepinnet.skyflow.operationcenter.enums.BizTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightProductTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavBmService;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavBmConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 飞行无人机品牌型号服务实现类
 *
 * <AUTHOR>
 */
@Service
public class FlightUavBmServiceImpl implements FlightUavBmService {

    @Resource
    private FlightUavBmRepository flightUavBmRepository;

    @Resource
    private FlightUavBmConvert flightUavBmConvert;

    @Resource
    private FlightUavRepository flightUavRepository;

    @Resource
    private FlightOrderProductUsageRepository orderProductUsageRepository;

    @Resource
    private FlightProductRepository flightProductRepository;

    @Override
    public String saveFlightUavBm(FlightUavBmDTO flightUavBmDTO) {
        // Validate required fields
        validateRequiredFields(flightUavBmDTO);
        
        // Validate parameter values
        validateParameterValues(flightUavBmDTO);
        
        // Validate picture count and format - assuming pictures are already validated by the frontend
        validatePictures(flightUavBmDTO);

        String flightUavBmNo = IdGenerateUtil.getId(BizTypeEnum.FLIGHT_UAV_BM.getType());
        flightUavBmDTO.setFlightUavBmNo(flightUavBmNo);
        // 检查型号编码是否已存在
        FlightUavBmDO existFlightUavBm = flightUavBmRepository.getOne(
                Wrappers.lambdaQuery(FlightUavBmDO.class)
                        .eq(FlightUavBmDO::getFlightUavBmModelNo, flightUavBmDTO.getFlightUavBmModelNo()),false
        );
        if (existFlightUavBm != null) {
            LogUtil.error("保存飞行无人机品牌型号失败，系统内部型号编码已存在: {}", flightUavBmDTO.getFlightUavBmModelNo());
            throw new BizException(BizErrorCode.FLIGHT_UAV_BM_ALREADY_EXISTS.getCode(), BizErrorCode.FLIGHT_UAV_BM_ALREADY_EXISTS.getDesc());
        }


        FlightUavBmDO flightUavBmDO = flightUavBmConvert.convertToDO(flightUavBmDTO);
        flightUavBmDO.setGmtCreated(LocalDateTime.now());
        flightUavBmDO.setGmtModified(LocalDateTime.now());
        flightUavBmDO.setId(null);
        boolean success = flightUavBmRepository.save(flightUavBmDO);
        if (!success) {
            LogUtil.error("保存飞行无人机品牌型号失败: {}", flightUavBmDTO.getFlightUavBmNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("保存飞行无人机品牌型号成功: {}", flightUavBmDO.getId());
        return flightUavBmDO.getFlightUavBmNo();
    }

    @Override
    public boolean updateFlightUavBm(FlightUavBmDTO flightUavBmDTO) {
        if (flightUavBmDTO.getId() == null) {
            LogUtil.error("更新飞行无人机品牌型号失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // Validate required fields
        validateRequiredFields(flightUavBmDTO);
        
        // Validate parameter values
        validateParameterValues(flightUavBmDTO);
        
        // Validate picture count and format - assuming pictures are already validated by the frontend
        validatePictures(flightUavBmDTO);

        // 检查品牌型号是否存在
        FlightUavBmDO existFlightUavBm = flightUavBmRepository.getById(flightUavBmDTO.getId());
        if (existFlightUavBm == null) {
            LogUtil.error("更新飞行无人机品牌型号失败，品牌型号不存在: {}", flightUavBmDTO.getId());
            throw new BizException(BizErrorCode.FLIGHT_UAV_BM_NOT_FOUND.getCode(), BizErrorCode.FLIGHT_UAV_BM_NOT_FOUND.getDesc());
        }

        // 如果更新了品牌型号编码，需要检查是否与其他品牌型号冲突
        if (!existFlightUavBm.getFlightUavBmNo().equals(flightUavBmDTO.getFlightUavBmNo())) {
            FlightUavBmDO conflictFlightUavBm = flightUavBmRepository.getOne(
                    Wrappers.lambdaQuery(FlightUavBmDO.class)
                            .eq(FlightUavBmDO::getFlightUavBmNo, flightUavBmDTO.getFlightUavBmNo())
                            .ne(FlightUavBmDO::getId, flightUavBmDTO.getId())
            );
            if (conflictFlightUavBm != null) {
                LogUtil.error("更新飞行无人机品牌型号失败，系统内部型号编码已被其他品牌型号使用: {}", flightUavBmDTO.getFlightUavBmNo());
                throw new BizException(BizErrorCode.FLIGHT_UAV_BM_ALREADY_EXISTS.getCode(), BizErrorCode.FLIGHT_UAV_BM_ALREADY_EXISTS.getDesc());
            }
        }
        
        // 检查机型名称是否与其他记录重复 (MODEL34)
        if (!existFlightUavBm.getFlightUavBmName().equals(flightUavBmDTO.getFlightUavBmName())) {
            FlightUavBmDO duplicateNameModel = flightUavBmRepository.getOne(
                    Wrappers.lambdaQuery(FlightUavBmDO.class)
                            .eq(FlightUavBmDO::getFlightUavBmName, flightUavBmDTO.getFlightUavBmName())
                            .ne(FlightUavBmDO::getId, flightUavBmDTO.getId())
                            .eq(FlightUavBmDO::getIsDeleted, false)
            );
            if (duplicateNameModel != null) {
                LogUtil.error("更新飞行无人机品牌型号失败，机型名称已被其他品牌型号使用: {}", flightUavBmDTO.getFlightUavBmName());
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "该机型已存在");
            }
        }

        FlightUavBmDO flightUavBmDO = flightUavBmConvert.convertToDO(flightUavBmDTO);
        flightUavBmDO.setGmtModified(LocalDateTime.now());

        boolean success = flightUavBmRepository.updateById(flightUavBmDO);
        if (!success) {
            LogUtil.error("更新飞行无人机品牌型号失败: {}", flightUavBmDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("更新飞行无人机品牌型号成功: {}", flightUavBmDO.getId());
        return true;
    }

    @Override
    public FlightUavBmDTO getFlightUavBmById(Integer id) {
        if (id == null) {
            LogUtil.error("获取飞行无人机品牌型号失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightUavBmDO flightUavBmDO = flightUavBmRepository.getById(id);
        if (flightUavBmDO == null) {
            LogUtil.error("获取飞行无人机品牌型号失败，品牌型号不存在: {}", id);
            throw new BizException(BizErrorCode.FLIGHT_UAV_BM_NOT_FOUND.getCode(), BizErrorCode.FLIGHT_UAV_BM_NOT_FOUND.getDesc());
        }

        return flightUavBmConvert.convert(flightUavBmDO);
    }

    @Override
    public FlightUavBmDTO getFlightUavBmByNo(String flightUavBmNo) {
        if (StringUtils.isBlank(flightUavBmNo)) {
            LogUtil.error("获取飞行无人机品牌型号失败，系统内部型号编码为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightUavBmDO flightUavBmDO = flightUavBmRepository.getOne(
                Wrappers.lambdaQuery(FlightUavBmDO.class)
                        .eq(FlightUavBmDO::getFlightUavBmNo, flightUavBmNo)
        );
        if (flightUavBmDO == null) {
            LogUtil.error("获取飞行无人机品牌型号失败，根据系统内部型号编码未找到对应记录: {}", flightUavBmNo);
            throw new BizException(BizErrorCode.FLIGHT_UAV_BM_NOT_FOUND.getCode(), BizErrorCode.FLIGHT_UAV_BM_NOT_FOUND.getDesc());
        }

        return flightUavBmConvert.convert(flightUavBmDO);
    }

    /**
     * 根据产品编号获取飞行无人机品牌型号
     *
     * @param productNo 产品编号
     * @return 飞行无人机品牌型号数据
     */
    @Override
    public FlightUavBmDTO getFlightUavBmByProductNo(String productNo) {
        if (StringUtils.isBlank(productNo)) {
            LogUtil.error("获取飞行无人机品牌型号失败，产品编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 使用FlightProduct表的flight_uav_bm_list字段进行查询
        FlightProductDO flightProductDO = flightProductRepository.getOne(
                Wrappers.lambdaQuery(FlightProductDO.class)
                        .eq(FlightProductDO::getProductNo, productNo)
        );
        
        if (flightProductDO == null || flightProductDO.getFlightUavBmList() == null 
                || flightProductDO.getFlightUavBmList().length == 0) {
            LogUtil.error("获取飞行无人机品牌型号失败，对应产品编号的品牌型号不存在: {}", productNo);
            throw new BizException(BizErrorCode.FLIGHT_UAV_BM_NOT_FOUND.getCode(), BizErrorCode.FLIGHT_UAV_BM_NOT_FOUND.getDesc());
        }
        
        String flightUavBmNo = flightProductDO.getFlightUavBmList()[0];
        return getFlightUavBmByNo(flightUavBmNo);
    }

    @Override
    public CommonPage<FlightUavBmDTO> pageQueryFlightUavBm(FlightUavBmQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<FlightUavBmDO> queryWrapper = Wrappers.lambdaQuery(FlightUavBmDO.class)
                .in(CollectionUtil.isNotEmpty(queryDTO.getFlightUavBmNoList()), FlightUavBmDO::getFlightUavBmNo, queryDTO.getFlightUavBmNoList())
                .eq(StringUtils.isNotBlank(queryDTO.getFlightUavBmNo()), FlightUavBmDO::getFlightUavBmNo, queryDTO.getFlightUavBmNo())
                .like(StringUtils.isNotBlank(queryDTO.getFlightUavBmName()), FlightUavBmDO::getFlightUavBmName, queryDTO.getFlightUavBmName())
                .like(StringUtils.isNotBlank(queryDTO.getFlightUavBmModelNo()), FlightUavBmDO::getFlightUavBmModelNo, queryDTO.getFlightUavBmModelNo())
                .eq(ObjectUtil.isNotNull(queryDTO.getFlightUavCrewType()), FlightUavBmDO::getFlightUavCrewType, queryDTO.getFlightUavCrewType())
                .eq(ObjectUtil.isNotNull(queryDTO.getFlightUavWeightClassfication()), FlightUavBmDO::getFlightUavWeightClassfication, queryDTO.getFlightUavWeightClassfication())
                .eq(ObjectUtil.isNotNull(queryDTO.getFlightUavFlyType()), FlightUavBmDO::getFlightUavFlyType, queryDTO.getFlightUavFlyType())
                .ge(ObjectUtil.isNotNull(queryDTO.getMinFlightUavRadius()), FlightUavBmDO::getFlightUavRadius, queryDTO.getMinFlightUavRadius())
                .le(ObjectUtil.isNotNull(queryDTO.getMaxFlightUavRadius()), FlightUavBmDO::getFlightUavRadius, queryDTO.getMaxFlightUavRadius())
                .eq(queryDTO.getFlightUavSupportVedio() != null, FlightUavBmDO::getFlightUavSupportVedio, queryDTO.getFlightUavSupportVedio())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), FlightUavBmDO::getTenantId, queryDTO.getTenantId())
                .orderByDesc(FlightUavBmDO::getGmtCreated);

        List<FlightUavBmDO> flightUavBmDOList = flightUavBmRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(flightUavBmDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<FlightUavBmDTO> flightUavBmDTOList = flightUavBmConvert.convertList(flightUavBmDOList);
        PageInfo<FlightUavBmDTO> pageInfo = new PageInfo<>(flightUavBmDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), flightUavBmDTOList);
    }

    @Override
    public List<FlightUavBmDTO> listFlightUavBm(List<String> uavBmNoList) {
        FlightUavBmQueryDTO flightUavBmQueryDTO = new FlightUavBmQueryDTO();
        flightUavBmQueryDTO.setFlightUavBmNoList(uavBmNoList);
        flightUavBmQueryDTO.setPageNum(1);
        flightUavBmQueryDTO.setPageSize(1000);
        CommonPage<FlightUavBmDTO> flightUavBmDTOCommonPage = pageQueryFlightUavBm(flightUavBmQueryDTO);
        return flightUavBmDTOCommonPage.getList();
    }

    @Override
    public boolean deleteFlightUavBm(Integer id) {
        if (id == null) {
            LogUtil.error("删除飞行无人机品牌型号失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查品牌型号是否存在
        FlightUavBmDO existFlightUavBm = flightUavBmRepository.getById(id);
        if (existFlightUavBm == null) {
            LogUtil.error("删除飞行无人机品牌型号失败，品牌型号不存在: {}", id);
            throw new BizException(BizErrorCode.FLIGHT_UAV_BM_NOT_FOUND.getCode(), BizErrorCode.FLIGHT_UAV_BM_NOT_FOUND.getDesc());
        }

        // 检查是否被无人机关联使用
        long uavCount = flightUavRepository.count(
                Wrappers.lambdaQuery(FlightUavDO.class)
                        .eq(FlightUavDO::getFlightUavBmNo, existFlightUavBm.getFlightUavBmNo())
                        .eq(FlightUavDO::getIsDeleted, false)
        );
        if (uavCount > 0) {
            LogUtil.error("删除飞行无人机品牌型号失败，该机型已被无人机关联使用: {}", existFlightUavBm.getFlightUavBmNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "该机型已被无人机关联使用，不允许删除");
        }

        // 检查是否被订单关联使用
        long orderCount = orderProductUsageRepository.count(
                Wrappers.lambdaQuery(FlightOrderProductUsageDO.class)
                        .eq(FlightOrderProductUsageDO::getUavModel, existFlightUavBm.getFlightUavBmModelNo())
                        .eq(FlightOrderProductUsageDO::getIsDeleted, false)
        );
        if (orderCount > 0) {
            LogUtil.error("删除飞行无人机品牌型号失败，该机型已被订单关联使用: {}", existFlightUavBm.getFlightUavBmNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "该机型已被订单关联使用，不允许删除");
        }

        // 使用SQL直接检查是否被产品关联使用
        String flightUavBmNo = existFlightUavBm.getFlightUavBmNo();
        // 使用Mybatis-Plus的selectCount方法，结合SQL条件
        Long productCount = flightProductRepository.getBaseMapper().selectCount(
                Wrappers.<FlightProductDO>query()
                        .eq("is_deleted", false)
                        .apply("'" + flightUavBmNo + "' = ANY(flight_uav_bm_list)")
        );
        
        if (productCount != null && productCount > 0) {
            LogUtil.error("删除飞行无人机品牌型号失败，该机型已被服务产品关联使用: {}", flightUavBmNo);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "该机型已被服务产品关联使用，不允许删除");
        }

        boolean success = flightUavBmRepository.removeById(id);
        if (!success) {
            LogUtil.error("删除飞行无人机品牌型号失败: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("删除飞行无人机品牌型号成功: {}", id);
        return true;
    }

    /**
     * 校验必填字段
     *
     * @param flightUavBmDTO 飞行无人机品牌型号DTO
     */
    private void validateRequiredFields(FlightUavBmDTO flightUavBmDTO) {
        // MODEL04: 类目不能为空
        if (flightUavBmDTO.getFlightUavFlyType() == null) {
            LogUtil.error("飞行无人机品牌型号校验失败，机型类目为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请选择机型类目");
        }
        
        // MODEL07 & MODEL09: 品牌和机型名称不能为空 (共用同一字段)
        if (StringUtils.isBlank(flightUavBmDTO.getFlightUavBmName())) {
            LogUtil.error("飞行无人机品牌型号校验失败，品牌和机型名称为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入机型名称");
        }
        
        // MODEL11: 载人类型不能为空
        if (flightUavBmDTO.getFlightUavCrewType() == null) {
            LogUtil.error("飞行无人机品牌型号校验失败，载人类型为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请选择载人类型");
        }
        
        // MODEL13: 重量分类不能为空
        if (flightUavBmDTO.getFlightUavWeightClassfication() == null) {
            LogUtil.error("飞行无人机品牌型号校验失败，重量分类为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请选择重量分类");
        }
        
        // MODEL22: 视频拍摄支持不能为空
        if (flightUavBmDTO.getFlightUavSupportVedio() == null) {
            LogUtil.error("飞行无人机品牌型号校验失败，视频拍摄支持为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请选择视频拍摄支持情况");
        }
    }
    
    /**
     * 校验参数值
     *
     * @param flightUavBmDTO 飞行无人机品牌型号DTO
     */
    private void validateParameterValues(FlightUavBmDTO flightUavBmDTO) {
        // MODEL14: 最大飞行时间为正整数
        if (flightUavBmDTO.getFlightUavMaxFlyMinute() == null || flightUavBmDTO.getFlightUavMaxFlyMinute() <= 0) {
            LogUtil.error("飞行无人机品牌型号校验失败，最大飞行时间不是正整数: {}", flightUavBmDTO.getFlightUavMaxFlyMinute());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "最大飞行时间必须为大于0的正整数");
        }
        
        // MODEL15: 最大飞行半径为正数
        if (flightUavBmDTO.getFlightUavRadius() == null || flightUavBmDTO.getFlightUavRadius().compareTo(BigDecimal.ZERO) <= 0) {
            LogUtil.error("飞行无人机品牌型号校验失败，最大飞行半径不是正数: {}", flightUavBmDTO.getFlightUavRadius());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "最大飞行半径必须为大于0的正数");
        }
        
        // MODEL16: 最大续航里程为正数
        if (flightUavBmDTO.getFlightUavMaxFlyRange() == null || flightUavBmDTO.getFlightUavMaxFlyRange().compareTo(BigDecimal.ZERO) <= 0) {
            LogUtil.error("飞行无人机品牌型号校验失败，最大续航里程不是正数: {}", flightUavBmDTO.getFlightUavMaxFlyRange());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "最大续航里程必须为大于0的正数");
        }
        
        // MODEL17: 最大载物重量为正数
        if (flightUavBmDTO.getFlightUavMaxCarrierWeight() == null || flightUavBmDTO.getFlightUavMaxCarrierWeight().compareTo(BigDecimal.ZERO) <= 0) {
            LogUtil.error("飞行无人机品牌型号校验失败，最大载物重量不是正数: {}", flightUavBmDTO.getFlightUavMaxCarrierWeight());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "最大载物重量必须为大于0的正数");
        }
        
        // MODEL18: 最大飞行海拔高度为正整数
        if (flightUavBmDTO.getFlightUavMaxFlyHeight() == null || flightUavBmDTO.getFlightUavMaxFlyHeight() <= 0) {
            LogUtil.error("飞行无人机品牌型号校验失败，最大飞行海拔高度不是正整数: {}", flightUavBmDTO.getFlightUavMaxFlyHeight());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "最大飞行海拔高度必须为大于0的正整数");
        }
    }
    
    /**
     * 校验图片数量和格式
     *
     * @param flightUavBmDTO 飞行无人机品牌型号DTO
     */
    private void validatePictures(FlightUavBmDTO flightUavBmDTO) {
        // MODEL25-28: 图片数量不能超过5张
        if (flightUavBmDTO.getFlightUavPictures() != null && flightUavBmDTO.getFlightUavPictures().length > 5) {
            LogUtil.error("飞行无人机品牌型号校验失败，图片数量超过5张: {}", flightUavBmDTO.getFlightUavPictures().length);
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "最多上传5张图片");
        }
        
        // 图片格式和大小校验通常在前端或文件上传服务中处理
    }
} 