package com.deepinnet.skyflow.operationcenter.service.flow.context;

import com.deepinnet.skyflow.operationcenter.dto.FlightOrderCreateDTO;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/19
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightOrderCreateContext {

    private FlightOrderCreateDTO flightOrderCreateDTO;

}
