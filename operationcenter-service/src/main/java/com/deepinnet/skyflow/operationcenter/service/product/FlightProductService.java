package com.deepinnet.skyflow.operationcenter.service.product;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductStatQueryDTO;
import com.deepinnet.skyflow.operationcenter.vo.FlightProductStatVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightProductVO;

import java.util.List;

/**
 * 飞行产品服务接口
 *
 * <AUTHOR>
 */
public interface FlightProductService {

    /**
     * 保存飞行产品
     *
     * @param flightProductDTO 飞行产品数据
     * @return 已保存的飞行产品ID
     */
    String saveFlightProduct(FlightProductDTO flightProductDTO);
    
    /**
     * 创建飞行产品（包含关联实体）
     *
     * @param flightProductDTO 飞行产品数据
     * @return 已保存的飞行产品ID
     */
    String createFlightProduct(FlightProductDTO flightProductDTO);

    /**
     * 更新飞行产品
     *
     * @param flightProductDTO 飞行产品数据
     * @return 更新是否成功
     */
    boolean updateFlightProduct(FlightProductDTO flightProductDTO);
    
    /**
     * 更新飞行产品（包含关联实体）
     *
     * @param flightProductDTO 飞行产品数据
     * @return 更新是否成功
     */
    boolean updateFlightProductWithRelated(FlightProductDTO flightProductDTO);

    /**
     * 根据ID获取飞行产品
     *
     * @param id 飞行产品ID
     * @return 飞行产品数据
     */
    FlightProductDTO getFlightProductById(Integer id);
    
    /**
     * 根据ID获取飞行产品（包含关联实体）
     *
     * @param id 飞行产品ID
     * @return 飞行产品数据
     */
    FlightProductVO getFlightProductWithRelatedById(Integer id);

    /**
     * 根据产品编号获取飞行产品
     *
     * @param productNo 产品编号
     * @return 飞行产品数据
     */
    FlightProductDTO getFlightProductByNo(String productNo);
    
    /**
     * 根据产品编号获取飞行产品（包含关联实体）
     *
     * @param productNo 产品编号
     * @return 飞行产品视图对象
     */
    FlightProductVO getFlightProductWithRelatedByNo(String productNo);

    /**
     * 分页查询飞行产品
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    CommonPage<FlightProductDTO> pageQueryFlightProduct(FlightProductQueryDTO queryDTO);
    
    /**
     * 分页查询飞行产品（包含关联实体）
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    CommonPage<FlightProductDTO> pageQueryFlightProductWithRelated(FlightProductQueryDTO queryDTO);

    /**
     * 根据产品编号列表批量查询
     * @param productNoList
     * @return
     */
    List<FlightProductDTO> listByProductNoList(List<String> productNoList);

    /**
     * 删除飞行产品
     *
     * @param id 飞行产品ID
     * @return 删除是否成功
     */
    boolean deleteFlightProduct(Integer id);
    
    /**
     * 删除飞行产品（包含关联实体）
     *
     * @param id 飞行产品ID
     * @return 删除是否成功
     */
    boolean deleteFlightProductWithRelated(Integer id);

    /**
     * 统计各类目下的飞行产品数量
     *
     * @param queryDTO 查询条件
     * @return 统计结果列表
     */
    FlightProductStatVO statCategoryFlightProduct(FlightProductStatQueryDTO queryDTO);
} 