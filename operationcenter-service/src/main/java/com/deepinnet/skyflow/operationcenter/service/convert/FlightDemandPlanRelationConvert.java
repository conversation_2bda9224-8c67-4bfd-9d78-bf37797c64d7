package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandPlanRelationDO;
import com.deepinnet.skyflow.operationcenter.dto.DemandSyncPlanBindDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandPlanRelationDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 飞行需求与计划关联转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface FlightDemandPlanRelationConvert {

    FlightDemandPlanRelationConvert INSTANCE = Mappers.getMapper(FlightDemandPlanRelationConvert.class);

    /**
     * DTO转DO
     *
     * @param dto DTO对象
     * @return DO对象
     */
    FlightDemandPlanRelationDO convertToDO(DemandSyncPlanBindDTO dto);

    /**
     * DO转DTO
     *
     * @param entity DO对象
     * @return DTO对象
     */
    DemandSyncPlanBindDTO convert(FlightDemandPlanRelationDO entity);
} 