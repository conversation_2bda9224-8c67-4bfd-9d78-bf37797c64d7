package com.deepinnet.skyflow.operationcenter.service.task.worker;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ScheduleTaskDO;
import com.deepinnet.skyflow.operationcenter.dal.enums.ScheduleTaskTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.task.ScheduleTaskService;
import org.apache.commons.collections4.CollectionUtils;

import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.List;

import static com.deepinnet.localdata.integration.error.BizErrorCode.IDEMPOTENT_REQUEST_ERROR;

/**
 * <AUTHOR> wong
 * @create 2022-12-01 08:28
 */

@Component
public abstract class AbstractBaseTaskWorker {
    @Resource
    private ScheduleTaskService scheduleTaskService;

    @Resource
    private TransactionTemplate transactionTemplate;

    private static final String EXECUTE_LOG_PATTERN = "EXECUTE,{},{},{},{}ms,{}";

    private static final Integer MAX_RETRY_COUNT = 10;

    private static final Integer TWO_MINUTES = 2 * 60 * 1000;
    protected static final String TRIGGER_TIME = "triggerTime";


    public abstract ScheduleTaskTypeEnum getTaskType();

    public static void main(String[] args) {
        String s = String.valueOf(DateUtil.current());
        System.out.println(s);
    }
    public void doCommit(ScheduleTaskDO scheduleTask) {
        // 获取必要的业务参数
        long startTime = System.currentTimeMillis();
        Long taskId = scheduleTask.getId();
        String bizNo = scheduleTask.getBizNo();
        String tenantId = scheduleTask.getTenantId();
        MDC.put(TRIGGER_TIME, String.valueOf(scheduleTask.getTriggerTime()));
        try {
            // 更新任务状态到执行中
            scheduleTaskService.updateTaskExecuting(taskId, bizNo, scheduleTask.getStatus(), System.currentTimeMillis(), tenantId);

            // 校验业务状态
            checkBizStatus(taskId, bizNo, scheduleTask.getTenantId());

            // 更新业务状态
            updateBizStatus(bizNo, scheduleTask.getTenantId());

            // 修改任务状态为成功&做其他的业务动作如发消息等
            transactionTemplate.executeWithoutResult(action -> {
                scheduleTaskService.updateTaskSuccess(taskId, bizNo, tenantId);

                doOtherBiz(scheduleTask);
            });
        } catch (BizException e) {
            // 异常场景：幂等处理
            if (StrUtil.equals(e.getErrorCode(), IDEMPOTENT_REQUEST_ERROR.getCode())) {
                LogUtil.error("执行任务幂等处理,任务id：{},业务单号：{}", taskId, bizNo);
                // 将任务状态设置为成功
                scheduleTaskService.updateTaskSuccess(taskId, bizNo, tenantId);
                return;
            }

            // 处理异常场景
            processException(e, scheduleTask);

            long endTime = System.currentTimeMillis();
            LogUtil.info("SCHEDULE-TASK-LOG", EXECUTE_LOG_PATTERN, scheduleTask.getId(), scheduleTask.getBizNo(), false, endTime - startTime, e.getErrorCode());
            return;
        } catch (Exception e) {
            makeTaskFailAndUpdateRetryCount(taskId, bizNo, scheduleTask.getRetryCount(), tenantId, scheduleTask.getTriggerTime(), e);
            LogUtil.error("任务执行失败，原因为：未知异常，任务id为：{}, 单号为：{}，异常堆栈为：{}", taskId, bizNo, e);
            LogUtil.info("SCHEDULE-TASK-LOG", EXECUTE_LOG_PATTERN, scheduleTask.getId(), scheduleTask.getBizNo(), false, BizErrorCode.UNKNOWN_EXCEPTION.getCode());
            return;
        }

        long endTime = System.currentTimeMillis();
        LogUtil.info("SCHEDULE-TASK-LOG", EXECUTE_LOG_PATTERN, scheduleTask.getId(), scheduleTask.getBizNo(), true, endTime - startTime, "-");
        LogUtil.info("任务执行成功，任务id为：{}, 单号为：{}", taskId, bizNo);
    }

    protected abstract void doOtherBiz(ScheduleTaskDO scheduleTask);

    /**
     * 处理异常场景
     */
    protected void processException(BizException exception, ScheduleTaskDO scheduleTask) {
        List<String> needTerminatedErrorCodeList = needTerminatedErrorCode();
        if (CollectionUtils.isEmpty(needTerminatedErrorCodeList)) {
            makeTaskFailAndUpdateRetryCount(scheduleTask.getId(), scheduleTask.getBizNo(), scheduleTask.getRetryCount(), scheduleTask.getTenantId(), scheduleTask.getTriggerTime(), exception);
        } else {
            if (needTerminatedErrorCodeList.contains(exception.getErrorCode())) {
                makeTaskTerminated(scheduleTask.getId(), scheduleTask.getBizNo(), scheduleTask.getTenantId());
            } else {
                makeTaskFailAndUpdateRetryCount(scheduleTask.getId(), scheduleTask.getBizNo(), scheduleTask.getRetryCount(), scheduleTask.getTenantId(), scheduleTask.getTriggerTime(), exception);
            }
        }
    }

    /**
     * 需要终止的异常错误码
     *
     * @return
     */
    protected abstract List<String> needTerminatedErrorCode();

    /**
     * 校验业务状态
     *
     * @param taskId
     * @param bizNo
     * @param tenantId
     */
    protected abstract void checkBizStatus(Long taskId, String bizNo, String tenantId);

    /**
     * 更新业务状态
     *
     * @param bizNo
     * @param tenantId
     */
    protected abstract void updateBizStatus(String bizNo, String tenantId);

    protected void makeTaskFailAndUpdateRetryCount(Long taskId, String bizNo, Integer retryCount, String tenantId, Long triggerTime, Exception e) {
        if (e instanceof BizException) {
            BizException bizException = (BizException) e;
            LogUtil.error("执行任务失败，原因为{},任务id：{},业务单号：{},异常堆栈：{}", bizException.getMessage(), taskId, bizNo, e);
        } else {
            LogUtil.error("执行任务失败，任务id：{},业务单号：{},异常堆栈：{}", taskId, bizNo, e);
        }

        int curRetryCount = retryCount == null ? 0 : retryCount;

        if (curRetryCount >= MAX_RETRY_COUNT) {
            LogUtil.error("执行任务失败，任务已经达到最大重试次数,任务id：{},业务单号：{},异常堆栈：{}", e);
            scheduleTaskService.updateTaskTerminated(taskId, bizNo, tenantId);
        } else {
            // 执行失败的任务，下次触发时间为上一次触发时间后的2分钟
            scheduleTaskService.updateTaskFail(taskId, bizNo, curRetryCount + 1, triggerTime + TWO_MINUTES, tenantId);
        }
    }

    protected void makeTaskTerminated(Long taskId, String bizNo, String tenantId) {
        scheduleTaskService.updateTaskTerminated(taskId, bizNo, tenantId);
    }
}
