package com.deepinnet.skyflow.operationcenter.service.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderApprovalDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderFileDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderFlyingInfoDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderProductUsageDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderApprovalRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderFileRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderFlyingInfoRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderProductUsageRepository;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductDTO;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightOrderConvert;
import com.deepinnet.skyflow.operationcenter.service.product.FlightProductService;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderFlyingInfoVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderProductUsageVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 飞行订单产品使用记录帮助类
 */

@Component
public class FlightOrderProductUsageHelper {

    @Resource
    private FlightOrderProductUsageRepository flightOrderProductUsageRepository;

    @Resource
    private FlightProductService flightProductService;

    @Resource
    private FlightOrderFileRepository flightOrderFileRepository;

    @Resource
    private FlightOrderFlyingInfoRepository flightOrderFlyingInfoRepository;

    @Resource
    private FlightOrderConvert flightOrderConvert;

    @Resource
    private FlightOrderApprovalRepository flightOrderApprovalRepository;

    /**
     * 批量查询产品使用记录
     *
     * @param orderNoList 订单号列表
     * @param tenantId 租户ID
     * @return 产品使用记录列表
     */
    public List<FlightOrderProductUsageDO> batchQueryProductUsage(List<String> orderNoList, String tenantId) {
        return flightOrderProductUsageRepository.list(
                Wrappers.lambdaQuery(FlightOrderProductUsageDO.class)
                        .in(FlightOrderProductUsageDO::getOrderNo, orderNoList)
                        .eq(FlightOrderProductUsageDO::getTenantId, tenantId)
                        .orderByDesc(FlightOrderProductUsageDO::getGmtCreated)
                        .orderByDesc(FlightOrderProductUsageDO::getId)
        );
    }

    /**
     * 批量查询飞行信息
     *
     * @param orderNoList 订单号列表
     * @param tenantId 租户ID
     * @return 飞行信息
     */
    public List<FlightOrderFlyingInfoDO> batchQueryFlyingInfo(List<String> orderNoList, String tenantId) {
        return flightOrderFlyingInfoRepository.list(
                Wrappers.lambdaQuery(FlightOrderFlyingInfoDO.class)
                        .in(FlightOrderFlyingInfoDO::getOrderNo, orderNoList)
                        .eq(FlightOrderFlyingInfoDO::getTenantId, tenantId)
                        .orderByDesc(FlightOrderFlyingInfoDO::getGmtCreated)
                        .orderByDesc(FlightOrderFlyingInfoDO::getId)
        );
    }

    /**
     * 设置产品信息到产品使用记录
     *
     * @param productUsageVOList 产品使用记录VO列表
     */
    public void setProductInfo(List<FlightOrderProductUsageVO> productUsageVOList) {
        if (CollUtil.isEmpty(productUsageVOList)) {
            return;
        }

        List<String> productNoList = productUsageVOList.stream()
                .map(FlightOrderProductUsageVO::getProductNo)
                .collect(Collectors.toList());

        List<FlightProductDTO> flightProductDTOS = flightProductService.listByProductNoList(productNoList);

        if (CollUtil.isNotEmpty(flightProductDTOS)) {
            Map<String, FlightProductDTO> productNoMap = flightProductDTOS.stream()
                    .collect(Collectors.toMap(FlightProductDTO::getProductNo, Function.identity()));

            productUsageVOList.forEach(productUsage ->
                    productUsage.setFlightProduct(productNoMap.get(productUsage.getProductNo()))
            );
        }
    }

    /**
     * 设置产品使用记录和产品信息
     *
     * @param flightOrderVO 订单VO
     * @param orderNo 订单号
     * @param tenantId 租户ID
     */
    public void setProductUsageAndProductInfo(FlightOrderVO flightOrderVO, String orderNo, String tenantId) {
        List<FlightOrderProductUsageDO> productUsageDOList = batchQueryProductUsage(List.of(orderNo), tenantId);

        if (CollUtil.isNotEmpty(productUsageDOList)) {
            List<FlightOrderProductUsageVO> productUsageVOList = productUsageDOList.stream()
                    .map(flightOrderConvert::convertToProductUsageVO)
                    .collect(Collectors.toList());

            setProductInfo(productUsageVOList);
            flightOrderVO.setProductUsageList(productUsageVOList);
        }

        List<FlightOrderFileDO> list = flightOrderFileRepository.list(Wrappers.<FlightOrderFileDO>lambdaQuery()
                .eq(FlightOrderFileDO::getOrderNo, orderNo));

        if (CollUtil.isNotEmpty(list)) {
            flightOrderVO.setFiles(flightOrderConvert.convertToFlightOrderFileList(list));
        }

        FlightOrderFlyingInfoDO flyingInfoDO = flightOrderFlyingInfoRepository.getOne(Wrappers.<FlightOrderFlyingInfoDO>lambdaQuery()
                .eq(FlightOrderFlyingInfoDO::getOrderNo, orderNo));

        if (ObjectUtil.isNotNull(flyingInfoDO)) {
            flightOrderVO.setFlyingInfo(flightOrderConvert.convertToFlightOrderFlyingInfoVO(flyingInfoDO));
        }

        List<FlightOrderApprovalDO> approvalInfoList = flightOrderApprovalRepository.list(Wrappers.<FlightOrderApprovalDO>lambdaQuery()
                .eq(FlightOrderApprovalDO::getOrderNo, orderNo)
                .orderByAsc(FlightOrderApprovalDO::getApprovalTime));

        if (CollUtil.isNotEmpty(approvalInfoList)) {
            flightOrderVO.setApprovalInfos(flightOrderConvert.convertToFlightOrderApprovalListVO(approvalInfoList));
        }
    }

    /**
     * 批量设置产品使用记录和产品信息
     *
     * @param flightOrderList 订单VO列表
     * @param tenantId 租户ID
     */
    public void batchSetProductUsageAndProductInfo(List<FlightOrderVO> flightOrderList, String tenantId) {
        if (CollUtil.isEmpty(flightOrderList)) {
            return;
        }

        List<String> orderNoList = flightOrderList.stream()
                .map(FlightOrderVO::getOrderNo)
                .collect(Collectors.toList());

        List<FlightOrderProductUsageDO> productUsageDOList = batchQueryProductUsage(orderNoList, tenantId);
        List<FlightOrderFlyingInfoDO> flightOrderFlyingInfoDOS = batchQueryFlyingInfo(orderNoList, tenantId);

        Map<String, List<FlightOrderProductUsageDO>> orderProductUsageMap =
                (productUsageDOList == null || productUsageDOList.isEmpty()) ?
                        Collections.emptyMap() :
                        productUsageDOList.stream().collect(Collectors.groupingBy(FlightOrderProductUsageDO::getOrderNo));

        Map<String, FlightOrderFlyingInfoDO> orderFlyingInfoMap =
                (flightOrderFlyingInfoDOS == null || flightOrderFlyingInfoDOS.isEmpty()) ?
                        Collections.emptyMap() :
                        flightOrderFlyingInfoDOS.stream().collect(Collectors.toMap(FlightOrderFlyingInfoDO::getOrderNo, Function.identity()));

        flightOrderList.forEach(order -> {
            String orderNo = order.getOrderNo();

            List<FlightOrderProductUsageDO> usageList = orderProductUsageMap.get(orderNo);
            if (CollUtil.isNotEmpty(usageList)) {
                List<FlightOrderProductUsageVO> productUsageVOList = usageList.stream()
                        .map(flightOrderConvert::convertToProductUsageVO)
                        .collect(Collectors.toList());

                setProductInfo(productUsageVOList);
                order.setProductUsageList(productUsageVOList);
            }

            FlightOrderFlyingInfoDO flyingInfoDO = orderFlyingInfoMap.get(orderNo);
            if (ObjectUtil.isNotNull(flyingInfoDO)) {
                order.setFlyingInfo(flightOrderConvert.convertToFlightOrderFlyingInfoVO(flyingInfoDO));
            }
        });
    }
} 