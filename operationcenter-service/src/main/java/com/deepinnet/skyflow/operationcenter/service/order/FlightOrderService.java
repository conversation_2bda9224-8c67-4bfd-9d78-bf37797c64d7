package com.deepinnet.skyflow.operationcenter.service.order;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.deepinnet.spatiotemporalplatform.vo.FlightPlanVO;

/**
 * 飞行订单服务接口
 *
 * <AUTHOR>
 */
public interface FlightOrderService {

    /**
     * 根据订单编号获取飞行订单
     *
     * @param dto 订单查询对象
     * @return 飞行订单数据
     */
    FlightOrderVO getFlightOrderByNo(OrderQueryDTO dto);

    /**
     * 根据订单编号获取飞行订单
     *
     * @param dto 订单查询对象
     * @return 飞行订单数据
     */
    CommonPage<FlightOrderVO> getFlightOrderListByPage(OrderPageQueryDTO dto);

    /**
     * 订单审核
     * @param dto 审核参数
     * @return 审核结果
     */
    Boolean orderApprove(OrderApproveDTO dto);

    /**
     * 创建订单
     * @param dto 创建参数
     * @return 创建结果
     */
    String createOrder(FlightOrderCreateDTO dto);

    /**
     * 计划列表查询
     * @param dto 查询参数
     * @return 计划列表
     */
    CommonPage<FlightPlanVO> getFlightPlanListByPage(FlightPlanPageQueryDTO dto);

    /**
     * 计划详情查询
     * @param planId 计划编号
     * @return 计划详情
     */
    FlightPlanVO getFlightPlanDetail(String planId);

    /**
     * 变更订单状态
     * @param bizNo 订单编号
     * @param code 状态码
     * @param tenantId 租户ID
     */
    void updateOrderStatus(String bizNo, String code, String tenantId);

    /**
     * 订单用量扣减
     * @param dto 设置参数
     * @return 扣减是否成功
     */
    Boolean orderProductUsage(FlightOrderProductUsageDTO dto);

    /**
     * 需求计划单删除
     * @param dto 订单详情查询dto
     */
    Boolean deleteOrder(OrderQueryDTO dto);

}