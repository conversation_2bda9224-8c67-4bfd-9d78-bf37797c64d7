package com.deepinnet.skyflow.operationcenter.service.task;


import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ScheduleTaskDO;
import com.deepinnet.skyflow.operationcenter.dal.model.UpdateTaskCondition;
import com.deepinnet.skyflow.operationcenter.dal.repository.ScheduleTaskRepository;
import com.deepinnet.skyflow.operationcenter.enums.ScheduleTaskStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2022-10-25 13:46
 */
@Service("scheduleTaskService")
public class ScheduleTaskService {
    @Resource
    private ScheduleTaskRepository scheduleTaskRepository;

    public List<ScheduleTaskDO> listTasks(List<String> types, List<String> statusList, Long triggerTime, Integer limitCount) {
        return scheduleTaskRepository.getAllByTypeAndStatusAndTriggerTime(types, statusList, triggerTime, limitCount);
    }

    public void batchInsertScheduleTasks(List<ScheduleTaskDO> scheduleTasks) {
        try {
            scheduleTaskRepository.batchSave(scheduleTasks);
        } catch (Exception e) {
            LogUtil.error("插入定时任务失败，任务信息：{},异常堆栈信息：{}", JSONUtil.toJsonStr(scheduleTasks), e);
            throw new BizException(BizErrorCode.INSERT_TASK_ERROR.getCode(), BizErrorCode.INSERT_TASK_ERROR.getDesc());
        }
    }

    public void updateTaskExecuting(Long taskId, String bizNo, String originalStatus, Long executeTime, String tenantId) {
        this.updateScheduleTask(
                taskId,
                bizNo,
                originalStatus,
                ScheduleTaskStatusEnum.EXECUTING.getStatus(),
                null,
                null,
                executeTime,
                tenantId);
    }

    public void updateTaskSuccess(Long taskId, String bizNo, String tenantId) {
        this.updateScheduleTask(
                taskId,
                bizNo,
                ScheduleTaskStatusEnum.EXECUTING.getStatus(),
                ScheduleTaskStatusEnum.SUCCESS.getStatus(),
                null,
                null,
                null,
                tenantId);
    }

    public void updateTaskFail(Long taskId, String bizNo, Integer retryCount, Long nextTriggerTime, String tenantId) {
        this.updateScheduleTask(
                taskId,
                bizNo,
                ScheduleTaskStatusEnum.EXECUTING.getStatus(),
                ScheduleTaskStatusEnum.FAIL.getStatus(),
                retryCount,
                nextTriggerTime,
                null,
                tenantId);
    }

    public void updateTaskTerminated(Long taskId, String bizNo, String tenantId) {
        this.updateScheduleTask(
                taskId,
                bizNo,
                ScheduleTaskStatusEnum.EXECUTING.getStatus(),
                ScheduleTaskStatusEnum.TERMINATED.getStatus(),
                null,
                null,
                null,
                tenantId);
    }

    private void updateScheduleTask(Long taskId, String bizNo, String originalStatus, String targetStatus,
                                    Integer retryCount, Long nextTriggerTime, Long executeTime, String tenantId) {
        try {
            UpdateTaskCondition updateTaskCondition = new UpdateTaskCondition();
            updateTaskCondition.setId(taskId);
            updateTaskCondition.setOriginalStatus(originalStatus);
            updateTaskCondition.setTargetStatus(targetStatus);
            updateTaskCondition.setRetryCount(retryCount);
            updateTaskCondition.setTenantId(tenantId);
            updateTaskCondition.setTriggerTime(nextTriggerTime);
            updateTaskCondition.setExecuteTime(executeTime);
            boolean isSuccess = scheduleTaskRepository.updateTaskByCondition(updateTaskCondition);
            if (!isSuccess) {
                LogUtil.error("更新任务状态失败，任务号：{}，业务号：{}", taskId, bizNo);
                throw new BizException(BizErrorCode.UPDATE_TASK_STATUS_ERROR.getCode(), BizErrorCode.UPDATE_TASK_STATUS_ERROR.getDesc());
            }
        } catch (Exception e) {
            LogUtil.error("更新任务状态失败，任务号：{}，业务号：{},异常堆栈信息：{}", taskId, bizNo, e);
            throw new BizException(BizErrorCode.UPDATE_TASK_STATUS_ERROR.getCode(), BizErrorCode.UPDATE_TASK_STATUS_ERROR.getDesc());
        }
    }

    public void deleteTask(String bizNo, String taskType, String tenantId) {
        try {
            boolean isSuccess = scheduleTaskRepository.remove(Wrappers.<ScheduleTaskDO>lambdaQuery()
                    .eq(ScheduleTaskDO::getBizNo, bizNo)
                    .eq(ScheduleTaskDO::getTaskType, taskType)
                    .eq(ScheduleTaskDO::getTenantId, tenantId));
            if (!isSuccess) {
                LogUtil.error("产出任务状态失败，业务号：{}，业务类型：{}", bizNo, taskType);
                throw new BizException(BizErrorCode.TASK_DELETE_ERROR.getCode(), BizErrorCode.TASK_DELETE_ERROR.getDesc());
            }
        } catch (Exception e) {
            LogUtil.error("产出任务状态失败，业务号：{}，业务类型：{}, 异常堆栈信息:{}", bizNo, taskType, e);
            throw new BizException(BizErrorCode.TASK_DELETE_ERROR.getCode(), BizErrorCode.TASK_DELETE_ERROR.getDesc());
        }
    }
}
