package com.deepinnet.skyflow.operationcenter.service.product;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.CategoryDTO;
import com.deepinnet.skyflow.operationcenter.dto.CategoryQueryDTO;

/**
 * 类目服务接口
 *
 * <AUTHOR>
 */
public interface CategoryService {

    /**
     * 保存类目
     *
     * @param categoryDTO 类目数据
     * @return 已保存的类目ID
     */
    String saveCategory(CategoryDTO categoryDTO);

    /**
     * 更新类目
     *
     * @param categoryDTO 类目数据
     * @return 更新是否成功
     */
    boolean updateCategory(CategoryDTO categoryDTO);

    /**
     * 根据ID获取类目
     *
     * @param id 类目ID
     * @return 类目数据
     */
    CategoryDTO getCategoryById(Integer id);

    /**
     * 根据类目编号获取类目
     *
     * @param categoryNo 类目编号
     * @return 类目数据
     */
    CategoryDTO getCategoryByNo(String categoryNo);

    /**
     * 分页查询类目
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    CommonPage<CategoryDTO> pageQueryCategory(CategoryQueryDTO queryDTO);

    /**
     * 删除类目
     *
     * @param id 类目ID
     * @return 删除是否成功
     */
    boolean deleteCategory(Integer id);
} 