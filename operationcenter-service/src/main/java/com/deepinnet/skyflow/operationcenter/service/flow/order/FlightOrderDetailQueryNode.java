package com.deepinnet.skyflow.operationcenter.service.flow.order;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderRepository;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightOrderConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.flow.context.OrderApproveContext;
import com.deepinnet.skyflow.operationcenter.service.helper.FlightOrderProductUsageHelper;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.yomahub.liteflow.annotation.LiteflowCmpDefine;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;

import javax.annotation.Resource;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@LiteflowComponent(value = "flightOrderDetailQueryNode", name = "订单详情查询节点")
@LiteflowCmpDefine
public class FlightOrderDetailQueryNode {

    @Resource
    private FlightOrderRepository flightOrderRepository;

    @Resource
    private FlightOrderConvert flightOrderConvert;

    @Resource
    private FlightOrderProductUsageHelper flightOrderProductUsageHelper;

    @LiteflowMethod(LiteFlowMethodEnum.PROCESS)
    public void process(NodeComponent bindCmp) {
        OrderApproveContext contextBean = bindCmp.getContextBean(OrderApproveContext.class);

        FlightOrderDO flightOrderDO = flightOrderRepository.getOne(
                Wrappers.lambdaQuery(FlightOrderDO.class)
                        .eq(FlightOrderDO::getOrderNo, contextBean.getOrderNo())
                        .eq(FlightOrderDO::getOrderType, contextBean.getOrderType())
                        .eq(FlightOrderDO::getTenantId, contextBean.getTenantId()));

        if (ObjectUtil.isNull(flightOrderDO)) {
            LogUtil.error("获取订单失败，订单:{}, 类型:{}, 不存在, tenantId:{}, userNo:{}", contextBean.getOrderNo(), contextBean.getOrderType(), contextBean.getTenantId(), contextBean.getUserNo());
            throw new BizException(BizErrorCode.ORDER_NOT_FOUND.getCode(), BizErrorCode.ORDER_NOT_FOUND.getDesc());
        }

        FlightOrderVO flightOrderVO = flightOrderConvert.convertToVO(flightOrderDO);

        flightOrderProductUsageHelper.setProductUsageAndProductInfo(flightOrderVO, contextBean.getOrderNo(), contextBean.getTenantId());

        contextBean.setFlightOrder(flightOrderVO);
    }
}
