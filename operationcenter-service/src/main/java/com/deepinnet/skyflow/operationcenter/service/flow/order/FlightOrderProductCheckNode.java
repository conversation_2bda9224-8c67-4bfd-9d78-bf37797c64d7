package com.deepinnet.skyflow.operationcenter.service.flow.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.enums.FlightProductTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.ValidityPeriodEnum;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.flow.context.FlightOrderCreateContext;
import com.deepinnet.skyflow.operationcenter.service.product.FlightProductService;
import com.yomahub.liteflow.annotation.LiteflowCmpDefine;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@LiteflowComponent(value = "flightOrderProductCheckNode", name = "订单产品校验节点")
@LiteflowCmpDefine
public class FlightOrderProductCheckNode {

    private static final String PRODUCT_NOT_FOUND_MSG = "产品编码:{} 不存在，请检查产品编码是否正确";
    private static final String PRICE_NOT_FOUND_MSG = "产品编码:{} 价格为空，请检查产品编码是否正确";
    private static final String PRICE_TYPE_NOT_FOUND_MSG = "产品编码:{} 价格类型:{} 不存在，请检查产品编码是否正确";
    private static final String PRODUCT_TYPE_NOT_FOUND_MSG = "产品编码:{} 产品类型:{} 不存在，请检查产品编码是否正确";

    @Resource
    private FlightProductService flightProductService;

    @LiteflowMethod(LiteFlowMethodEnum.PROCESS)
    public void process(NodeComponent bindCmp) {
        FlightOrderCreateContext contextBean = bindCmp.getContextBean(FlightOrderCreateContext.class);

        FlightOrderCreateDTO flightOrderCreateDTO = contextBean.getFlightOrderCreateDTO();

        if (StrUtil.equals(flightOrderCreateDTO.getOrderType(), OrderTypeEnum.DEMAND_PLAN.getCode())
                && CollUtil.isEmpty(flightOrderCreateDTO.getProducts())) {
            return;
        }

        List<String> productNo = flightOrderCreateDTO.getProducts()
                .stream().map(FlightOrderProductDTO::getProductNo).collect(Collectors.toList());

        List<FlightProductDTO> flightProductList = flightProductService.listByProductNoList(productNo);

        // 校验产品数量是否与下单吻合
        checkProductList(flightProductList, productNo, flightOrderCreateDTO);

        // 校验主产品
        checkMainProduct(flightProductList, flightOrderCreateDTO);

        // 计算各产品价格
        if (StrUtil.equals(OrderTypeEnum.NORMAL.getCode(), flightOrderCreateDTO.getOrderType())) {
            setPrice(flightProductList, flightOrderCreateDTO);
        }

    }

    private static void setPrice(List<FlightProductDTO> flightProductList, FlightOrderCreateDTO flightOrderCreateDTO) {
        Map<String, FlightProductDTO> productNoMap = flightProductList.stream()
                .collect(Collectors.toMap(FlightProductDTO::getProductNo, Function.identity()));

        LocalDateTime current = LocalDateTime.now();
        BigDecimal totalPrice = flightOrderCreateDTO.getProducts().stream()
                .map(product -> calculateProductPrice(product, productNoMap, current))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        flightOrderCreateDTO.setOrderAmount(totalPrice.toString());
    }

    private static BigDecimal calculateProductPrice(FlightOrderProductDTO product, Map<String, FlightProductDTO> productNoMap, LocalDateTime current) {
        FlightProductDTO flightProductDTO = Optional.ofNullable(productNoMap.get(product.getProductNo()))
                .orElseThrow(() -> {
                    LogUtil.error(PRODUCT_NOT_FOUND_MSG, product.getProductNo());
                    return new BizException(BizErrorCode.PRODUCT_SERVICE_NOT_FOUND_ERROR.getCode(), BizErrorCode.PRODUCT_SERVICE_NOT_FOUND_ERROR.getDesc());
                });

        Optional.ofNullable(flightProductDTO.getFlightUavBm())
                .ifPresent(uavBm -> product.setUavModel(uavBm.getFlightUavBmName()));

        List<PriceDTO> priceList = Optional.ofNullable(flightProductDTO.getPriceList())
                .filter(CollUtil::isNotEmpty)
                .orElseThrow(() -> {
                    LogUtil.error(PRICE_NOT_FOUND_MSG, product.getProductNo());
                    return new BizException(BizErrorCode.PRODUCT_PRICE_NOT_FOUND.getCode(), BizErrorCode.PRODUCT_PRICE_NOT_FOUND.getDesc());
                });

        Map<ValidityPeriodEnum, PriceDTO> pricePeriodEnumMap = priceList.stream()
                .collect(Collectors.toMap(PriceDTO::getValidityPeriod, Function.identity()));

        PriceDTO priceDTO = Optional.ofNullable(pricePeriodEnumMap.get(product.getValidityPeriodType()))
                .orElseThrow(() -> {
                    LogUtil.error(PRICE_TYPE_NOT_FOUND_MSG, product.getProductNo(), product.getValidityPeriodType());
                    return new BizException(BizErrorCode.PRODUCT_PRICE_TYPE_NOT_FOUND.getCode(), BizErrorCode.PRODUCT_PRICE_TYPE_NOT_FOUND.getDesc());
                });

        product.setValidityPeriodStart(current);

        if (ObjectUtil.notEqual(priceDTO.getValidityPeriod(), ValidityPeriodEnum.SINGLE)) {
            product.setTotalQuantity(ObjectUtil.isNull(priceDTO.getValidityTimes()) ? -1 : priceDTO.getValidityTimes());
            switch (priceDTO.getValidityPeriod()) {
                case WEEK:
                    product.setValidityPeriodEnd(current.plusWeeks(1).withHour(23).withMinute(59).withSecond(59));
                    break;
                case MONTH:
                    product.setValidityPeriodEnd(current.plusMonths(1).withHour(23).withMinute(59).withSecond(59));
                    break;
                case YEAR:
                    product.setValidityPeriodEnd(current.plusYears(1).withHour(23).withMinute(59).withSecond(59));
                    break;
            }
        } else {
            product.setTotalQuantity(product.getCount());
        }

        BigDecimal basePrice = priceDTO.getBasePrice();
        BigDecimal count = BigDecimal.valueOf(product.getCount());

        product.setBasePrice(basePrice.toString());
        BigDecimal currentPrice = basePrice.multiply(count);
        product.setPrice(currentPrice.toString());

        return currentPrice;
    }

    private static void checkMainProduct(List<FlightProductDTO> flightProductList, FlightOrderCreateDTO flightOrderCreateDTO) {
        List<FlightProductDTO> mainProduct = flightProductList.stream().filter(product ->
                        ObjectUtil.equals(product.getProductType(), FlightProductTypeEnum.FLIGHT_SCENARIO)
                                || ObjectUtil.equals(product.getProductType(), FlightProductTypeEnum.FLIGHT_UAV))
                .collect(Collectors.toList());

        if (StrUtil.equals(flightOrderCreateDTO.getOrderType(), OrderTypeEnum.NORMAL.getCode()) && CollUtil.isEmpty(mainProduct)) {
            LogUtil.error("主产品不存在，请检查产品编码是否正确");
            throw new BizException(BizErrorCode.ORDER_NOT_EXIST_MAIN_PRODUCT.getCode(), BizErrorCode.ORDER_NOT_EXIST_MAIN_PRODUCT.getDesc());
        }

        if (StrUtil.equals(flightOrderCreateDTO.getOrderType(), OrderTypeEnum.NORMAL.getCode())) {
            flightOrderCreateDTO.setProductName(mainProduct.get(0).getProductName());
            flightOrderCreateDTO.setProductNo(mainProduct.get(0).getProductNo());
            flightOrderCreateDTO.setProductType(mainProduct.get(0).getProductType().getType());
        } else {
            flightOrderCreateDTO.setProductType(flightProductList.get(0).getProductType().getType());
        }

    }

    private static void checkProductList(List<FlightProductDTO> flightProductList, List<String> productNo, FlightOrderCreateDTO flightOrderCreateDTO) {
        if (flightProductList.size() != productNo.size()) {

            List<String> productNoList = flightProductList.stream().map(FlightProductDTO::getProductNo).collect(Collectors.toList());

            List<String> missingProductNo = productNo.stream()
                    .filter(pn -> !productNoList.contains(pn))
                    .collect(Collectors.toList());
            LogUtil.error("以下 产品编码:{} 在 flightProductList 中不存在:", missingProductNo);
            throw new BizException(BizErrorCode.PRODUCT_SERVICE_NOT_FOUND_ERROR.getCode(), BizErrorCode.PRODUCT_SERVICE_NOT_FOUND_ERROR.getDesc());
        }
    }
}
