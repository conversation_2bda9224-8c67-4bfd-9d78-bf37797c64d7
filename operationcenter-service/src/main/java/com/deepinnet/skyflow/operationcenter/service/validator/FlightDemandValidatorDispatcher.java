package com.deepinnet.skyflow.operationcenter.service.validator;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:
 * Date: 2025/4/17
 * Author: lijunheng
 */
@Component
public class FlightDemandValidatorDispatcher {

    @Resource
    private List<FlightDemandValidator> flightDemandValidatorList;

    public void validate(FlightDemandDTO demand) {
        for (FlightDemandValidator validator : flightDemandValidatorList) {
            if (validator.supports(demand.getType())) {
                invokeValidator(validator, demand);
                return;
            }
        }
    }

    @SuppressWarnings("unchecked")
    private <T extends FlightDemandDTO> void invokeValidator(FlightDemandValidator validator, FlightDemandDTO demand) {
        validator.validate(demand);
    }
}
