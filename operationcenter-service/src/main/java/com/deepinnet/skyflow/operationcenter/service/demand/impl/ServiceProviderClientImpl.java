package com.deepinnet.skyflow.operationcenter.service.demand.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.deepinnet.infra.api.dto.SupplierContactInfoDTO;
import com.deepinnet.infra.api.dto.UserDetailDTO;
import com.deepinnet.infra.api.dto.UserQueryDTO;
import com.deepinnet.infra.api.enums.UserTypeEnum;
import com.deepinnet.localdata.integration.FlightDemandClient;
import com.deepinnet.localdata.integration.model.outsidebean.*;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandSceneEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightProductTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.ProductServiceTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.client.FlightUserClient;
import com.deepinnet.skyflow.operationcenter.service.demand.ServiceProviderClient;
import com.deepinnet.skyflow.operationcenter.service.demand.impl.matching.NestProviderMatchingRule;
import com.deepinnet.skyflow.operationcenter.service.demand.impl.matching.OneNetUnifiedFlyNestProviderMatchingRule;
import com.deepinnet.skyflow.operationcenter.service.demand.impl.matching.OneNetUnifiedFlyPilotProviderMatchingRule;
import com.deepinnet.skyflow.operationcenter.service.demand.impl.matching.PilotProviderMatchingRule;
import com.deepinnet.skyflow.operationcenter.service.order.FlightOrderHelper;
import com.deepinnet.skyflow.operationcenter.service.product.FlightProductService;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderProductUsageVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import com.deepinnet.skyflow.operationcenter.service.infra.util.WktUtil;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 服务提供商客户端实现类
 *
 * <AUTHOR>
 */
@Service
public class ServiceProviderClientImpl implements ServiceProviderClient {

    private static final Logger logger = LoggerFactory.getLogger(ServiceProviderClientImpl.class);

    @Resource
    private PilotProviderMatchingRule pilotProviderMatchingRule;

    @Resource
    private NestProviderMatchingRule nestProviderMatchingRule;

    @Resource
    private OneNetUnifiedFlyPilotProviderMatchingRule oneNetUnifiedFlyPilotProviderMatchingRule;

    @Resource
    private OneNetUnifiedFlyNestProviderMatchingRule oneNetUnifiedFlyNestProviderMatchingRule;

    @Resource
    private FlightOrderHelper orderHelper;

    @Resource
    private FlightProductService productService;

    @Resource
    private FlightDemandClient yfFlightDemandClient;

    @Resource
    private FlightUserClient userClient;

    @Override
    public String matchServiceProviders(FlightDemandDTO demand) {
        logger.info("开始为需求匹配服务商: {}", demand.getDemandNo());
        if (demand.getScene() == FlightDemandSceneEnum.ONE_NET_UNIFIED_FLY) {
            return oneNetUnifiedFlyMatchServiceProviders(demand);
        } else {
            return normalMatchServiceProviders(demand);
        }
    }

    private String normalMatchServiceProviders(FlightDemandDTO demand) {
        //根据订单下的产品类型进行判断
        FlightOrderVO flightOrder = orderHelper.getFlightOrder(demand);
        List<FlightOrderProductUsageVO> productUsageList = flightOrder.getProductUsageList();
        //查询订单中主产品的具体无人机型号
        String mainProductNo = productUsageList.stream()
                .filter(p -> Objects.equals(p.getProductType(), flightOrder.getMainProductType()))
                .map(FlightOrderProductUsageVO::getProductNo)
                .findFirst().get();
        List<String> incrementServiceProductNoList = productUsageList.stream()
                .filter(p -> Objects.equals(p.getProductType(), FlightProductTypeEnum.FLIGHT_SERVICE.getType()))
                .map(FlightOrderProductUsageVO::getProductNo)
                .collect(Collectors.toList());
        List<String> productNoList = new ArrayList<>();
        productNoList.addAll(incrementServiceProductNoList);
        productNoList.add(mainProductNo);

        List<FlightProductDTO> flightProductDTOList = productService.listByProductNoList(productNoList);
        FlightProductDTO mainFlightProduct = flightProductDTOList.stream()
                .filter(p -> Objects.equals(p.getProductNo(), mainProductNo))
                .findFirst().get();
        //机型列表
        List<String> flightUavBmList = new ArrayList<>();
        if (mainFlightProduct.getProductType() == FlightProductTypeEnum.FLIGHT_UAV) {
            flightUavBmList.add(mainFlightProduct.getFlightUavBm().getFlightUavBmNo());
        } else if (mainFlightProduct.getProductType() == FlightProductTypeEnum.FLIGHT_SCENARIO) {
            flightUavBmList.addAll(Arrays.asList(mainFlightProduct.getFlightUavBmList()));
        }
        if (CollectionUtil.isEmpty(flightUavBmList)) {
            return null;
        }

        //判断增值服务列表中是否存在飞手服务
        Boolean hasFlyerService = hasFlyerService(demand, productUsageList);

        //查询增值服务中是否勾选了飞手服务
        if (hasFlyerService) {
            return pilotProviderMatchingRule.matchProvider(demand, flightUavBmList);
        } else {
            //如果是机巢服务并且找不到对应的服务商，需要再查找飞手服务的服务商
            String matchedProviderNo = nestProviderMatchingRule.matchProvider(demand, flightUavBmList);
            if (matchedProviderNo == null) {
                return pilotProviderMatchingRule.matchProvider(demand, flightUavBmList);
            }
            return matchedProviderNo;
        }
    }

    private String oneNetUnifiedFlyMatchServiceProviders(FlightDemandDTO demand) {
        //根据订单下的产品类型进行判断
        FlightOrderVO flightOrder = orderHelper.getFlightOrder(demand);

        //判断增值服务列表中是否存在飞手服务
        Boolean hasFlyerService = hasFlyerService(demand, flightOrder.getProductUsageList());
        //查询增值服务中是否勾选了飞手服务
        if (hasFlyerService) {
            return oneNetUnifiedFlyPilotProviderMatchingRule.matchProvider(demand);
        } else {
            //如果是机巢服务并且找不到对应的服务商，需要再查找飞手服务的服务商
            String matchedProviderNo = oneNetUnifiedFlyNestProviderMatchingRule.matchProvider(demand);
            if (matchedProviderNo == null) {
                return oneNetUnifiedFlyPilotProviderMatchingRule.matchProvider(demand);
            }
            return matchedProviderNo;
        }
    }

    private Boolean hasFlyerService(FlightDemandDTO demand, List<FlightOrderProductUsageVO> productUsageList) {
        //判断增值服务列表中是否存在飞手服务
        List<String> incrementServiceList = demand.getIncrementService();
        Boolean hasFlyerService;
        if (CollectionUtil.isEmpty(incrementServiceList)) {
            hasFlyerService = Boolean.FALSE;
        } else {
            hasFlyerService = incrementServiceList.stream()
                    .anyMatch(incrNo -> productUsageList.stream()
                            .anyMatch(product -> Objects.equals(product.getProductNo(), incrNo) && Objects.equals(product.getFlightProduct().getProductServiceType(), ProductServiceTypeEnum.PILOT_SERVICE)));
        }
        return hasFlyerService;
    }

    @Override
    public String syncDemandToProvider(FlightDemandDTO demand) {
        logger.info("同步需求 {} 到服务商: {}", demand.getDemandNo(), demand.getServiceProviderNo());
        FlightDemandCreateRequest flightDemandCreateRequest = convertToYfDTO(demand);
        FlightDemandCreateResponse flightDemand = yfFlightDemandClient.createFlightDemand(flightDemandCreateRequest);
        return flightDemand.getCode();
    }

    private FlightDemandCreateRequest convertToYfDTO(FlightDemandDTO demand) {
        FlightDemandCreateRequest request = new FlightDemandCreateRequest();

        // 设置基本属性
        request.setCode(demand.getDemandNo());
        request.setName(demand.getName());
        request.setTaskFeature(demand.getType().getType());

        //设置需求服务商和发布方
        setUserDetail(demand, request);

        //设置省市区编码
        setAreaCode(demand, request);

        //设置需求文件
        setFileList(demand, request);

        // 根据需求类型处理详情信息
        setAreaCoordinate(demand, request);

        setStartEndTime(demand, request);

        return request;
    }

    private void setAreaCode(FlightDemandDTO demand, FlightDemandCreateRequest request) {
        String inspectionAreaCode = demand.getInspectionAreaCode();
        if (StrUtil.isNotBlank(inspectionAreaCode)) {
            String[] codeSplit = inspectionAreaCode.split("/");
            request.setProvinceCode(codeSplit[0]);
            request.setCityCode(codeSplit[1]);
            request.setCountryCode(codeSplit[2]);
        }
    }

    private void setAreaCoordinate(FlightDemandDTO demand, FlightDemandCreateRequest request) {
        // 设置区域坐标，将WKT格式转换为lng,lat|lng,lat格式
        request.setArea(convertWktToCoordinateString(demand.getAreaCoordinate()));
        request.setCenterPoint(demand.getCenterPointLongitude() + "," + demand.getCenterPointLatitude());
    }

    private void setStartEndTime(FlightDemandDTO demand, FlightDemandCreateRequest request) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        switch (demand.getType()) {
            case ROUTINE_INSPECTION:
                RoutineInspectionFlightDemandDTO routineInspection = demand.getRoutineInspectionDetail();
                if (routineInspection != null) {
                    // 设置巡检相关信息
                    if (routineInspection.getDemandStartTime() != null) {
                        request.setStartTime(routineInspection.getDemandStartTime().format(formatter));
                    }
                    if (routineInspection.getDemandEndTime() != null) {
                        request.setEndTime(routineInspection.getDemandEndTime().format(formatter));
                    }
                }
                break;
            case EMERGENCY_RESPONSE:
                EmergencyResponseFlightDemandDTO emergency = demand.getEmergencyResponseDetail();
                if (emergency != null) {
                    // 设置应急相关信息
                    if (emergency.getTakeOffTime() != null) {
                        request.setStartTime(emergency.getTakeOffTime().format(formatter));
                        request.setEndTime("9999-12-31");
                    }
                }
                break;
            default:
                break;
        }
    }

    private void setUserDetail(FlightDemandDTO demand, FlightDemandCreateRequest request) {
        UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setUserNos(List.of(demand.getServiceProviderNo(), demand.getPublisherNo()));
        List<UserDetailDTO> userDetailList = userClient.getUserDetailList(userQueryDTO);
        Map<String, UserDetailDTO> userDetailDTOMap = userDetailList.stream().collect(Collectors.toMap(UserDetailDTO::getUserType, Function.identity()));
        request.setCustomerDetail(convertCustomerDetailDTO(userDetailDTOMap.get(UserTypeEnum.CUSTOMER.getCode())));
        request.setSupplierDetail(convertSupplierDetailDTO(userDetailDTOMap.get(UserTypeEnum.SUPPLIER.getCode())));
    }

    private SupplierDetailDTO convertSupplierDetailDTO(UserDetailDTO userDetailDTO) {
        SupplierDetailDTO supplierDetailDTO = new SupplierDetailDTO();
        com.deepinnet.infra.api.dto.SupplierDetailDTO readyDTO = userDetailDTO.getSupplierDetailDTO();
        supplierDetailDTO.setUserNo(readyDTO.getUserNo());
        supplierDetailDTO.setCompanyName(readyDTO.getCompanyName());
        supplierDetailDTO.setPhone(readyDTO.getPhone());
        supplierDetailDTO.setAddress(readyDTO.getAddress());
        supplierDetailDTO.setRegionCode(readyDTO.getRegionCode());
        supplierDetailDTO.setRegionName(readyDTO.getRegionName());
        List<SupplierContactInfoDTO> contactInfoList = readyDTO.getContactInfoList();
        if (CollectionUtil.isNotEmpty(contactInfoList)) {
            List<ContactInfoDTO> list = contactInfoList.stream().map(contactInfo -> {
                ContactInfoDTO contactInfoDTO = new ContactInfoDTO();
                contactInfoDTO.setName(contactInfo.getName());
                contactInfoDTO.setPhone(contactInfo.getPhone());
                contactInfoDTO.setPosition(contactInfo.getPosition());
                return contactInfoDTO;
            }).collect(Collectors.toList());
            supplierDetailDTO.setContactInfoList(list);
        } else {
            //添加自己
            ContactInfoDTO contactInfoDTO = new ContactInfoDTO();
            contactInfoDTO.setName(userDetailDTO.getUserName());
            contactInfoDTO.setPhone(userDetailDTO.getPhone());
            supplierDetailDTO.setContactInfoList(ListUtil.toList(contactInfoDTO));
        }
        return supplierDetailDTO;
    }

    private CustomerDetailDTO convertCustomerDetailDTO(UserDetailDTO userDetailDTO) {
        CustomerDetailDTO customerDetailDTO = new CustomerDetailDTO();
        com.deepinnet.infra.api.dto.CustomerDetailDTO readyDTO = userDetailDTO.getCustomerDetailDTO();
        customerDetailDTO.setUserNo(readyDTO.getUserNo());
        customerDetailDTO.setOrganizationName(readyDTO.getOrganizationName());
        customerDetailDTO.setPhone(readyDTO.getPhone());
        customerDetailDTO.setAddress(readyDTO.getAddress());
        customerDetailDTO.setRegionCode(readyDTO.getRegionCode());
        customerDetailDTO.setRegionName(readyDTO.getRegionName());
        List<com.deepinnet.infra.api.dto.ContactInfoDTO> contactInfoList = readyDTO.getContactInfoDTOs();
        if (CollectionUtil.isNotEmpty(contactInfoList)) {
            List<ContactInfoDTO> list = contactInfoList.stream().map(contactInfo -> {
                ContactInfoDTO contactInfoDTO = new ContactInfoDTO();
                contactInfoDTO.setName(contactInfo.getName());
                contactInfoDTO.setPhone(contactInfo.getPhone());
                contactInfoDTO.setPosition(contactInfo.getPosition());
                return contactInfoDTO;
            }).collect(Collectors.toList());
            customerDetailDTO.setContactInfoDTOs(list);
        } else {
            ContactInfoDTO contactInfoDTO = new ContactInfoDTO();
            contactInfoDTO.setName(userDetailDTO.getUserName());
            contactInfoDTO.setPhone(userDetailDTO.getPhone());
            customerDetailDTO.setContactInfoDTOs(ListUtil.toList(contactInfoDTO));
        }
        return customerDetailDTO;
    }

    private void setFileList(FlightDemandDTO demand, FlightDemandCreateRequest request) {
        List<FileDTO> requestAdditionalFileList = demand.getRequestAdditionalFiles();
        if (CollectionUtil.isNotEmpty(requestAdditionalFileList)) {
            request.setFileList(requestAdditionalFileList.stream().map(file -> {
                FilesByDemandVO fileDTO = new FilesByDemandVO();
                fileDTO.setFileName(file.getName());
                fileDTO.setFileAddr(file.getUrl());
                return fileDTO;
            }).collect(Collectors.toList()));
        }
    }

    /**
     * 将WKT格式的几何图形字符串转换为经纬度坐标点列表格式
     * 例如：POLYGON((lng1 lat1, lng2 lat2, ...)) -> lng1,lat1|lng2,lat2|...
     *
     * @param wktString WKT格式的几何图形字符串
     * @return 经纬度坐标点列表格式的字符串
     */
    private String convertWktToCoordinateString(String wktString) {
        if (StrUtil.isBlank(wktString)) {
            return null;
        }

        try {
            // 使用WktUtil将WKT字符串转换为几何对象
            Geometry geometry = WktUtil.toGeometry(wktString);
            if (geometry == null) {
                return null;
            }

            // 获取几何对象的所有坐标点
            Coordinate[] coordinates = geometry.getCoordinates();
            if (coordinates == null || coordinates.length == 0) {
                return null;
            }

            // 构建坐标点列表字符串
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < coordinates.length; i++) {
                Coordinate coord = coordinates[i];
                sb.append(coord.x).append(",").append(coord.y).append("|");
            }
            sb.deleteCharAt(sb.length() - 1);
            return sb.toString();
        } catch (Exception e) {
            // 如果转换失败，返回原始字符串或null
            return null;
        }
    }
} 