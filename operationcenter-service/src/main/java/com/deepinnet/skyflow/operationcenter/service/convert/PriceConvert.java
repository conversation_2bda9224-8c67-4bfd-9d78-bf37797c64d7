package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.PriceDO;
import com.deepinnet.skyflow.operationcenter.dto.PriceDTO;
import com.deepinnet.skyflow.operationcenter.enums.ValidityPeriodEnum;
import com.deepinnet.skyflow.operationcenter.vo.PriceVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 价格转换接口
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface PriceConvert {

    PriceConvert INSTANCE = Mappers.getMapper(PriceConvert.class);

    /**
     * DO 转 DTO
     *
     * @param priceDO DO 对象
     * @return DTO 对象
     */
    @Mapping(source = "validityPeriod", target = "validityPeriod", qualifiedByName = "stringToValidityPeriodEnum")
    PriceDTO convert(PriceDO priceDO);

    /**
     * DTO 转 DO
     *
     * @param priceDTO DTO 对象
     * @return DO 对象
     */
    @Mapping(target = "gmtModified", ignore = true)
    @Mapping(source = "validityPeriod", target = "validityPeriod", qualifiedByName = "validityPeriodEnumToString")
    PriceDO convertToDO(PriceDTO priceDTO);

    /**
     * DTO List 转 DO List
     *
     * @param priceDTOList DTO 列表
     * @return DO 列表
     */
    List<PriceDO> convertToDOList(List<PriceDTO> priceDTOList);

    /**
     * DO List 转 DTO List
     *
     * @param priceDOList DO 列表
     * @return DTO 列表
     */
    List<PriceDTO> convertList(List<PriceDO> priceDOList);

    /**
     * DTO 转 VO
     *
     * @param priceDTO DTO 对象
     * @return VO 对象
     */
    PriceVO convertToVO(PriceDTO priceDTO);

    /**
     * DTO列表 转 VO列表
     *
     * @param priceDTOList DTO 列表
     * @return VO 列表
     */
    List<PriceVO> convertToVOList(List<PriceDTO> priceDTOList);
    
    /**
     * 字符串转枚举
     *
     * @param type 有效期类型字符串
     * @return 有效期枚举
     */
    @Named("stringToValidityPeriodEnum")
    default ValidityPeriodEnum stringToValidityPeriodEnum(String type) {
        if (type == null) {
            return null;
        }
        for (ValidityPeriodEnum typeEnum : ValidityPeriodEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 枚举转字符串
     *
     * @param typeEnum 有效期枚举
     * @return 有效期类型字符串
     */
    @Named("validityPeriodEnumToString")
    default String validityPeriodEnumToString(ValidityPeriodEnum typeEnum) {
        return typeEnum == null ? null : typeEnum.getType();
    }
} 