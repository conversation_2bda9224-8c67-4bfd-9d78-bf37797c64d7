package com.deepinnet.skyflow.operationcenter.service.validator;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;

/**
 * Description:
 * Date: 2025/4/17
 * Author: lijunheng
 */
public interface FlightDemandValidator {

    /**
     * 判断是否支持该类
     *
     * @param typeEnum
     * @return
     */
    boolean supports(FlightDemandTypeEnum typeEnum);

    /**
     * 校验逻辑，异常时抛出 IllegalArgumentException 或自定义异常
     *
     * @param demand
     */
    void validate(FlightDemandDTO demand);
}

