package com.deepinnet.skyflow.operationcenter.service.sse;

import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import lombok.Data;

/**
 * Description:
 * Date: 2025/5/10
 * Author: lijunheng
 */
@Data
public class SseEventsInterestKey {

    /*------------这两个必选-------------------------*/
    private String id = IdGenerateUtil.getId("SSE_CLIENT");

    private String userId;

    /*------------关心任务还是关心租户，只能二选一------------------*/
    private String taskId;

    private String tenantId;

    public String buildKey() {
        return String.format("%s_%s_%s_%s", id, userId, taskId, tenantId);
    }

    /**
     *
     * @param field 需要比较的字段名称
     * @param value 原始的整个buildStr字符串
     * @param input 输入的字段值
     * @return
     */
    public static boolean match(String field, String value, String input) {
        if (value == null || input == null) return false;

        String[] parts = value.split("_", -1);
        if (parts.length != 4) return false;

        switch (field) {
            case "id":
                return input.equals(parts[0]);
            case "userId":
                return input.equals(parts[1]);
            case "taskId":
                return input.equals(parts[2]);
            case "tenantId":
                return input.equals(parts[3]);
            default:
                return false;
        }
    }

}
