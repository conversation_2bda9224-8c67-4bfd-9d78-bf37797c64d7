package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.WhiteListDO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListDTO;
import com.deepinnet.skyflow.operationcenter.vo.WhiteListVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 白名单对象转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface WhiteListConvert {

    WhiteListConvert INSTANCE = Mappers.getMapper(WhiteListConvert.class);

    /**
     * DTO转DO
     *
     * @param whiteListDTO 白名单DTO
     * @return 白名单DO
     */
    WhiteListDO convertToDO(WhiteListDTO whiteListDTO);

    /**
     * DO转DTO
     *
     * @param whiteListDO 白名单DO
     * @return 白名单DTO
     */
    WhiteListDTO convert(WhiteListDO whiteListDO);

    /**
     * DO列表转DTO列表
     *
     * @param whiteListDOList 白名单DO列表
     * @return 白名单DTO列表
     */
    List<WhiteListDTO> convertList(List<WhiteListDO> whiteListDOList);

    /**
     * DTO转VO
     *
     * @param whiteListDTO 白名单DTO
     * @return 白名单VO
     */
    WhiteListVO convertToVO(WhiteListDTO whiteListDTO);

    /**
     * DTO列表转VO列表
     *
     * @param whiteListDTOList 白名单DTO列表
     * @return 白名单VO列表
     */
    List<WhiteListVO> convertToVOList(List<WhiteListDTO> whiteListDTOList);
} 