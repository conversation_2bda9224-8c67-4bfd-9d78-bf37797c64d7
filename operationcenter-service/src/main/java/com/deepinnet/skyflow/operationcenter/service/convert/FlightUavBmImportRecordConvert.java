package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavBmImportRecordDO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportRecordDTO;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavBmImportRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 飞行无人机品牌型号导入记录转换接口
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface FlightUavBmImportRecordConvert {

    FlightUavBmImportRecordConvert INSTANCE = Mappers.getMapper(FlightUavBmImportRecordConvert.class);

    /**
     * DO 转 DTO
     *
     * @param importRecordDO DO 对象
     * @return DTO 对象
     */
    FlightUavBmImportRecordDTO convert(FlightUavBmImportRecordDO importRecordDO);

    /**
     * DTO 转 DO
     *
     * @param importRecordDTO DTO 对象
     * @return DO 对象
     */
    @Mapping(target = "gmtModified", ignore = true)
    FlightUavBmImportRecordDO convertToDO(FlightUavBmImportRecordDTO importRecordDTO);

    /**
     * DO List 转 DTO List
     *
     * @param importRecordDOList DO 列表
     * @return DTO 列表
     */
    List<FlightUavBmImportRecordDTO> convertList(List<FlightUavBmImportRecordDO> importRecordDOList);

    /**
     * DTO 转 VO
     *
     * @param importRecordDTO DTO 对象
     * @return VO 对象
     */
    FlightUavBmImportRecordVO convertToVO(FlightUavBmImportRecordDTO importRecordDTO);

    /**
     * DTO列表 转 VO列表
     *
     * @param importRecordDTOList DTO 列表
     * @return VO 列表
     */
    List<FlightUavBmImportRecordVO> convertToVOList(List<FlightUavBmImportRecordDTO> importRecordDTOList);
} 