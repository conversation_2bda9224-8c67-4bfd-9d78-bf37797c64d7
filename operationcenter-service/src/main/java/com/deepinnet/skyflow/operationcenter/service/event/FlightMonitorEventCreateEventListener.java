package com.deepinnet.skyflow.operationcenter.service.event;

import com.deepinnet.digitaltwin.common.util.JsonConvertUtil;
import com.deepinnet.skyflow.operationcenter.dto.FileInfoDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightEventStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.helper.FlightPlanReportService;
import com.deepinnet.skyflow.operationcenter.service.sse.FlightEventSseEmitterManager;
import com.deepinnet.skyflow.operationcenter.service.sse.SseEventsInterestKey;
import com.deepinnet.spatiotemporalplatform.client.skyflow.FlightClient;
import com.deepinnet.spatiotemporalplatform.dto.FlightReportDTO;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description:
 * Date: 2025/4/17
 * Author: lijunheng
 */
@Component
public class FlightMonitorEventCreateEventListener implements ApplicationListener<FlightMonitorEventCreateEvent> {

    @Resource
    private FlightEventSseEmitterManager eventSseEmitterManager;

    @Resource
    private FlightPlanReportService flightPlanReportService;

    @Resource
    private FlightClient flightClient;

    @Override
    public void onApplicationEvent(FlightMonitorEventCreateEvent event) {
        FlightEventsDTO flightEventsDTO = event.getFlightEventsDTO();
        FlightEventStatusEnum status = flightEventsDTO.getStatus();
        //sse通知前端事件变化
        eventSseEmitterManager.broadcastData(sseKey -> SseEventsInterestKey.match("taskId", sseKey, flightEventsDTO.getFlightTaskCode()), flightEventsDTO);
        eventSseEmitterManager.broadcastData(sseKey -> SseEventsInterestKey.match("tenantId", sseKey, flightEventsDTO.getTenantId()), flightEventsDTO);

        //判断状态如果是已取消、已失败、已完成的话，通知前端sse事件结束了
        if (status == FlightEventStatusEnum.CANCEL || status == FlightEventStatusEnum.FAIL || status == FlightEventStatusEnum.DONE) {
            eventSseEmitterManager.broadcastComplete(sseKey -> SseEventsInterestKey.match("taskId", sseKey, flightEventsDTO.getFlightTaskCode()));
            //生成飞行报告
            FileInfoDTO fileInfoDTO = flightPlanReportService.generateFlightPlanReport(flightEventsDTO.getFlightTaskCode());
            // 更新飞行计划表
            FlightReportDTO flightReportDTO = new FlightReportDTO();
            flightReportDTO.setPlanId(flightEventsDTO.getFlightTaskCode());
            flightReportDTO.setFlightReport(JsonConvertUtil.toJsonStr(fileInfoDTO));
            flightClient.updateFlightReport(flightReportDTO);
        }
    }
}
