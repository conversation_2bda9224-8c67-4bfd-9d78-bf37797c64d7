package com.deepinnet.skyflow.operationcenter.service.task;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/24
 */
public class TenantContextHolder {

    private static final ThreadLocal<String> TENANT_ID_CONTEXT = new ThreadLocal<>();

    /**
     * 设置当前线程的租户 ID
     * @param tenantId 租户标识
     */
    public static void setTenantId(String tenantId) {
        TENANT_ID_CONTEXT.set(tenantId);
    }

    /**
     * 获取当前线程的租户 ID
     * @return 租户标识，如果未设置则返回 null
     */
    public static String getTenantId() {
        return TENANT_ID_CONTEXT.get();
    }

    /**
     * 清除当前线程的租户 ID
     */
    public static void clear() {
        TENANT_ID_CONTEXT.remove();
    }

}
