
spring:
  datasource:
    hikari:
      leak-detection-threshold: 30000
    dynamic:
      primary: master
      datasource:
        master:
          url: ***************************************************************************************************************************
          username: postgres
          password: Shendu188
          driver-class-name: org.postgresql.Driver

  redis:
    host: ***************
    port: 32375
    password: Shendu188
    database: 1


algorithm:
  redis:
    stream:
      topic: topic
      group: skyFlow

stpf:
  service:
    url: http://*************:60316
    #url: http://localhost:60106

# MinIO配置
minio:
  endpoint: https://minio.deepinnet.com:9000
  accessKey: cosskJEYUP9ffuNO3ruv
  secretKey: QwKVrdyLGu89xIfRimlLnf0WDkF3JTBdoi1oaJDZ
  bucketName: deepinnet

out-api:
  yuan-fei:
    #    管控
    control:
      username: 13904014313
      password: zhjg2023
      serverUrl: https://jt-gk.kitegogo.cn
#      serverUrl: http://localhost:8080

    #    运营端
    op:
      username: 17647654305
      password: 123456aA!
      serverUrl: https://lg-yy.kitegogo.cn
#      serverUrl: http://localhost:8080

  gps:
    address: http://*************:8090
    username: 衢州交警
    password: Hgs@123456


inner-api:
  algorithm:
#    serverUrl: http://localhost:8080
    serverUrl: http://localhost:8080

mqtt:
  protocol: ws
  host: mqtest.kitegogo.cn
  port: 2416
  username: admin
  password: public

mock:
  # 鸳飞
  yf: false
  # 算法
  sf: true

management:
  server:
    port: 9000
  endpoints:
    web:
      exposure:
        include: "*"