server:
  port: 8080

spring:
  main:
    allow-circular-references: true
  application:
    name: operationcenter
  servlet:
    multipart:
      # 文件上传的最大大小
      max-file-size: 50MB
      # 请求的最大大小（包含表单数据）
      max-request-size: 250MB

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

logging:
  level:
    com.deepinnet.skyflow.operationcenter: info
    org.springframework: info


jasypt:
  encryptor:
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator

mybatis-plus:
  mapper-locations: classpath:mybatis/**/*.xml
  configuration:
    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#  sql:
#    print: true
  type-handlers-package: com.deepinnet.skyflow.operationcenter.dal.typehandler
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: isDeleted
      logic-delete-value: true
      logic-not-delete-value: false

pagehelper:
  helper-dialect: postgresql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

knife4j:
  enable: true
  openapi:
    title: 低空经济运营平台
    description: 低空经济运营平台
    email: <EMAIL>
    concat: juju
    version: v4.0
    group:
      web:
        group-name: web
        api-rule: package
        api-rule-resources:
          - com.deepinnet

feign:
  httpclient:
    enabled: true
  okhttp:
    enabled: false

salt: shendu188

sa-token:
  # token 有效期（单位：秒），默认30天，-1代表永不过期
  timeout: 17280000
  # jwt秘钥
  jwt-secret-key: shendu188


liteflow:
  #规则文件路径
  rule-source: classpath:config/flow.xml
  #-----------------以下非必须-----------------
  #liteflow是否开启，默认为true
  enable: true
  #liteflow的banner打印是否开启，默认为true
  print-banner: true
  #上下文的初始数量槽，默认值为1024，这个值不用刻意配置，这个值会自动扩容
  slot-size: 1024
  #FlowExecutor的execute2Future的线程数，默认为64
  main-executor-works: 64
  #FlowExecutor的execute2Future的自定义线程池Builder，LiteFlow提供了默认的Builder
  main-executor-class: com.yomahub.liteflow.thread.LiteFlowDefaultMainExecutorBuilder
  #自定义请求ID的生成类，LiteFlow提供了默认的生成类
  request-id-generator-class: com.yomahub.liteflow.flow.id.DefaultRequestIdGenerator
  #全局异步节点线程池大小，默认为64
  global-thread-pool-size: 64
  #全局异步节点线程池队列大小，默认为512
  global-thread-pool-queue-size: 512
  #全局异步节点线程池自定义Builder，LiteFlow提供了默认的线程池Builder
  global-thread-pool-executor-class: com.yomahub.liteflow.thread.LiteFlowDefaultGlobalExecutorBuilder
  #异步线程最长的等待时间(只用于when)，默认值为15000
  when-max-wait-time: 15000
  #异步线程最长的等待时间(只用于when)，默认值为MILLISECONDS，毫秒
  when-max-wait-time-unit: MILLISECONDS
  #每个WHEN是否用单独的线程池
  when-thread-pool-isolate: false
  #设置解析模式，一共有三种模式，PARSE_ALL_ON_START | PARSE_ALL_ON_FIRST_EXEC | PARSE_ONE_ON_FIRST_EXEC
  parse-mode: PARSE_ALL_ON_START
  #全局重试次数，默认为0
  retry-count: 0
  #是否支持不同类型的加载方式混用，默认为false
  support-multiple-type: false
  #全局默认节点执行器
  node-executor-class: com.yomahub.liteflow.flow.executor.DefaultNodeExecutor
  #是否打印执行中过程中的日志，默认为true
  print-execution-log: true
  #是否开启本地文件监听，默认为false
  enable-monitor-file: false
  #是否开启快速解析模式，默认为false
  fast-load: false
  #是否开启Node节点实例ID持久化，默认为false
  enable-node-instance-id: false
  #简易监控配置选项
  monitor:
    #监控是否开启，默认不开启
    enable-log: false
    #监控队列存储大小，默认值为200
    queue-limit: 200
    #监控一开始延迟多少执行，默认值为300000毫秒，也就是5分钟
    delay: 300000
    #监控日志打印每过多少时间执行一次，默认值为300000毫秒，也就是5分钟
    period: 300000

