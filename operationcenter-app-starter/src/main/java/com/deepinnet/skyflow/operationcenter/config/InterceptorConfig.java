package com.deepinnet.skyflow.operationcenter.config;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/12/9
 */

@Configuration
public class InterceptorConfig {

    @Bean
    public SaInterceptor saInterceptor() {
        return new SaInterceptor(handle -> {
            try {
                // 该标志直接免登处理
                String authMode = SaHolder.getRequest().getHeader("X-Auth-Mode");
                String cookie = SaHolder.getRequest().getHeader("Cookie");
                if (StringUtils.equals(authMode, "bypass") || StringUtils.contains(cookie, "bypass")) {
                    return;
                }

                StpUtil.checkLogin();
            } catch (Exception e) {
                StackTraceElement[] stackTrace = e.getStackTrace();
                if (StringUtils.equals(stackTrace[14].getClassName(), "cn.dev33.satoken.jwt.SaJwtTemplate")) {
                    LogUtil.error("saToken异常处理类，解析jwt token失败，堆栈信息：{}", e);
                    throw new BizException(BizErrorCode.ACCOUNT_LOGIN_EXPIRE.getCode(), BizErrorCode.ACCOUNT_LOGIN_EXPIRE.getDesc());
                }

                // 获取当前请求的 HttpServletRequest
                HttpServletRequest request = (HttpServletRequest) SaHolder.getRequest().getSource();
                // 获取当前访问的 URL
                String requestUrl = request.getRequestURI();

                LogUtil.error("saToken拦截器，访问的url为：{}, 异常信息：", requestUrl, e);
                throw new BizException(BizErrorCode.ACCOUNT_NOT_LOGIN.getCode(), BizErrorCode.ACCOUNT_NOT_LOGIN.getDesc());
            }
        });
    }

}
