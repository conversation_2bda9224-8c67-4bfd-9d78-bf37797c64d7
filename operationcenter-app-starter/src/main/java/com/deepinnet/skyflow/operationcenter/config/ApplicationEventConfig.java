package com.deepinnet.skyflow.operationcenter.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ApplicationEventMulticaster;
import org.springframework.context.event.SimpleApplicationEventMulticaster;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * Description:
 * Date: 2025/5/22
 * Author: lijunheng
 */
@Configuration
public class ApplicationEventConfig {

    @Bean
    public ApplicationEventMulticaster applicationEventMulticaster(@Autowired @Qualifier("applicationEventTaskExecutor") ThreadPoolExecutor applicationEventTaskExecutor) {
        SimpleApplicationEventMulticaster multicaster = new SimpleApplicationEventMulticaster();
        multicaster.setTaskExecutor(applicationEventTaskExecutor);
        return multicaster;
    }
}
