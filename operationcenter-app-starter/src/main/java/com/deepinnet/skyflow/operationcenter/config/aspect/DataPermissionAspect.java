package com.deepinnet.skyflow.operationcenter.config.aspect;

import cn.dev33.satoken.context.SaHolder;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.UserClient;
import com.deepinnet.infra.api.dto.DataAccessDTO;
import com.deepinnet.skyflow.operationcenter.service.context.DataPermission;
import com.deepinnet.skyflow.operationcenter.service.context.DataPermissionContext;
import com.deepinnet.skyflow.operationcenter.service.context.UserDataPermissionEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description: 数据权限切面
 * Date: 2025/5/21
 * Author: lijunheng
 */
@Slf4j
@Aspect
@Component
public class DataPermissionAspect {

    @Resource
    private UserClient userClient;

    @Around("@annotation(dataPermission)")
    public Object around(ProceedingJoinPoint point, DataPermission dataPermission) throws Throwable {
        if (!dataPermission.value()) {
            return point.proceed();
        }

        // 该标志直接免登处理
        String authMode = SaHolder.getRequest().getHeader("X-Auth-Mode");
        String cookie = SaHolder.getRequest().getHeader("Cookie");
        if (StringUtils.equals(authMode, "bypass") || StringUtils.contains(cookie, "bypass")) {
            return point.proceed();
        }

        // 获取用户可访问数据权限
        Result<DataAccessDTO> result = userClient.getAvailableQueryData();
        if (!result.isSuccess()) {
            log.error("用户权限验证失败，{}", result.getErrorDesc());
            throw new RuntimeException("获取用户数据权限失败: " + result.getErrorDesc());
        }
        
        try {
            // 设置数据权限上下文
            UserDataPermissionEntity userDataPermissionEntity = new UserDataPermissionEntity();
            userDataPermissionEntity.setDataAccess(result.getData());
            userDataPermissionEntity.setEnableDataPermission(true);
            DataPermissionContext.set(userDataPermissionEntity);
            
            // 执行原方法
            return point.proceed();
        } finally {
            // 清除上下文
            DataPermissionContext.clear();
        }
    }
} 