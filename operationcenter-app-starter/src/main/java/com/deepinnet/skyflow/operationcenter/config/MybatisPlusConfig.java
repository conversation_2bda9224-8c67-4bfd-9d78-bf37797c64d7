package com.deepinnet.skyflow.operationcenter.config;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.*;
import com.deepinnet.skyflow.operationcenter.dal.typehandler.*;
import org.springframework.context.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * MyBatis-Plus 配置类
 *
 * <AUTHOR>
 */
@Configuration
public class MybatisPlusConfig {

    @Resource
    private DeepinnetTenantLineHandler tenantLineHandler;

    @Resource
    private UserVisibleDataPermissionHandler userVisibleDataPermissionHandler;

    /**
     * 不需要数据权限的表
     */
    private static final List<String> IGNORE_DATA_PERMISSION_TABLE_NAMES = ListUtil.toList(
            "flight_demand_prop"
    );

    /**
     * 注册自定义类型处理器
     */
    @Bean
    public StringArrayTypeHandler stringArrayTypeHandler() {
        return new StringArrayTypeHandler();
    }
    
    /**
     * 注册PostgreSQL text[]类型处理器
     */
    @Bean
    public PostgresTextArrayTypeHandler postgresTextArrayTypeHandler() {
        return new PostgresTextArrayTypeHandler();
    }
    
    /**
     * 注册飞行无人机飞行方式枚举类型处理器
     */
    @Bean
    public FlightUavFlyTypeEnumTypeHandler flightUavFlyTypeEnumTypeHandler() {
        return new FlightUavFlyTypeEnumTypeHandler();
    }
    
    /**
     * 注册PostgreSQL Point类型处理器
     */
    @Bean
    public PGPointTypeHandler pgPointTypeHandler() {
        return new PGPointTypeHandler();
    }
    
    /**
     * 配置分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 添加分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        // 注册租户行拦截器
        interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(tenantLineHandler));

        userVisibleDataPermissionHandler.setIgnoreDataPermissionTableNames(IGNORE_DATA_PERMISSION_TABLE_NAMES);
        interceptor.addInnerInterceptor(new DataPermissionInterceptor(userVisibleDataPermissionHandler));
        return interceptor;
    }

} 