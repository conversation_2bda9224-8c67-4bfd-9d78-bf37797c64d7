package com.deepinnet.skyflow.operationcenter.config;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.util.ClassUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.service.context.TenantContext;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.tenant.TenantIdUtil;
import net.sf.jsqlparser.expression.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/4/21 10:09
 * @Description
 */
@Component
public class DeepinnetTenantLineHandler implements TenantLineHandler, InitializingBean {

    List<String> needTenantIdTableNames = new ArrayList<>();

    @Override
    public void afterPropertiesSet() {
        Set<String> tableNames = getTableNames("com.deepinnet.skyflow.operationcenter.dal.dataobject");
        tableNames.remove("dictionary");
        tableNames.remove("dictionary_item");
        needTenantIdTableNames.addAll(tableNames);
    }

    @Override
    public Expression getTenantId() {
        // 如果开启了禁用租户id则直接返回null
        if (TenantContext.isTenantLineDisabled()) {
            return null;
        }

        String tenantId = TenantIdUtil.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            LogUtil.warn("租户id不存在，需要传递租户id");
            return null;
//            throw new BizException(BizErrorCode.TENANT_ID_NOT_FOUND.getCode(), BizErrorCode.TENANT_ID_NOT_FOUND.getDesc());
        }

        return new StringValue(tenantId);
    }

    @Override
    public String getTenantIdColumn() {
        return "tenant_id";
    }

    @Override
    public boolean ignoreTable(String tableName) {
        // 如果开启了禁用租户id则直接放行，不拼接租户id，适用场景：定时任务
        if (TenantContext.isTenantLineDisabled()) {
            return true;
        }

        if (StringUtils.isBlank(TenantIdUtil.getTenantId())) {
            LogUtil.error("租户id不存在，需要传递租户id");
            throw new BizException(BizErrorCode.TENANT_ID_NOT_FOUND.getCode(), BizErrorCode.TENANT_ID_NOT_FOUND.getDesc());
        }

        return !needTenantIdTableNames.contains(tableName);
    }

    public static Set<String> getTableNames(String basePackage) {
        // 扫描包下所有类（非抽象）
        Set<Class<?>> classSet = ClassUtil.scanPackage(basePackage);

        // 过滤出有 @TableName 注解的类，提取表名
        return classSet.stream()
                .filter(clazz -> AnnotationUtil.hasAnnotation(clazz, TableName.class))
                .map(clazz -> AnnotationUtil.getAnnotationValue(clazz, TableName.class, "value"))
                .map(String::valueOf)
                .collect(Collectors.toSet());
    }
}
