package com.deepinnet.skyflow.operationcenter.config;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.skyflow.operationcenter.dal.config.DataPermissionColumn;
import com.deepinnet.skyflow.operationcenter.service.context.DataPermissionContext;
import com.deepinnet.skyflow.operationcenter.service.context.UserDataPermissionEntity;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.schema.Column;
import org.springframework.stereotype.Component;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2025/5/21
 * Author: lijunheng
 */
@Slf4j
@Component
public class UserVisibleDataPermissionHandler implements DataPermissionHandler {

    private List<String> ignoreDataPermissionTableNames;

    public void setIgnoreDataPermissionTableNames(List<String> ignoreDataPermissionTableNames) {
        this.ignoreDataPermissionTableNames = ignoreDataPermissionTableNames;
    }

    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        UserDataPermissionEntity userDataPermissionEntity = DataPermissionContext.get();
        //如果是不需要登录的接口、未开启权限过滤或者超级管理员，那就不需要加用户权限过滤了
        if (userDataPermissionEntity == null
                || userDataPermissionEntity.getEnableDataPermission() == Boolean.FALSE
                || userDataPermissionEntity.getDataAccess().getSupportQueryUserNos().contains(DataScopeConstants.SUPER_ADMIN)
        ) {
            return where;
        }

        // 获取实体类的 class 名称
        String className = mappedStatementId.substring(0, mappedStatementId.lastIndexOf('.'));
        Class<?> clazz;
        try {
            clazz = Class.forName(className);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Class not found: " + className);
        }

        // 获取泛型类型（BaseMapper<Entity>）
        for (Type type : clazz.getGenericInterfaces()) {
            if (type instanceof ParameterizedType) {
                ParameterizedType pt = (ParameterizedType) type;
                Type[] args = pt.getActualTypeArguments();
                if (args.length > 0 && args[0] instanceof Class<?>) {
                    Class<?> entityClass = (Class<?>) args[0];

                    //判断是否忽略权限
                    if (CollectionUtil.isNotEmpty(ignoreDataPermissionTableNames)) {
                        TableName tableNameAnno = entityClass.getAnnotation(TableName.class);
                        if (tableNameAnno != null && ignoreDataPermissionTableNames.contains(tableNameAnno.value())) {
                            return where;
                        }
                    }

                    String userIdColumn = null;
                    try {
                        userIdColumn = getUserIdColumn(entityClass);
                    } catch (NoSuchFieldException e) {
                        //找不到创建人字段就直接放过吧，避免改造太多接口，需要权限的地方再加入吧
                        return where;
                    }

                    // 获取当前用户权限用户 ID 列表（你需要根据实际项目获取）
                    List<String> allowedUserIds = userDataPermissionEntity.getDataAccess().getSupportQueryUserNos();
                    if (CollectionUtil.isEmpty(allowedUserIds)) {
                        // 没有权限用户，不做限制（或者你想返回空数据，可以加 1=0）
                        return where;
                    }

                    // 构建 IN 表达式：userIdColumn IN (1, 2, 3)
                    InExpression inExpression = new InExpression(
                            new Column(userIdColumn),
                            new ExpressionList(allowedUserIds.stream()
                                    .map(StringValue::new)
                                    .collect(Collectors.toList()))
                    );

                    // 和原始 where 进行合并
                    return (where != null)
                            ? new AndExpression(where, inExpression)
                            : inExpression;
                }
            }
        }
        return where;
    }

    private String getUserIdColumn(Class<?> entityClass) throws NoSuchFieldException {
        DataPermissionColumn annotation = entityClass.getAnnotation(DataPermissionColumn.class);
        String userIdColumn;
        if (annotation != null) {
            userIdColumn = annotation.value();
        } else {
            try {
                entityClass.getDeclaredField("creator");
            } catch (NoSuchFieldException e) {
                log.warn("{} 请指定数据创建人字段，可以使用@DataPermission注解或者默认creator字段", entityClass.getName());
                throw e;
            }
            userIdColumn = "creator";
        }
        return userIdColumn;
    }
}
