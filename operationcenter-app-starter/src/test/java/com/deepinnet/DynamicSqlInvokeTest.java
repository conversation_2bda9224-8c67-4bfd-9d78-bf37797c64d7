package com.deepinnet;

import com.deepinnet.skyflow.operationcenter.OperationCenterApplication;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.WhiteListDO;
import com.deepinnet.skyflow.operationcenter.service.demand.impl.matching.DynamicSqlEntity;
import com.deepinnet.skyflow.operationcenter.service.demand.impl.matching.DynamicSqlInvokeHelper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Description: 动态SQL调用和供应商匹配测试
 * Date: 2025/4/22
 * Author: lijunheng
 */
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OperationCenterApplication.class)
public class DynamicSqlInvokeTest {

    @Resource
    private DynamicSqlInvokeHelper dynamicSqlInvokeHelper;

    private DynamicSqlEntity dynamicSqlEntity;

    @Before
    public void setUp() {
        // 初始化动态SQL实体
        dynamicSqlEntity = new DynamicSqlEntity();
    }

    @Test
    public void testSimpleQuery() {
        dynamicSqlEntity.setSelect("select *");
        dynamicSqlEntity.setFrom("from white_list");
        dynamicSqlEntity.getWhereList().add("customer_user_no = :customer_user_no");
        dynamicSqlEntity.addParameter("customer_user_no", "enim irure laborum");
        
        Map result = dynamicSqlInvokeHelper.queryForObject(dynamicSqlEntity, Map.class);
        Assert.assertNotNull("查询结果不应为空", result);
    }

    @Test
    public void testMultipleConditionsQuery() {
        dynamicSqlEntity.setSelect("select id, customer_user_no");
        dynamicSqlEntity.setFrom("from white_list");
        dynamicSqlEntity.getWhereList().add("customer_user_name in (:list)");
        dynamicSqlEntity.getWhereList().add("is_deleted = :delete");
        dynamicSqlEntity.getWhereList().add("tenant_id = :tenantId and tenant_id = :tenantId");
        ArrayList<Object> objects = new ArrayList<>();
        dynamicSqlEntity.addParameter("list", objects);
        dynamicSqlEntity.addParameter("delete", false);
        dynamicSqlEntity.addParameter("tenantId", "20");
        objects.add("钱磊");
        objects.add("贺静");
        List<WhiteListDO> results = dynamicSqlInvokeHelper.queryForList(dynamicSqlEntity, WhiteListDO.class);
        Assert.assertNotNull("查询结果列表不应为空", results);
    }

    @Test
    public void testQueryWithGroupBy() {
        dynamicSqlEntity.setSelect("select supplier_user_no, count(*) as count");
        dynamicSqlEntity.setFrom("from white_list");
        dynamicSqlEntity.setGroupBy("group by supplier_user_no");
        
        List<Map> results = dynamicSqlInvokeHelper.queryForList(dynamicSqlEntity, Map.class);
        Assert.assertNotNull("分组查询结果不应为空", results);
    }
}
