package com.deepinnet;

import com.deepinnet.skyflow.operationcenter.OperationCenterApplication;
import com.deepinnet.skyflow.operationcenter.dto.FileInfoDTO;
import com.deepinnet.skyflow.operationcenter.service.RedisTaskStateManager;
import com.deepinnet.skyflow.operationcenter.service.helper.FlightPlanReportService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Description: 动态SQL调用和供应商匹配测试
 * Date: 2025/4/22
 * Author: lijunheng
 */
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OperationCenterApplication.class)
public class FlightPlanServiceTest {

    @Resource
    private FlightPlanReportService reportService;

    @Test
    public void testGenerate() {
        FileInfoDTO fileInfoDTO = reportService.generateFlightPlanReport("飞行计划id");
        System.out.println(fileInfoDTO);
    }
}
