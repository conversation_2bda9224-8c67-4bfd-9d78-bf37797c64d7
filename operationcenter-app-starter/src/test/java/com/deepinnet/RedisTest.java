package com.deepinnet;

import com.deepinnet.skyflow.operationcenter.OperationCenterApplication;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.WhiteListDO;
import com.deepinnet.skyflow.operationcenter.service.RedisTaskStateManager;
import com.deepinnet.skyflow.operationcenter.service.demand.impl.matching.DynamicSqlEntity;
import com.deepinnet.skyflow.operationcenter.service.demand.impl.matching.DynamicSqlInvokeHelper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: 动态SQL调用和供应商匹配测试
 * Date: 2025/4/22
 * Author: lijunheng
 */
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OperationCenterApplication.class)
public class RedisTest {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedisTaskStateManager taskStateManager;

    @Test
    public void testSendMsg() {
        Map<String, String> message = new HashMap<>();
        message.put("username", "username2");
        message.put("action", "action");

        RecordId mystream = redisTemplate.opsForStream().add("topic", message);
        System.out.println(mystream);
    }

    @Test
    public void testRedisTaskStateManager() {
        String taskId = "taskId";
        boolean tryStart = taskStateManager.tryStart(taskId, 10);
        Assert.assertTrue(tryStart);

        boolean tryEnd = taskStateManager.tryEnd(taskId, 10);
        Assert.assertTrue(tryEnd);

        boolean tryEnd2 = taskStateManager.tryEnd(taskId, 10);
        Assert.assertTrue(tryEnd2);
    }

}
