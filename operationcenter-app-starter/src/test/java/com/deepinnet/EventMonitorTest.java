package com.deepinnet;

import com.deepinnet.digitaltwin.common.util.JsonConvertUtil;
import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsStatDTO;
import com.deepinnet.skyflow.operationcenter.OperationCenterApplication;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsStatQueryDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightEventStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightEventTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.algorithm.EventMonitorService;
import com.google.common.collect.ImmutableMap;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.MDC;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Description: 动态SQL调用和供应商匹配测试
 * Date: 2025/4/22
 * Author: lijunheng
 */
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OperationCenterApplication.class)
public class EventMonitorTest {

    @Resource
    private EventMonitorService eventMonitorService;

    @Before
    public void setup() {
        MDC.put("Tenant-Id", "1");
    }

    @Test
    public void testQueryFlightEventsStat() {
        FlightEventsStatQueryDTO queryDTO = new FlightEventsStatQueryDTO();
        queryDTO.setTenantId("1");
        queryDTO.setStartTime(LocalDateTime.now().withDayOfMonth(8));
        List<FlightEventsStatDTO> flightEventsStatDTOS = eventMonitorService.queryFlightEventsStat(queryDTO);
        System.out.println(flightEventsStatDTOS);
    }

    @Test
    public void testQueryListByFlightTaskCode() {
        List<FlightEventsDTO> flightEventsDTOS = eventMonitorService.queryListByFlightTaskCode("1");
        System.out.println(flightEventsDTOS);
    }

    @Test
    public void testSaveEvent() {
        FlightEventsDTO flightEventsDTO = new FlightEventsDTO();
        flightEventsDTO.setId("3");
        flightEventsDTO.setEventType(FlightEventTypeEnum.ILLEGAL_TURN);
        flightEventsDTO.setEventTime(LocalDateTime.now());
        flightEventsDTO.setEventName(FlightEventTypeEnum.ILLEGAL_TURN.getDesc());
        flightEventsDTO.setDescription("1");
        flightEventsDTO.setEventPoint(new PointCoordinate(122.0, 25.0));
        flightEventsDTO.setEventLocation("xxx街道xxx店门口");
        flightEventsDTO.setDuration(60);
        flightEventsDTO.setStatus(FlightEventStatusEnum.RUNNING);
        flightEventsDTO.setLicensePlate("1");
        Map<String, String> imageMap = ImmutableMap.of("url", "xxxx");
        flightEventsDTO.setEvidenceImages(JsonConvertUtil.toJsonStr(imageMap));
        Map<String, String> videoMap = ImmutableMap.of("url", "xxxx");
        flightEventsDTO.setEvidenceVideos(JsonConvertUtil.toJsonStr(videoMap));
        flightEventsDTO.setFlightTaskCode("1");
        flightEventsDTO.setTenantId("1");
        eventMonitorService.saveEvent(flightEventsDTO);
    }

    @Test
    public void testGetEventById() {
        FlightEventsDTO flightEventsDTO = eventMonitorService.getEventById("2");
        System.out.println(flightEventsDTO);
    }

    @Test
    public void testPageQuery() {
        FlightEventsQueryDTO queryDTO = new FlightEventsQueryDTO();
        queryDTO.setTenantId("1");
        queryDTO.setStartTime(LocalDateTime.now().withDayOfMonth(8));
        queryDTO.setEndTime(LocalDateTime.now());
        List<FlightEventsDTO> flightEventsDTOS = eventMonitorService.pageQuery(queryDTO).getList();
        System.out.println(flightEventsDTOS);
    }
}
