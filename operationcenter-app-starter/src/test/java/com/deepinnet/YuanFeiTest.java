package com.deepinnet;

import com.deepinnet.skyflow.operationcenter.OperationCenterApplication;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.deepinnet.skyflow.operationcenter.web.mockapp.RealTimeFlyDataMsgBody;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Description:
 * Date: 2025/5/15
 * Author: lijunheng
 */
@Slf4j
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OperationCenterApplication.class)
public class YuanFeiTest {

    @Autowired
    private MqttPahoMessageHandler mqttOutbound;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void test() throws InterruptedException {
        while (true) {
            Thread.sleep(3000l);
            // 创建飞行数据
            RealTimeFlyDataMsgBody flyData = new RealTimeFlyDataMsgBody();
            flyData.setUavId(IdGenerateUtil.getId("uavIdTest"));
            flyData.setFlightId(IdGenerateUtil.getId("flightId"));
            flyData.setPlanId(IdGenerateUtil.getId("planId"));
            flyData.setState(0);
            flyData.setCpuid(IdGenerateUtil.getId("cpuid"));

            // 根据状态设置不同的位置
            double offsetLon = (Math.random() - 0.5) * 0.01;
            double offsetLat = (Math.random() - 0.5) * 0.01;

            flyData.setLon((long) ((1 + offsetLon) * 10000000));
            flyData.setLat((long) ((1 + offsetLat) * 10000000));
            flyData.setAlt(100 + (int) (Math.random() * 20));
            flyData.setHeight(80 + (int) (Math.random() * 20));
            flyData.setRelativeHeight(80 + (int) (Math.random() * 20));
            flyData.setSpeed((int) (10 + Math.random() * 5));
            flyData.setTimestamp(System.currentTimeMillis());

            /**
             * 飞行记录同步MQTT
             */
            try {
                // 将飞行数据转换为JSON字符串
                String payload = objectMapper.writeValueAsString(flyData);

                // 构建MQTT消息
                String topic = "/jtyy/realtime/fly/uav/";
                Message<String> message = MessageBuilder
                        .withPayload(payload)
                        .setHeader(MqttHeaders.TOPIC, topic)
                        .setHeader("mqtt_receivedTopic", topic)
                        .setHeader(MqttHeaders.QOS, 1) // QoS 1 - 至少一次
                        .build();

                // 发送MQTT消息
                mqttOutbound.handleMessage(message);

                log.info("发送飞行记录: planId={}, state={}, topic={}", flyData.getPlanId(), flyData.getState(), topic);
            } catch (JsonProcessingException e) {
                log.error("飞行数据序列化失败", e);
            } catch (Exception e) {
                log.error("MQTT消息发送失败", e);
            }
        }

    }
}

