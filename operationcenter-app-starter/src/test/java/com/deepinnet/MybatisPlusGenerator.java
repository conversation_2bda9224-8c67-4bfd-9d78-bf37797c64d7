package com.deepinnet;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig.Builder;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.BeetlTemplateEngine;
import com.baomidou.mybatisplus.generator.keywords.MySqlKeyWordsHandler;
import org.junit.jupiter.api.Test;

import java.util.Collections;

/**

 * @since 2022-11-05 星期六
 * <p>
 * dao层模版生成
 * 直接运行test方法即可拥有mybatis-plus模版方法！！！
 * <p>
 * 本地修改后请勿提交!!!
 **/
@SuppressWarnings("all")
public class MybatisPlusGenerator {

    //============================================ 数据库连接信息 根据实际情况改===============================================//
    private final String url = "************************************************";
    private final String userName = "postgres";
    private final String password = "Shendu188";
    //============================================ 数据库连接信息 根据实际情况改===============================================//

    //======================================= 项目路径根据自己实际情况改 window D:// =========================================//
    private final String path
            = System.getProperty("user.dir").substring(0, System.getProperty("user.dir").lastIndexOf("/")) + "/operationcenter-dal";
    //============================= ============ 项目路径根据自己实际情况改 window D:// ========================================//

    //====================================== 包名(例如rule)（会新建包不需要请置空）==============================================//

    private final String moduleName = "";
    //===================================== 包名(例如rule）（会新建包不需要请置空）==============================================//

    //===================================== 作者 ====================================================//
    private final String auth = "juju.";
    //===================================== 作者 ====================================================//

    //============================================ 需要生成的表多个用,分割 ==================================================//
    private final String needTables = "flight_order";
    //============================================ 需要生成的表多个用,分割 ==================================================//

    @Test
    public void generator() {
        FastAutoGenerator.create(new Builder(url, userName, password)
                        .keyWordsHandler(new MySqlKeyWordsHandler()) //关键字处理
                        .typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                            switch (metaInfo.getJdbcType()) {
                                //数据库tinyint类型映射为java的Integer类型
                                case TINYINT:
                                case BIT:
                                    return DbColumnType.INTEGER;
                                //数据库时间类型统一映射为java的Date类型
                                case DATE:
                                case TIME:
                                case TIMESTAMP:
                                    return DbColumnType.LOCAL_DATE_TIME;
                                default:
                                    return typeRegistry.getColumnType(metaInfo);
                            }
                        }))
                .globalConfig(builder -> {
                    builder
                            .author(auth) // 设置作者
                            .outputDir(path + "/src/main/java"); // 指定输出目录
                })

                .packageConfig(builder -> {
                    builder.parent("com.deepinnet.skyflow.operationcenter.dal") // 设置父包名
                            .entity("dataobject" + getModuleName())
                            .mapper("mapper" + getModuleName())
                            .service("repository" + getModuleName())
                            .serviceImpl(
                                    "repository" + getModuleName() + ".impl")
                            .pathInfo(
                                    Collections.singletonMap(OutputFile.xml,
                                            path + "/src/main/resources/mybatis/mapper/" + moduleName)); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> builder.addInclude(needTables) // 设置需要生成的表名 多个用,隔开
                        //.addTablePrefix("t_", "c_") // 设置过滤表前缀
                        //实体类 配置
                        .entityBuilder()
                        .idType(IdType.AUTO)
                        .enableActiveRecord()
                        //.superClass(BaseEntity.class)
                        .formatFileName("%sDO")
                        .enableFileOverride()  //是否覆盖
                        .enableLombok()
                        .logicDeleteColumnName("is_deleted")
                        .enableTableFieldAnnotation()
                        //mapper 配置
                        .mapperBuilder()
                        .formatMapperFileName("%sDao")
                        //.enableFileOverride() //是否覆盖
                        .enableBaseColumnList()
                        .enableBaseResultMap()
                        //repository 配置
                        .serviceBuilder()
                        //.enableFileOverride()
                        .formatServiceFileName("%sRepository")
                        .formatServiceImplFileName("%sRepositoryImpl"))
                .templateConfig(builder -> {
                    //不生成controller
                    builder.controller("");
                    //默认不生成xml 需要的话把下面代码注释掉
                    //builder.xml("");
                })
                .templateEngine(new BeetlTemplateEngine()) // 使用beetl引擎模板，默认的是Velocity引擎模板
                .execute();
    }

    private String getModuleName() {
        return StringUtils.isNotBlank(moduleName) ? "." + moduleName : moduleName;
    }

    public static void main(String[] args) {
        System.out.println(System.getProperty("user.dir"));
    }
}
