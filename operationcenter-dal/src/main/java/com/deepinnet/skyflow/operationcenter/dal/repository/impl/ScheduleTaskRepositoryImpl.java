package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ScheduleTaskDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.ScheduleTaskDao;
import com.deepinnet.skyflow.operationcenter.dal.model.UpdateTaskCondition;
import com.deepinnet.skyflow.operationcenter.dal.repository.ScheduleTaskRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 定时任务记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Service
public class ScheduleTaskRepositoryImpl extends ServiceImpl<ScheduleTaskDao, ScheduleTaskDO> implements ScheduleTaskRepository {

    private static final String LIMIT_SQL = " LIMIT ";

    @Override
    public boolean batchSave(List<ScheduleTaskDO> scheduleTasks) {
        if (CollUtil.isEmpty(scheduleTasks)) {
            return false;
        }

        return super.saveBatch(scheduleTasks);
    }

    @Override
    public List<ScheduleTaskDO> getAllByTypeAndStatusAndTriggerTime(List<String> types, List<String> statusList, Long triggerTime, Integer limitCount) {
        LambdaQueryWrapper<ScheduleTaskDO> wrapper = Wrappers.lambdaQuery(ScheduleTaskDO.class)
                .in(ScheduleTaskDO::getTaskType, types)
                .lt(ScheduleTaskDO::getTriggerTime, triggerTime)
                .in(ScheduleTaskDO::getStatus, statusList)
                .orderByAsc(ScheduleTaskDO::getTriggerTime)
                .last(LIMIT_SQL + limitCount);
        List<ScheduleTaskDO> taskDOs = super.list(wrapper);
        return CollUtil.isEmpty(taskDOs) ? null : taskDOs;
    }

    @Override
    public boolean updateTaskByCondition(UpdateTaskCondition updateTaskCondition) {
        LambdaUpdateWrapper<ScheduleTaskDO> updateWrapper = Wrappers.lambdaUpdate(ScheduleTaskDO.class)
                .set(updateTaskCondition.getExecuteTime() != null, ScheduleTaskDO::getExecuteTime, updateTaskCondition.getExecuteTime())
                .set(StrUtil.isNotEmpty(updateTaskCondition.getTargetStatus()), ScheduleTaskDO::getStatus, updateTaskCondition.getTargetStatus())
                .set(updateTaskCondition.getRetryCount() != null, ScheduleTaskDO::getRetryCount, updateTaskCondition.getRetryCount())
                .set(updateTaskCondition.getTriggerTime() != null, ScheduleTaskDO::getTriggerTime, updateTaskCondition.getTriggerTime())
                .eq(ScheduleTaskDO::getId, updateTaskCondition.getId())
                .eq(ScheduleTaskDO::getStatus, updateTaskCondition.getOriginalStatus())
                .eq(ScheduleTaskDO::getTenantId, updateTaskCondition.getTenantId());

        return super.update(updateWrapper);
    }

}
