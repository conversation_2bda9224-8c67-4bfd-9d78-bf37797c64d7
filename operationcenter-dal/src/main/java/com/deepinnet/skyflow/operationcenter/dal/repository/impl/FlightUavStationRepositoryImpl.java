package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavStationDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightUavStationMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightUavStationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 飞行无人机站点仓储实现类
 *
 * <AUTHOR>
 */
@Repository
@Slf4j
public class FlightUavStationRepositoryImpl extends ServiceImpl<FlightUavStationMapper, FlightUavStationDO> implements FlightUavStationRepository {

    @Resource
    private JdbcTemplate jdbcTemplate;
    
    // 表名常量
    private static final String TABLE_FLIGHT_UAV = "flight_uav";
    private static final String TABLE_FLIGHT_UAV_STATION = "flight_uav_station";

    @Override
    public List<FlightUavStationDO> findStationsInPolygonOrderByDistance(
            String polygonWkt, String tenantId, String status, List<String> flightUavBmList, int pageNum, int pageSize) {
        
        // 计算分页参数
        int offset = (pageNum - 1) * pageSize;
        
        // 构建SQL查询
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT fs.*, ");
        sql.append("ST_Distance(fs.coordinate::geography, ST_Centroid(ST_GeomFromText(?, 4326)), true) as distance ");
        sql.append("FROM flight_uav_station fs ");
        
        // 如果需要根据飞行器型号列表筛选，添加JOIN语句
        boolean hasFlightUavBmList = !CollectionUtils.isEmpty(flightUavBmList);
        if (hasFlightUavBmList) {
            sql.append("JOIN flight_uav f ON fs.flight_uav_no = f.flight_uav_no ");
        }
        
        sql.append("WHERE ");
        sql.append("fs.is_deleted = false ");
        
        List<Object> params = new ArrayList<>();
        params.add(polygonWkt);
        
        // 添加可选条件
        if (StringUtils.isNotBlank(tenantId)) {
            sql.append("AND fs.tenant_id = ? ");
            params.add(tenantId);
        }
        
        if (StringUtils.isNotBlank(status)) {
            sql.append("AND fs.status = ? ");
            params.add(status);
        }
        
        // 添加飞行器型号列表筛选条件
        if (hasFlightUavBmList) {
            sql.append("AND f.flight_uav_bm_no IN (");
            for (int i = 0; i < flightUavBmList.size(); i++) {
                if (i > 0) {
                    sql.append(", ");
                }
                sql.append("?");
                params.add(flightUavBmList.get(i));
            }
            sql.append(") ");
        }
        
        // 排序和分页
        sql.append("ORDER BY distance ASC ");
        sql.append("LIMIT ? OFFSET ?");
        params.add(pageSize);
        params.add(offset);
        
        // 执行查询
        return jdbcTemplate.query(sql.toString(), params.toArray(), new FlightUavStationRowMapper());
    }
    
    @Override
    public long countStationsInPolygon(String polygonWkt, String tenantId, String status, List<String> flightUavBmList) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) FROM flight_uav_station fs ");
        
        // 如果需要根据飞行器型号列表筛选，添加JOIN语句
        boolean hasFlightUavBmList = !CollectionUtils.isEmpty(flightUavBmList);
        if (hasFlightUavBmList) {
            sql.append("JOIN flight_uav f ON fs.flight_uav_no = f.flight_uav_no ");
        }
        
        sql.append("WHERE ");
        sql.append("fs.is_deleted = false ");
        
        List<Object> params = new ArrayList<>();

        // 添加可选条件
        if (StringUtils.isNotBlank(tenantId)) {
            sql.append("AND fs.tenant_id = ? ");
            params.add(tenantId);
        }
        
        if (StringUtils.isNotBlank(status)) {
            sql.append("AND fs.status = ? ");
            params.add(status);
        }
        
        // 添加飞行器型号列表筛选条件
        if (hasFlightUavBmList) {
            sql.append("AND f.flight_uav_bm_no IN (");
            for (int i = 0; i < flightUavBmList.size(); i++) {
                if (i > 0) {
                    sql.append(", ");
                }
                sql.append("?");
                params.add(flightUavBmList.get(i));
            }
            sql.append(") ");
        }
        
        return jdbcTemplate.queryForObject(sql.toString(), params.toArray(), Long.class);
    }
    
    /**
     * 保存飞行无人机站点
     *
     * @param flightUavStationDO 飞行无人机站点DO
     * @return 是否保存成功
     */
    @Override
    public boolean save(FlightUavStationDO flightUavStationDO) {
        return super.save(flightUavStationDO);
    }

    /**
     * 更新飞行无人机站点
     *
     * @param flightUavStationDO 飞行无人机站点DO
     * @return 是否更新成功
     */
    @Override
    public boolean updateById(FlightUavStationDO flightUavStationDO) {
        return super.updateById(flightUavStationDO);
    }

    /**
     * 根据id查询飞行无人机站点
     *
     * @param id 主键id
     * @return 飞行无人机站点DO
     */
    @Override
    public FlightUavStationDO getById(Long id) {
        return super.getById(id);
    }

    /**
     * 根据查询条件获取飞行无人机站点列表
     *
     * @param queryWrapper 查询条件
     * @return 飞行无人机站点DO列表
     */
    @Override
    public List<FlightUavStationDO> list(LambdaQueryWrapper<FlightUavStationDO> queryWrapper) {
        return super.list(queryWrapper);
    }
    
    /**
     * 根据飞行器型号编号列表查询飞行无人机站点
     *
     * @param flightStationNo 机巢编号
     * @param companyUserNo   服务商编号
     * @param tenantId        租户ID
     * @param flightUavNo     飞行器编号
     * @param status          状态
     * @param minRadius       最小半径
     * @param maxRadius       最大半径
     * @param flightUavBmList 飞行器型号编号列表
     * @param flightUavSn     飞行器SN码
     * @param name            机巢名称
     * @param provinceCode    省份编码
     * @param cityCode        城市编码
     * @param districtCode    区县编码
     * @param flightUavFlyType 飞行器飞行方式类型
     * @param flightUavBmModelNo 飞行器机型型号
     * @return 飞行无人机站点DO列表
     */
    @Override
    public List<FlightUavStationDO> queryStationsByFlightUavBm(
            String flightStationNo,
            String companyUserNo,
            String tenantId,
            String flightUavNo,
            String status,
            Integer minRadius,
            Integer maxRadius,
            List<String> flightUavBmList,
            String flightUavSn,
            String name,
            String provinceCode,
            String cityCode,
            String districtCode,
            String flightUavFlyType,
            String flightUavBmModelNo) {
        
        // 构建查询条件
        LambdaQueryWrapper<FlightUavStationDO> queryWrapper = Wrappers.lambdaQuery(FlightUavStationDO.class)
                .eq(StringUtils.isNotBlank(flightStationNo), FlightUavStationDO::getFlightStationNo, flightStationNo)
                .eq(StringUtils.isNotBlank(companyUserNo), FlightUavStationDO::getCompanyUserNo, companyUserNo)
                .eq(StringUtils.isNotBlank(tenantId), FlightUavStationDO::getTenantId, tenantId)
                .eq(StringUtils.isNotBlank(flightUavNo), FlightUavStationDO::getFlightUavNo, flightUavNo)
                .like(StringUtils.isNotBlank(name), FlightUavStationDO::getName, name)
                .eq(StringUtils.isNotBlank(provinceCode), FlightUavStationDO::getProvinceCode, provinceCode)
                .eq(StringUtils.isNotBlank(cityCode), FlightUavStationDO::getCityCode, cityCode)
                .eq(StringUtils.isNotBlank(districtCode), FlightUavStationDO::getDistrictCode, districtCode)
                .eq(status != null, FlightUavStationDO::getStatus, status)
                .eq(FlightUavStationDO::getIsDeleted, false)
                .ge(minRadius != null, FlightUavStationDO::getRadius, minRadius)
                .le(maxRadius != null, FlightUavStationDO::getRadius, maxRadius);
                
        // 添加SN和机型相关查询条件
        boolean hasUavConditions = !CollectionUtils.isEmpty(flightUavBmList) || 
                StringUtils.isNotBlank(flightUavSn) || 
                StringUtils.isNotBlank(flightUavFlyType) || 
                StringUtils.isNotBlank(flightUavBmModelNo);
        
        if (hasUavConditions) {
            // 如果需要查询飞行方式类型或机型型号，需要连接flight_uav和flight_uav_bm表
            if (StringUtils.isNotBlank(flightUavFlyType) || StringUtils.isNotBlank(flightUavBmModelNo)) {
                StringBuilder existsClause = new StringBuilder();
                existsClause.append(String.format("SELECT 1 FROM %s f ", TABLE_FLIGHT_UAV));
                existsClause.append(String.format("JOIN flight_uav_bm bm ON f.flight_uav_bm_no = bm.flight_uav_bm_no "));
                existsClause.append(String.format("WHERE f.flight_uav_no = %s.flight_uav_no ", TABLE_FLIGHT_UAV_STATION));
                
                List<String> conditions = new ArrayList<>();
                
                if (StringUtils.isNotBlank(flightUavFlyType)) {
                    conditions.add(String.format("bm.flight_uav_fly_type = '%s'", flightUavFlyType));
                }
                
                if (StringUtils.isNotBlank(flightUavBmModelNo)) {
                    conditions.add(String.format("bm.flight_uav_bm_model_no LIKE '%%%s%%'", flightUavBmModelNo));
                }
                
                // 添加其他可选条件
                if (!CollectionUtils.isEmpty(flightUavBmList)) {
                    conditions.add(String.format("f.flight_uav_bm_no IN (%s)",
                            flightUavBmList.stream()
                                    .map(s -> "'" + s + "'")
                                    .collect(Collectors.joining(","))));
                }
                
                if (StringUtils.isNotBlank(flightUavSn)) {
                    // 使用LIKE进行模糊查询，而不是精确匹配
                    conditions.add(String.format("f.flight_uav_sn LIKE '%%%s%%'", flightUavSn));
                }
                
                if (!conditions.isEmpty()) {
                    existsClause.append(" AND ").append(String.join(" AND ", conditions));
                }
                
                queryWrapper.exists(existsClause.toString());
            } else {
                // 原有的查询逻辑，不需要关联flight_uav_bm表
                StringBuilder existsClause = new StringBuilder();
                existsClause.append(String.format("SELECT 1 FROM %s f WHERE f.flight_uav_no = %s.flight_uav_no", 
                        TABLE_FLIGHT_UAV, TABLE_FLIGHT_UAV_STATION));
                
                List<String> conditions = new ArrayList<>();
                
                if (!CollectionUtils.isEmpty(flightUavBmList)) {
                    conditions.add(String.format("f.flight_uav_bm_no IN (%s)",
                            flightUavBmList.stream()
                                    .map(s -> "'" + s + "'")
                                    .collect(Collectors.joining(","))));
                }
                
                if (StringUtils.isNotBlank(flightUavSn)) {
                    // 使用LIKE进行模糊查询，而不是精确匹配
                    conditions.add(String.format("f.flight_uav_sn LIKE '%%%s%%'", flightUavSn));
                }
                
                if (!conditions.isEmpty()) {
                    existsClause.append(" AND ").append(String.join(" AND ", conditions));
                }
                
                queryWrapper.exists(existsClause.toString());
            }
        }
        
        queryWrapper.orderByDesc(FlightUavStationDO::getGmtCreated);
        
        return this.baseMapper.selectList(queryWrapper);
    }
    
    /**
     * 更新飞行无人机站点所有列，包括空值
     *
     * @param flightUavStationDO 飞行无人机站点DO
     * @return 是否更新成功
     */
    public boolean updateAllColumnById(FlightUavStationDO flightUavStationDO) {
        // 直接使用SQL更新，确保所有列都会被更新，包括NULL值
        return this.baseMapper.updateAllColumns(flightUavStationDO) > 0;
    }
    
    /**
     * RowMapper for FlightUavStationDO
     */
    private static class FlightUavStationRowMapper implements RowMapper<FlightUavStationDO> {
        @Override
        public FlightUavStationDO mapRow(ResultSet rs, int rowNum) throws SQLException {
            FlightUavStationDO station = new FlightUavStationDO();
            station.setId(rs.getInt("id"));
            station.setFlightStationNo(rs.getString("flight_station_no"));
            station.setCompanyUserNo(rs.getString("company_user_no"));
            station.setAvailableQuantity(rs.getInt("available_quantity"));
            station.setRadius(rs.getBigDecimal("radius"));
            station.setFlightUavNo(rs.getString("flight_uav_no"));
            station.setTenantId(rs.getString("tenant_id"));
            station.setName(rs.getString("name"));
            station.setProvince(rs.getString("province"));
            station.setCity(rs.getString("city"));
            station.setDistrict(rs.getString("district"));
            station.setAddress(rs.getString("address"));
            station.setStatus(rs.getString("status"));
            station.setIsDeleted(rs.getBoolean("is_deleted"));
            
            // 处理日期时间字段
            java.sql.Timestamp gmtCreated = rs.getTimestamp("gmt_created");
            if (gmtCreated != null) {
                station.setGmtCreated(gmtCreated.toLocalDateTime());
            }
            
            java.sql.Timestamp gmtModified = rs.getTimestamp("gmt_modified");
            if (gmtModified != null) {
                station.setGmtModified(gmtModified.toLocalDateTime());
            }
            
            // 设置额外的距离字段（如果需要，在Station对象中添加一个临时字段来保存）
            try {
                double distance = rs.getDouble("distance");
                station.setDistanceMeters(distance);
            } catch (SQLException e) {
                // 忽略异常，可能查询中没有距离字段
            }
            
            return station;
        }
    }
} 