package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.WhiteListDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.WhiteListMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.WhiteListRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 白名单数据仓库实现类
 *
 * <AUTHOR>
 */
@Repository
public class WhiteListRepositoryImpl extends ServiceImpl<WhiteListMapper, WhiteListDO> implements WhiteListRepository {
    
    @Resource
    private WhiteListMapper whiteListMapper;
    
    @Override
    public List<Map<String, Object>> findDistinctCustomersWithPaging(
            String customerUserName,
            String supplierUserName,
            String tenantId,
            Integer offset,
            Integer limit
    ) {
        return whiteListMapper.findDistinctCustomersWithPaging(
                customerUserName,
                supplierUserName,
                tenantId,
                offset,
                limit
        );
    }
    
    @Override
    public long countDistinctCustomers(
            String customerUserName,
            String supplierUserName,
            String tenantId
    ) {
        return whiteListMapper.countDistinctCustomers(
                customerUserName,
                supplierUserName,
                tenantId
        );
    }
    
    @Override
    public List<WhiteListDO> findByCustomerUserNo(String customerUserNo) {
        return list(
                Wrappers.lambdaQuery(WhiteListDO.class)
                        .eq(StringUtils.isNotBlank(customerUserNo), WhiteListDO::getCustomerUserNo, customerUserNo)
                        .orderByDesc(WhiteListDO::getGmtCreated)
        );
    }
    
    @Override
    public boolean deleteByCustomerUserNo(String customerUserNo) {
        return remove(
                Wrappers.lambdaQuery(WhiteListDO.class)
                        .eq(StringUtils.isNotBlank(customerUserNo), WhiteListDO::getCustomerUserNo, customerUserNo)
        );
    }
} 