package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavBmImportDetailDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightUavBmImportDetailMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightUavBmImportDetailRepository;
import org.springframework.stereotype.Repository;

/**
 * 飞行无人机品牌型号导入明细仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightUavBmImportDetailRepositoryImpl extends ServiceImpl<FlightUavBmImportDetailMapper, FlightUavBmImportDetailDO>
        implements FlightUavBmImportDetailRepository {
} 