package com.deepinnet.skyflow.operationcenter.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightProductDO;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 飞行产品 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FlightProductMapper extends BaseMapper<FlightProductDO> {
    
    /**
     * 分页查询飞行产品（带关联查询）
     * 
     * 支持的查询条件：
     * - 基础查询条件：产品编号、产品名称、产品类型等
     * - 关联查询条件：飞行方式、无人机品牌名称、无人机型号编号等
     *
     * @param queryDTO 查询条件
     * @return 飞行产品列表
     */
    List<FlightProductDO> pageQueryFlightProductWithJoin(@Param("queryDTO") FlightProductQueryDTO queryDTO);
} 