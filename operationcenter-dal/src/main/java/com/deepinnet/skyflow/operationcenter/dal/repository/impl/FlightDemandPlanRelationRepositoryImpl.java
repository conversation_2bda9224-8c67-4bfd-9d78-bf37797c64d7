package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandPlanRelationDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDemandPlanRelationMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandPlanRelationRepository;
import org.springframework.stereotype.Repository;

/**
 * 飞行需求与计划关联表仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightDemandPlanRelationRepositoryImpl extends ServiceImpl<FlightDemandPlanRelationMapper, FlightDemandPlanRelationDO>
        implements FlightDemandPlanRelationRepository {
} 