package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 白名单实体
 *
 * <AUTHOR>
 */
@Data
@TableName("white_list")
public class WhiteListDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 客户编号
     */
    private String customerUserNo;
    
    /**
     * 服务商编号
     */
    private String supplierUserNo;
    
    /**
     * 客户名称
     */
    private String customerUserName;
    
    /**
     * 服务商名称
     */
    private String supplierUserName;
    
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean isDeleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;
    
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
    
    /**
     * 租户ID
     */
    private String tenantId;
} 