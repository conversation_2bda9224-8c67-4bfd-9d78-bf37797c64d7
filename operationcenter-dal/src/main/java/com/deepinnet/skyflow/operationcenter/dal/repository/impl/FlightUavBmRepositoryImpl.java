package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavBmDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightUavBmMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightUavBmRepository;
import org.springframework.stereotype.Repository;

/**
 * 飞行无人机品牌型号仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightUavBmRepositoryImpl extends ServiceImpl<FlightUavBmMapper, FlightUavBmDO>
        implements FlightUavBmRepository {
} 