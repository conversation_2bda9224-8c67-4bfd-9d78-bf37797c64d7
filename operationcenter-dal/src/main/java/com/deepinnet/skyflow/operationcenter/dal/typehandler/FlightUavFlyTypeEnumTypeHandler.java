package com.deepinnet.skyflow.operationcenter.dal.typehandler;

import com.deepinnet.skyflow.operationcenter.enums.FlightUavFlyTypeEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;

/**
 * 飞行无人机飞行方式枚举类型处理器
 *
 * <AUTHOR>
 */
public class FlightUavFlyTypeEnumTypeHandler extends BaseTypeHandler<FlightUavFlyTypeEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, FlightUavFlyTypeEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getType());
    }

    @Override
    public FlightUavFlyTypeEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String type = rs.getString(columnName);
        return getFlightUavFlyTypeEnum(type);
    }

    @Override
    public FlightUavFlyTypeEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String type = rs.getString(columnIndex);
        return getFlightUavFlyTypeEnum(type);
    }

    @Override
    public FlightUavFlyTypeEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String type = cs.getString(columnIndex);
        return getFlightUavFlyTypeEnum(type);
    }

    private FlightUavFlyTypeEnum getFlightUavFlyTypeEnum(String type) {
        if (type == null) {
            return null;
        }
        
        for (FlightUavFlyTypeEnum flyTypeEnum : FlightUavFlyTypeEnum.values()) {
            if (Objects.equals(flyTypeEnum.getType(), type)) {
                return flyTypeEnum;
            }
        }
        
        return null;
    }
} 