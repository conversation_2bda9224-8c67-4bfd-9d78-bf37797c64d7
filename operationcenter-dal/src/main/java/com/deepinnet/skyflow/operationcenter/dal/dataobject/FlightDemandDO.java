package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.deepinnet.skyflow.operationcenter.dal.config.DataPermissionColumn;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 飞行需求表实体类
 *
 * <AUTHOR>
 */
@DataPermissionColumn("publisher_no")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_demand")
public class FlightDemandDO extends Model<FlightDemandDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 需求编号
     */
    @TableField("demand_no")
    private String demandNo;

    /**
     * 需求名称
     */
    @TableField("name")
    private String name;

    /**
     * 需求描述
     */
    @TableField("demand_desc")
    private String demandDesc;

    /**
     * 需求类型
     */
    @TableField("type")
    private String type;

    /**
     * 需求来源场景
     */
    @TableField("scene")
    private String scene;

    /**
     * 发布者编号
     */
    @TableField("publisher_no")
    private String publisherNo;

    /**
     * 发布者名称
     */
    @TableField(value = "publisher_name")
    private String publisherName;

    /**
     * 发布时间
     */
    @TableField("publish_time")
    private LocalDateTime publishTime;

    /**
     * 飞行订单编号
     */
    @TableField("flight_order_no")
    private String flightOrderNo;

    @TableField("product_name")
    private String productName;

    /**
     * 飞行无人机型号
     */
    @TableField("flight_uav_bm")
    private String flightUavBm;

    /**
     * 增值服务
     */
    @TableField("increment_service")
    private String incrementService;

    /**
     * 附加文件请求
     */
    @TableField("request_additional_files")
    private String requestAdditionalFiles;

    /**
     * 详细类目编号
     */
    @TableField("category_no")
    private String categoryNo;

    @TableField("category_full_name")
    private String categoryFullName;

    /**
     * 匹配状态
     */
    @TableField("match_status")
    private String matchStatus;

    /**
     * 同步状态
     */
    @TableField("sync_status")
    private String syncStatus;

    /**
     * 巡检区域名称
     */
    @TableField("inspection_area_name")
    private String inspectionAreaName;

    /**
     * 巡检区域编码, 格式: provinceCode/cityCode/countryCode/streetCode
     */
    @TableField("inspection_area_code")
    private String inspectionAreaCode;

    /**
     * 区域面积，平方公里
     */
    @TableField("area")
    private Double area;

    /**
     * 中心点坐标-经度
     */
    @TableField("center_point_longitude")
    private String centerPointLongitude;

    /**
     * 中心点坐标-纬度
     */
    @TableField("center_point_latitude")
    private String centerPointLatitude;

    /**
     * 区域坐标，WKT字符串
     */
    @TableField("area_coordinate")
    private String areaCoordinate;

    /**
     * 服务提供商编号
     */
    @TableField("service_provider_no")
    private String serviceProviderNo;

    /**
     * 服务提供商名称
     */
    @TableField("service_provider_name")
    private String serviceProviderName;

    /**
     * 服务提供商公司名称
     */
    @TableField(value = "service_provider_company_name")
    private String ServiceProviderCompanyName;

    /**
     * 服务提供商组织机构ID
     */
    @TableField(value = "service_provider_organization_id")
    private String serviceProviderOrganizationId;

    /**
     * 客户组织ID
     */
    @TableField(value = "organization_id")
    private String organizationId;

    /**
     * 客户组织名称
     */
    @TableField(value = "organization_name")
    private String organizationName;

    /**
     * 同步订单编号
     */
    @TableField("sync_order_no")
    private String syncOrderNo;

    /**
     * 业务数据
     */
    @TableField("biz_data")
    private String bizData;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
} 