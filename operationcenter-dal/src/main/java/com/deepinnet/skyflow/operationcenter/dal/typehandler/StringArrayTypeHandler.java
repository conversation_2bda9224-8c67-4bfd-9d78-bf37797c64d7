package com.deepinnet.skyflow.operationcenter.dal.typehandler;

import com.alibaba.fastjson2.JSON;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 用于处理String[]类型字段的MyBatis TypeHandler
 *
 * <AUTHOR>
 */
@MappedTypes(String[].class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class StringArrayTypeHandler extends BaseTypeHandler<String[]> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String[] parameter, JdbcType jdbcType) throws SQLException {
        // 将String[]转换为JSON数组字符串
        String arrayJson = JSON.toJSONString(parameter);
        ps.setString(i, arrayJson);
    }

    @Override
    public String[] getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String arrayJson = rs.getString(columnName);
        return getStringArray(arrayJson);
    }

    @Override
    public String[] getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String arrayJson = rs.getString(columnIndex);
        return getStringArray(arrayJson);
    }

    @Override
    public String[] getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String arrayJson = cs.getString(columnIndex);
        return getStringArray(arrayJson);
    }

    private String[] getStringArray(String arrayJson) {
        if (arrayJson == null || arrayJson.isEmpty()) {
            return new String[0];
        }
        try {
            return JSON.parseObject(arrayJson, String[].class);
        } catch (Exception e) {
            // 如果解析失败，返回空数组
            return new String[0];
        }
    }
} 