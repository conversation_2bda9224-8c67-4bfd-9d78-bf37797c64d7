package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderApprovalDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightOrderApprovalDao;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderApprovalRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Service
public class FlightOrderApprovalRepositoryImpl extends ServiceImpl<FlightOrderApprovalDao, FlightOrderApprovalDO> implements FlightOrderApprovalRepository {

}
