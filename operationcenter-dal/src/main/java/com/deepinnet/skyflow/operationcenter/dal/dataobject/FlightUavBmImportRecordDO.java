package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 飞行无人机品牌型号导入记录表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_uav_bm_import_record")
public class FlightUavBmImportRecordDO extends Model<FlightUavBmImportRecordDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 导入批次号
     */
    @TableField("import_batch_no")
    private String importBatchNo;

    /**
     * 导入文件名
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 导入总条数
     */
    @TableField("total_count")
    private Integer totalCount;

    /**
     * 成功条数
     */
    @TableField("success_count")
    private Integer successCount;

    /**
     * 失败条数
     */
    @TableField("fail_count")
    private Integer failCount;

    /**
     * 导入状态（0-导入中，1-导入成功，2-导入失败）
     */
    @TableField("import_status")
    private Integer importStatus;

    /**
     * 导入失败原因
     */
    @TableField("fail_reason")
    private String failReason;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;
} 