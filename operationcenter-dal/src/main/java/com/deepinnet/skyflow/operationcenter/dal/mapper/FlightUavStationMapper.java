package com.deepinnet.skyflow.operationcenter.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavStationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 飞行无人机站点 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FlightUavStationMapper extends BaseMapper<FlightUavStationDO> {
    
    /**
     * 更新全部字段，包括NULL值
     * 
     * @param entity 实体对象
     * @return 影响行数
     */
    @Update("<script>" +
            "UPDATE flight_uav_station SET " +
            "flight_station_no = #{entity.flightStationNo}, " +
            "company_user_no = #{entity.companyUserNo}, " +
            "available_quantity = #{entity.availableQuantity}, " +
            "radius = #{entity.radius}, " +
            "flight_uav_no = #{entity.flightUavNo}, " +
            "name = #{entity.name}, " +
            "province = #{entity.province}, " +
            "province_code = #{entity.provinceCode}, " +
            "city = #{entity.city}, " +
            "city_code = #{entity.cityCode}, " +
            "district = #{entity.district}, " +
            "district_code = #{entity.districtCode}, " +
            "address = #{entity.address}, " +
            "<if test='entity.coordinate != null'>" +
            "coordinate = #{entity.coordinate, typeHandler=com.deepinnet.skyflow.operationcenter.dal.typehandler.PGPointTypeHandler}, " +
            "</if>" +
            "<if test='entity.coordinate == null'>" +
            "coordinate = null, " +
            "</if>" +
            "status = #{entity.status}, " +
            "tenant_id = #{entity.tenantId}, " +
            "gmt_modified = #{entity.gmtModified} " +
            "WHERE id = #{entity.id} AND is_deleted = false" +
            "</script>")
    int updateAllColumns(@Param("entity") FlightUavStationDO entity);
} 