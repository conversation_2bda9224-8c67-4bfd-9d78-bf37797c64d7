package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderProductUsageDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightOrderProductUsageDao;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderProductUsageRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Service
public class FlightOrderProductUsageRepositoryImpl extends ServiceImpl<FlightOrderProductUsageDao, FlightOrderProductUsageDO> implements FlightOrderProductUsageRepository {

}
