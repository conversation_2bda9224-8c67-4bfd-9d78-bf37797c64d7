package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.locationtech.jts.geom.Point;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 飞行无人机站点表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_uav_station")
public class FlightUavStationDO extends Model<FlightUavStationDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 机巢编号
     */
    @TableField("flight_station_no")
    private String flightStationNo;

    /**
     * 服务商编号
     */
    @TableField("company_user_no")
    private String companyUserNo;

    /**
     * 可用无人机数量
     */
    @TableField("available_quantity")
    private Integer availableQuantity;

    /**
     * 站点覆盖半径(米)
     */
    @TableField("radius")
    private BigDecimal radius;

    /**
     * 关联的飞行器编号
     */
    @TableField("flight_uav_no")
    private String flightUavNo;

    /**
     * 机巢名称
     */
    @TableField("name")
    private String name;

    /**
     * 省份
     */
    @TableField("province")
    private String province;

    /**
     * 省份编码
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 区县
     */
    @TableField("district")
    private String district;

    /**
     * 区县编码
     */
    @TableField("district_code")
    private String districtCode;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 坐标
     */
    @TableField(value = "coordinate", typeHandler = com.deepinnet.skyflow.operationcenter.dal.typehandler.PGPointTypeHandler.class)
    private Point coordinate;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 租户
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 临时字段，用于存储距离信息（米）
     */
    @TableField(exist = false)
    private Double distanceMeters;
} 