package com.deepinnet.skyflow.operationcenter.dal.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavStationDO;

import java.util.List;

/**
 * 飞行无人机站点仓储接口
 *
 * <AUTHOR>
 */
public interface FlightUavStationRepository extends IService<FlightUavStationDO> {
    
    /**
     * 根据WKT多边形查询站点并按距离排序
     *
     * @param polygonWkt 多边形WKT字符串
     * @param tenantId 租户ID，可为空
     * @param status 状态，可为空
     * @param flightUavBmList 飞行器型号编号列表，可为空
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 站点列表，带距离信息
     */
    List<FlightUavStationDO> findStationsInPolygonOrderByDistance(
            String polygonWkt, 
            String tenantId, 
            String status,
            List<String> flightUavBmList,
            int pageNum, 
            int pageSize);
    
    /**
     * 统计多边形内的站点数量
     *
     * @param polygonWkt 多边形WKT字符串
     * @param tenantId 租户ID，可为空
     * @param status 状态，可为空
     * @param flightUavBmList 飞行器型号编号列表，可为空
     * @return 站点数量
     */
    long countStationsInPolygon(String polygonWkt, String tenantId, String status, List<String> flightUavBmList);

    /**
     * 保存飞行无人机站点
     *
     * @param flightUavStationDO 飞行无人机站点DO
     * @return 是否保存成功
     */
    boolean save(FlightUavStationDO flightUavStationDO);

    /**
     * 更新飞行无人机站点
     *
     * @param flightUavStationDO 飞行无人机站点DO
     * @return 是否更新成功
     */
    boolean updateById(FlightUavStationDO flightUavStationDO);

    /**
     * 更新飞行无人机站点所有列，包括空值
     *
     * @param flightUavStationDO 飞行无人机站点DO
     * @return 是否更新成功
     */
    boolean updateAllColumnById(FlightUavStationDO flightUavStationDO);

    /**
     * 根据id查询飞行无人机站点
     *
     * @param id 主键id
     * @return 飞行无人机站点DO
     */
    FlightUavStationDO getById(Long id);

    /**
     * 根据查询条件获取飞行无人机站点列表
     *
     * @param queryWrapper 查询条件
     * @return 飞行无人机站点DO列表
     */
    List<FlightUavStationDO> list(LambdaQueryWrapper<FlightUavStationDO> queryWrapper);

    /**
     * 根据飞行器型号编号列表查询飞行无人机站点
     *
     * @param flightStationNo 机巢编号
     * @param companyUserNo   服务商编号
     * @param tenantId        租户ID
     * @param flightUavNo     飞行器编号
     * @param status          状态
     * @param minRadius       最小半径
     * @param maxRadius       最大半径
     * @param flightUavBmList 飞行器型号编号列表
     * @param flightUavSn     飞行器SN码
     * @param name            机巢名称
     * @param provinceCode    省份编码
     * @param cityCode        城市编码
     * @param districtCode    区县编码
     * @param flightUavFlyType 飞行器飞行方式类型
     * @param flightUavBmModelNo 飞行器机型型号
     * @return 飞行无人机站点DO列表
     */
    List<FlightUavStationDO> queryStationsByFlightUavBm(
        String flightStationNo,
        String companyUserNo,
        String tenantId,
        String flightUavNo,
        String status,
        Integer minRadius,
        Integer maxRadius,
        List<String> flightUavBmList,
        String flightUavSn,
        String name,
        String provinceCode,
        String cityCode,
        String districtCode,
        String flightUavFlyType,
        String flightUavBmModelNo
    );
} 