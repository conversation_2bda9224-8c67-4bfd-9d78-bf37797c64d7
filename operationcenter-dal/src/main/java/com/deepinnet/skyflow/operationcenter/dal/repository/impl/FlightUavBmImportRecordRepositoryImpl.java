package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavBmImportRecordDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightUavBmImportRecordMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightUavBmImportRecordRepository;
import org.springframework.stereotype.Repository;

/**
 * 飞行无人机品牌型号导入记录仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightUavBmImportRecordRepositoryImpl extends ServiceImpl<FlightUavBmImportRecordMapper, FlightUavBmImportRecordDO>
        implements FlightUavBmImportRecordRepository {
} 