package com.deepinnet.skyflow.operationcenter.dal.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Description:
 * Date: 2025/5/21
 * Author: lijunheng
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface DataPermissionColumn {

    String value() default "creator";
}
