package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 飞行需求与计划关联表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_demand_plan_relation")
public class FlightDemandPlanRelationDO extends Model<FlightDemandPlanRelationDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 飞行需求编号
     */
    @TableField("flight_demand_code")
    private String flightDemandCode;

    /**
     * 计划ID
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;
} 