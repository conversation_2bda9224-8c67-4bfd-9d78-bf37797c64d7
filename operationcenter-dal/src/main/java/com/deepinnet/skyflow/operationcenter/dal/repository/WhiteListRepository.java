package com.deepinnet.skyflow.operationcenter.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.WhiteListDO;

import java.util.List;
import java.util.Map;

/**
 * 白名单数据仓库接口
 *
 * <AUTHOR>
 */
public interface WhiteListRepository extends IService<WhiteListDO> {
    
    /**
     * 查询按客户分组的白名单分页数据
     *
     * @param customerUserName 客户名称（模糊查询）
     * @param supplierUserName 服务商名称（模糊查询）
     * @param tenantId 租户ID
     * @param offset 偏移量
     * @param limit 每页记录数
     * @return 分组后的客户列表
     */
    List<Map<String, Object>> findDistinctCustomersWithPaging(
            String customerUserName, 
            String supplierUserName, 
            String tenantId, 
            Integer offset, 
            Integer limit
    );
    
    /**
     * 统计按客户分组的白名单总数
     *
     * @param customerUserName 客户名称（模糊查询）
     * @param supplierUserName 服务商名称（模糊查询）
     * @param tenantId 租户ID
     * @return 客户总数
     */
    long countDistinctCustomers(
            String customerUserName, 
            String supplierUserName, 
            String tenantId
    );
    
    /**
     * 根据客户编号查询其白名单列表
     *
     * @param customerUserNo 客户编号
     * @return 白名单列表
     */
    List<WhiteListDO> findByCustomerUserNo(String customerUserNo);
    
    /**
     * 根据客户编号删除其白名单记录
     *
     * @param customerUserNo 客户编号
     * @return 删除是否成功
     */
    boolean deleteByCustomerUserNo(String customerUserNo);
} 