package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightProductDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightProductMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightProductRepository;
import org.springframework.stereotype.Repository;

/**
 * 飞行产品仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightProductRepositoryImpl extends ServiceImpl<FlightProductMapper, FlightProductDO>
        implements FlightProductRepository {
} 