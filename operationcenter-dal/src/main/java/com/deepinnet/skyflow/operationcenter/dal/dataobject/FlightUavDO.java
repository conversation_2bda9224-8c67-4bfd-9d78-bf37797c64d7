package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 飞行无人机表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_uav")
public class FlightUavDO extends Model<FlightUavDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 无人机编号
     */
    @TableField("flight_uav_no")
    private String flightUavNo;

    /**
     * 飞行器名称
     */
    @TableField("flight_uav_name")
    private String flightUavName;

    /**
     * 归属机巢
     */
    @TableField("flight_station_no")
    private String flightStationNo;

    /**
     * SN码
     */
    @TableField("flight_uav_sn")
    private String flightUavSn;

    /**
     * 机型编号
     */
    @TableField("flight_uav_bm_no")
    private String flightUavBmNo;

    /**
     * 服务商编号
     */
    @TableField("supplier_user_no")
    private String supplierUserNo;

    /**
     * 租户
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
} 