package com.deepinnet.skyflow.operationcenter.dal.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.jdbc.PgArray;

import java.sql.*;

/**
 * 用于处理PostgreSQL中text[]类型字段的TypeHandler
 *
 * <AUTHOR>
 */
@MappedTypes(String[].class)
@MappedJdbcTypes(JdbcType.ARRAY)
public class PostgresTextArrayTypeHandler extends BaseTypeHandler<String[]> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String[] parameter, JdbcType jdbcType) throws SQLException {
        // 创建PostgreSQL数组
        Connection conn = ps.getConnection();
        Array array = conn.createArrayOf("text", parameter);
        ps.setArray(i, array);
    }

    @Override
    public String[] getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getArray(rs.getArray(columnName));
    }

    @Override
    public String[] getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getArray(rs.getArray(columnIndex));
    }

    @Override
    public String[] getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getArray(cs.getArray(columnIndex));
    }

    private String[] getArray(Array array) throws SQLException {
        if (array == null) {
            return new String[0];
        }
        
        Object obj = array.getArray();
        if (obj instanceof String[]) {
            return (String[]) obj;
        } else if (obj instanceof Object[]) {
            Object[] objArray = (Object[]) obj;
            String[] result = new String[objArray.length];
            for (int i = 0; i < objArray.length; i++) {
                if (objArray[i] != null) {
                    result[i] = objArray[i].toString();
                }
            }
            return result;
        }
        return new String[0];
    }
} 