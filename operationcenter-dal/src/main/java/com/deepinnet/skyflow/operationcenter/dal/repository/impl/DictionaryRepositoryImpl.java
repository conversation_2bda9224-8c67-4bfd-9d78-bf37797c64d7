package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.DictionaryDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.DictionaryMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.DictionaryRepository;
import org.springframework.stereotype.Repository;

/**
 * 字典仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class DictionaryRepositoryImpl extends ServiceImpl<DictionaryMapper, DictionaryDO>
        implements DictionaryRepository {
} 