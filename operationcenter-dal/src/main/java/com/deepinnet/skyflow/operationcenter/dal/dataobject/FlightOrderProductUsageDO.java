package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Getter
@Setter
@TableName("flight_order_product_usage")
public class FlightOrderProductUsageDO extends Model<FlightOrderProductUsageDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 产品编号
     */
    @TableField("product_no")
    private String productNo;

    /**
     * 产品类型
     */
    @TableField("product_type")
    private String productType;

    /**
     * 无人机型号
     */
    @TableField("uav_model")
    private String uavModel;

    /**
     * 订单产品用量
     */
    @TableField("total_quantity")
    private Integer totalQuantity;

    /**
     * 产品当前用量
     */
    @TableField("use_quantity")
    private Integer useQuantity;

    /**
     * 产品有效期类型(single-按次; week-7天; month-包月; year-包年)
     */
    @TableField("validity_period_type")
    private String validityPeriodType;

    /**
     * 有效期开始时间
     */
    @TableField("validity_period_start")
    private LocalDateTime validityPeriodStart;

    /**
     * 有效期结束时间
     */
    @TableField("validity_period_end")
    private LocalDateTime validityPeriodEnd;

    /**
     * 购买数量
     */
    @TableField("count")
    private Integer count;

    /**
     * 当前产品价格(数量 * 单价 = 当前产品价格)
     */
    @TableField("price")
    private String price;

    /**
     * 产品单价
     */
    @TableField("base_price")
    private String basePrice;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
