package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Getter
@Setter
@TableName("flight_order_flying_info")
public class FlightOrderFlyingInfoDO extends Model<FlightOrderFlyingInfoDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 飞行频次
     */
    @TableField("flying_frequency")
    private String flyingFrequency;

    /**
     * 飞行次数
     */
    @TableField("flying_num")
    private Integer flyingNum;

    /**
     * 预估飞行次数
     */
    @TableField("except_flying_num")
    private Integer exceptFlyingNum;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private String startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private String endTime;

    /**
     * 飞行区域
     */
    @TableField("flying_area")
    private String flyingArea;

    /**
     * 应用场景
     */
    @TableField("apply_scenarios")
    private String applyScenarios;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 有效期开始时间
     */
    @TableField("validity_period_start")
    private LocalDateTime validityPeriodStart;

    /**
     * 有效期结束时间
     */
    @TableField("validity_period_end")
    private LocalDateTime validityPeriodEnd;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
