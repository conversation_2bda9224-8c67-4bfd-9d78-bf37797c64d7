package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandPropDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDemandPropMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandPropRepository;
import org.springframework.stereotype.Repository;

/**
 * 飞行需求属性仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightDemandPropRepositoryImpl extends ServiceImpl<FlightDemandPropMapper, FlightDemandPropDO>
        implements FlightDemandPropRepository {
} 