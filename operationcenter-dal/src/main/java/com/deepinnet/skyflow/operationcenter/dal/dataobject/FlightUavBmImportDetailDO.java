package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 飞行无人机品牌型号导入明细表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_uav_bm_import_detail")
public class FlightUavBmImportDetailDO extends Model<FlightUavBmImportDetailDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 导入批次号
     */
    @TableField("import_batch_no")
    private String importBatchNo;

    /**
     * 所在行号
     */
    @TableField("row_num")
    private Integer rowNum;

    /**
     * 无人机品牌型号编号
     */
    @TableField("flight_uav_bm_no")
    private String flightUavBmNo;

    /**
     * 飞行无人机品牌型号名称（原始数据）
     */
    @TableField("raw_data")
    private String rawData;

    /**
     * 处理状态（0-处理中，1-成功，2-失败）
     */
    @TableField("process_status")
    private Integer processStatus;

    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 修改人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;
} 