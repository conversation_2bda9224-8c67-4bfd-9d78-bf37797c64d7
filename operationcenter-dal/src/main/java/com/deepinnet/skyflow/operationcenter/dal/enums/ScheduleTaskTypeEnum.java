package com.deepinnet.skyflow.operationcenter.dal.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@AllArgsConstructor
@Getter
public enum ScheduleTaskTypeEnum {

    CLOSE_ORDER("close_order", "关闭超时订单任务"),

    ;

    /**
     * 类型
     */
    private final String type;

    /**
     * 描述信息
     */
    private final String desc;

    public static ScheduleTaskTypeEnum getByType(String type) {
        if (type == null) {
            return null;
        }
        for (ScheduleTaskTypeEnum an : ScheduleTaskTypeEnum.values()) {
            if (StrUtil.equals(an.getType(), type)) {
                return an;
            }
        }

        return null;
    }


    public static final List<String> SUPPORT_TASK_TYPE_LIST = List.of(CLOSE_ORDER.getType());
}
