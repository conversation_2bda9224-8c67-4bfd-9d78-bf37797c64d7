package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Getter
@Setter
@TableName("flight_order")
public class FlightOrderDO extends Model<FlightOrderDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 订单状态(NOT_START-未开始; IN_PROGRESS-进行中; END-已结束)
     */
    @TableField("status")
    private String status;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 主产品类型(flight_uav - 机型服务产品；flight_scenario - 场景服务产品)
     */
    private String mainProductType;

    /**
     * 用户编号
     */
    @TableField("user_no")
    private String userNo;

    /**
     * 客户ID
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 组织ID
     */
    @TableField("organization_id")
    private String organizationId;

    /**
     * 订单金额
     */
    @TableField("order_amount")
    private String orderAmount;

    /**
     * 付款方式(MONTHLY_PAY-月结; IMMEDIATE_PAY-立即支付)
     */
    @TableField("pay_type")
    private String payType;

    /**
     * 支付渠道(alipay; wechat; unionpay)
     */
    @TableField("pay_source")
    private String paySource;

    /**
     * 审核状态(IN_REVIEW-审核中; APPROVED-审核通过; REJECT-审核拒绝)
     */
    @TableField("approve_status")
    private String approveStatus;

    /**
     * 支付状态（UNPAY-未支付; PAID-已支付）
     */
    @TableField("pay_status")
    private String payStatus;

    /**
     * 退款状态(UNREFUND-未退款; REFUNDING-退款中; REFUNDED-已退款)
     */
    @TableField("refund_status")
    private String refundStatus;

    /**
     * 支付金额
     */
    @TableField("pay_amount")
    private String payAmount;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private String refundAmount;

    /**
     * 下单时间
     */
    @TableField("order_time")
    private LocalDateTime orderTime;

    /**
     * 类目编号
     */
    @TableField("category_no")
    private String categoryNo;

    /**
     * 类目名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 场景
     */
    @TableField("scene")
    private String scene;

    /**
     * 订单类型
     */
    @TableField("order_type")
    private String orderType;

    /**
     * 组织名称
     */
    @TableField("organization_name")
    private String organizationName;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
