package com.deepinnet.skyflow.operationcenter.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.WhiteListDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 白名单数据映射接口
 *
 * <AUTHOR>
 */
@Mapper
public interface WhiteListMapper extends BaseMapper<WhiteListDO> {
    
    /**
     * 查询按客户分组的白名单分页数据
     *
     * @param customerUserName 客户名称（模糊查询）
     * @param supplierUserName 服务商名称（模糊查询）
     * @param tenantId 租户ID
     * @param offset 偏移量
     * @param limit 每页记录数
     * @return 分组后的客户列表
     */
    List<Map<String, Object>> findDistinctCustomersWithPaging(
            @Param("customerUserName") String customerUserName,
            @Param("supplierUserName") String supplierUserName,
            @Param("tenantId") String tenantId,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit
    );
    
    /**
     * 统计按客户分组的白名单总数
     *
     * @param customerUserName 客户名称（模糊查询）
     * @param supplierUserName 服务商名称（模糊查询）
     * @param tenantId 租户ID
     * @return 客户总数
     */
    long countDistinctCustomers(
            @Param("customerUserName") String customerUserName,
            @Param("supplierUserName") String supplierUserName,
            @Param("tenantId") String tenantId
    );
} 