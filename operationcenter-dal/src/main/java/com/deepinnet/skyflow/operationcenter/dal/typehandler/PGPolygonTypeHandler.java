package com.deepinnet.skyflow.operationcenter.dal.typehandler;

import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Polygon;
import org.postgis.PGgeometry;

/**
 * <PERSON><PERSON> z<PERSON>
 * Date 2024-04-25
 **/

@MappedTypes({Polygon.class})
public class PGPolygonTypeHandler extends AbstractGeometryTypeHandler<Polygon> {

    public Polygon getResult(PGgeometry pGgeometry) {
        if (pGgeometry == null) {
            return null;
        }
        String pgWkt = pGgeometry.toString();
        String target = String.format("SRID=%s;", SRID_IN_DB);
        String wkt = pgWkt.replace(target, "");
        try {
            return (Polygon) READER_POOL.get().read(wkt);
        } catch (Exception e) {
            throw new RuntimeException("解析Polygon wkt失败：" + wkt, e);
        }
    }
}
