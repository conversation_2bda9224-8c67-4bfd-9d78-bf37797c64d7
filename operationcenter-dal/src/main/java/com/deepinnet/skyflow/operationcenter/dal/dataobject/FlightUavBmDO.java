package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.deepinnet.skyflow.operationcenter.dal.typehandler.FlightUavCrewTypeEnumTypeHandler;
import com.deepinnet.skyflow.operationcenter.dal.typehandler.FlightUavFlyTypeEnumTypeHandler;
import com.deepinnet.skyflow.operationcenter.dal.typehandler.FlightUavWeightClassificationEnumTypeHandler;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavCrewTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavFlyTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavWeightClassificationEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 飞行无人机品牌型号表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_uav_bm")
public class FlightUavBmDO extends Model<FlightUavBmDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 系统内部型号编码（系统自动生成的唯一标识符）
     */
    @TableField("flight_uav_bm_no")
    private String flightUavBmNo;

    /**
     * 型号描述
     */
    @TableField("flight_uav_bm_desc")
    private String flightUavBmDesc;

    /**
     * 品牌名
     */
    @TableField("flight_uav_bm_name")
    private String flightUavBmName;

    /**
     * 第三方厂商的机器型号编号（厂商原始出厂型号）
     */
    @TableField("flight_uav_bm_model_no")
    private String flightUavBmModelNo;

    /**
     * 载人类型
     */
    @TableField(value = "flight_uav_crew_type", typeHandler = FlightUavCrewTypeEnumTypeHandler.class)
    private FlightUavCrewTypeEnum flightUavCrewType;

    /**
     * 重量分类
     */
    @TableField(value = "flight_uav_weight_classfication", typeHandler = FlightUavWeightClassificationEnumTypeHandler.class)
    private FlightUavWeightClassificationEnum flightUavWeightClassfication;

    /**
     * 飞行方式分类
     */
    @TableField(value = "flight_uav_fly_type", typeHandler = FlightUavFlyTypeEnumTypeHandler.class)
    private FlightUavFlyTypeEnum flightUavFlyType;

    /**
     * 最大载物重量(kg)
     */
    @TableField("flight_uav_max_carrier_weight")
    private BigDecimal flightUavMaxCarrierWeight;

    /**
     * 最长飞行时间(分钟)
     */
    @TableField("flight_uav_max_fly_minute")
    private Integer flightUavMaxFlyMinute;

    /**
     * 最大续航里程(公里)
     */
    @TableField("flight_uav_max_fly_range")
    private BigDecimal flightUavMaxFlyRange;

    /**
     * 最大飞行海拔高度(米)
     */
    @TableField("flight_uav_max_fly_height")
    private Integer flightUavMaxFlyHeight;
    
    /**
     * 飞行半径(米)
     */
    @TableField("flight_uav_radius")
    private BigDecimal flightUavRadius;

    /**
     * 是否支持视频拍摄
     */
    @TableField("flight_uav_support_vedio")
    private Boolean flightUavSupportVedio;

    /**
     * 相机像素
     */
    @TableField("flight_uav_camera_pixel")
    private String flightUavCameraPixel;

    /**
     * 应用场景
     */
    @TableField(value = "flight_uav_applied_scenarios", typeHandler = com.deepinnet.skyflow.operationcenter.dal.typehandler.PostgresTextArrayTypeHandler.class)
    private String[] flightUavAppliedScenarios;

    /**
     * 图片列表
     */
    @TableField(value = "flight_uav_pictures", typeHandler = com.deepinnet.skyflow.operationcenter.dal.typehandler.PostgresTextArrayTypeHandler.class)
    private String[] flightUavPictures;

    /**
     * 租户
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;
    
    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
} 