package com.deepinnet.skyflow.operationcenter.dal.repository;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.ScheduleTaskDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.skyflow.operationcenter.dal.model.UpdateTaskCondition;

import java.util.List;

/**
 * <p>
 * 定时任务记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
public interface ScheduleTaskRepository extends IService<ScheduleTaskDO> {

    boolean batchSave(List<ScheduleTaskDO> scheduleTasks);

    List<ScheduleTaskDO> getAllByTypeAndStatusAndTriggerTime(List<String> types, List<String> statusList, Long triggerTime, Integer limitCount);

    boolean updateTaskByCondition(UpdateTaskCondition updateTaskCondition);

}
