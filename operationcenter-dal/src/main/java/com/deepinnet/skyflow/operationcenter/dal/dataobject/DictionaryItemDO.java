package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 字典项表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dictionary_item")
public class DictionaryItemDO extends Model<DictionaryItemDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典ID
     */
    @TableField("dictionary_id")
    private Long dictionaryId;

    /**
     * 字典项编码
     */
    @TableField("code")
    private String code;

    /**
     * 字典项名称
     */
    @TableField("name")
    private String name;

    /**
     * 状态
     */
    @TableField("state")
    private String state;

    /**
     * 父编码
     */
    @TableField("parent_code")
    private String parentCode;

    /**
     * 层级
     */
    @TableField("level")
    private Integer level;

    /**
     * 层级名称
     */
    @TableField("level_name")
    private String levelName;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;
} 