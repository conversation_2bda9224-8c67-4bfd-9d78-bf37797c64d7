package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.DictionaryItemDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.DictionaryItemMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.DictionaryItemRepository;
import org.springframework.stereotype.Repository;

/**
 * 字典项仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class DictionaryItemRepositoryImpl extends ServiceImpl<DictionaryItemMapper, DictionaryItemDO>
        implements DictionaryItemRepository {
} 