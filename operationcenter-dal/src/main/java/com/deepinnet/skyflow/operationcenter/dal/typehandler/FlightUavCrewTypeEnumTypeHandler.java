package com.deepinnet.skyflow.operationcenter.dal.typehandler;

import com.deepinnet.skyflow.operationcenter.enums.FlightUavCrewTypeEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 飞行无人机载人类型枚举类型处理器
 *
 * <AUTHOR>
 */
public class FlightUavCrewTypeEnumTypeHandler extends BaseTypeHandler<FlightUavCrewTypeEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, FlightUavCrewTypeEnum parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setString(i, parameter.getCode());
    }

    @Override
    public FlightUavCrewTypeEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String code = rs.getString(columnName);
        return rs.wasNull() ? null : FlightUavCrewTypeEnum.getByCode(code);
    }

    @Override
    public FlightUavCrewTypeEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String code = rs.getString(columnIndex);
        return rs.wasNull() ? null : FlightUavCrewTypeEnum.getByCode(code);
    }

    @Override
    public FlightUavCrewTypeEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String code = cs.getString(columnIndex);
        return cs.wasNull() ? null : FlightUavCrewTypeEnum.getByCode(code);
    }
} 