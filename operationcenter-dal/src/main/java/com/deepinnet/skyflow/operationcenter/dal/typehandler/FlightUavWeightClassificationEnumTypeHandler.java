package com.deepinnet.skyflow.operationcenter.dal.typehandler;

import com.deepinnet.skyflow.operationcenter.enums.FlightUavWeightClassificationEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 飞行无人机重量分类枚举类型处理器
 *
 * <AUTHOR>
 */
public class FlightUavWeightClassificationEnumTypeHandler extends BaseTypeHandler<FlightUavWeightClassificationEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, FlightUavWeightClassificationEnum parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setString(i, parameter.getCode());
    }

    @Override
    public FlightUavWeightClassificationEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String code = rs.getString(columnName);
        return rs.wasNull() ? null : FlightUavWeightClassificationEnum.getByCode(code);
    }

    @Override
    public FlightUavWeightClassificationEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String code = rs.getString(columnIndex);
        return rs.wasNull() ? null : FlightUavWeightClassificationEnum.getByCode(code);
    }

    @Override
    public FlightUavWeightClassificationEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String code = cs.getString(columnIndex);
        return cs.wasNull() ? null : FlightUavWeightClassificationEnum.getByCode(code);
    }
} 