package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.CategoryDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.CategoryMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.CategoryRepository;
import org.springframework.stereotype.Repository;

/**
 * 类目仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class CategoryRepositoryImpl extends ServiceImpl<CategoryMapper, CategoryDO>
        implements CategoryRepository {
} 