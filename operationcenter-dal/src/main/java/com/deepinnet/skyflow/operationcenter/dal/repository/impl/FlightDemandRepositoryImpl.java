package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDemandMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandRepository;
import org.springframework.stereotype.Repository;

/**
 * 飞行需求仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightDemandRepositoryImpl extends ServiceImpl<FlightDemandMapper, FlightDemandDO>
        implements FlightDemandRepository {
} 