package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.deepinnet.skyflow.operationcenter.dal.typehandler.ArrayTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 类目表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("category")
public class CategoryDO extends Model<CategoryDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类目编码
     */
    @TableField("category_no")
    private String categoryNo;

    /**
     * 类目名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 类目描述
     */
    @TableField("category_description")
    private String categoryDescription;

    /**
     * 父类目
     */
    @TableField("parent_category_no")
    private String parentCategoryNo;

    /**
     * 类目层级
     */
    @TableField("category_level")
    private Integer categoryLevel;

    /**
     * 类型
     */
    @TableField("type")
    private String type;

    /**
     * 类目图片列表
     */
    @TableField(value = "category_picture_list", typeHandler = ArrayTypeHandler.class)
    private String[] categoryPictureList;

    /**
     * 租户
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
} 