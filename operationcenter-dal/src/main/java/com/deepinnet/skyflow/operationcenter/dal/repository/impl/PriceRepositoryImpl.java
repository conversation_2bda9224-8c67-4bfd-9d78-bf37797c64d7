package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.PriceDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.PriceMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.PriceRepository;
import org.springframework.stereotype.Repository;

/**
 * 价格仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class PriceRepositoryImpl extends ServiceImpl<PriceMapper, PriceDO>
        implements PriceRepository {
} 