package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 定时任务记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Getter
@Setter
@TableName("schedule_task")
public class ScheduleTaskDO extends Model<ScheduleTaskDO> {

    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务单号
     */
    @TableField("biz_no")
    private String bizNo;

    /**
     * 任务类型
     */
    @TableField("task_type")
    private String taskType;

    /**
     * 任务的状态
     */
    @TableField("status")
    private String status;

    /**
     * 任务来源于哪个应用
     */
    @TableField("source")
    private String source;

    /**
     * 当前的环境
     */
    @TableField("env")
    private String env;

    /**
     * 任务实际被触发的时间
     */
    @TableField("trigger_time")
    private Long triggerTime;

    /**
     * 实际执行任务的时间
     */
    @TableField("execute_time")
    private Long executeTime;

    /**
     * 任务重试的次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 租户 id
     */
    @TableField("tenant_id")
    private String tenantId;
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
