package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightOrderDao;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
public class FlightOrderRepositoryImpl extends ServiceImpl<FlightOrderDao, FlightOrderDO> implements FlightOrderRepository {

}
