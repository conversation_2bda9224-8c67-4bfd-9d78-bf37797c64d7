package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderFlyingInfoDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightOrderFlyingInfoDao;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderFlyingInfoRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Service
public class FlightOrderFlyingInfoRepositoryImpl extends ServiceImpl<FlightOrderFlyingInfoDao, FlightOrderFlyingInfoDO> implements FlightOrderFlyingInfoRepository {

}
