package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightEventsDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightEventsMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightEventsRepository;
import org.springframework.stereotype.Repository;

/**
 * 飞行事件记录仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightEventsRepositoryImpl extends ServiceImpl<FlightEventsMapper, FlightEventsDO>
        implements FlightEventsRepository {
} 