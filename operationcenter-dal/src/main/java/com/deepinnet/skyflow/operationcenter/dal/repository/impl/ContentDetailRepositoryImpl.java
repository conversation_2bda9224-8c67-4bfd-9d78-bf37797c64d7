package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ContentDetailDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.ContentDetailMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.ContentDetailRepository;
import org.springframework.stereotype.Repository;

/**
 * 首页内容仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class ContentDetailRepositoryImpl extends ServiceImpl<ContentDetailMapper, ContentDetailDO>
        implements ContentDetailRepository {
} 