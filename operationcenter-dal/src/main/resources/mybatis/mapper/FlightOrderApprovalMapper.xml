<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.FlightOrderApprovalDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderApprovalDO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="approval_status" property="approvalStatus" />
        <result column="approval_user_no" property="approvalUserNo" />
        <result column="approval_user_ name" property="approvalUserName" />
        <result column="approval_time" property="approvalTime" />
        <result column="remark" property="remark" />
        <result column="tenant_id" property="tenantId" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, approval_status, approval_user_no, approval_user_name, approval_time, remark, tenant_id, is_deleted, gmt_created, gmt_modified
    </sql>

</mapper>
