<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.FlightOrderProductUsageDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderProductUsageDO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="product_no" property="productNo" />
        <result column="product_type" property="productType" />
        <result column="uav_model" property="uavModel" />
        <result column="total_quantity" property="totalQuantity" />
        <result column="use_quantity" property="useQuantity" />
        <result column="validity_period_type" property="validityPeriodType" />
        <result column="validity_period_start" property="validityPeriodStart" />
        <result column="validity_period_end" property="validityPeriodEnd" />
        <result column="count" property="count" />
        <result column="price" property="price" />
        <result column="base_price" property="basePrice" />
        <result column="tenant_id" property="tenantId" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, product_no, product_type, uav_model, total_quantity, use_quantity, validity_period_type, validity_period_start, validity_period_end, count, price, base_price, tenant_id, gmt_created, gmt_modified
    </sql>

</mapper>
