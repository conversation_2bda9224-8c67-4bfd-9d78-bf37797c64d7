<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.FlightPlanDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO">
        <id column="id" property="id" />
        <result column="mission_id" property="missionId" />
        <result column="plan_id" property="planId" />
        <result column="uav_id" property="uavId" />
        <result column="status" property="status" />
        <result column="planed_takeoff_time" property="planedTakeoffTime" />
        <result column="planed_landing_time" property="planedLandingTime" />
        <result column="operator_user" property="operatorUser" />
        <result column="operator_phone" property="operatorPhone" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="landing_aerodrome_id" property="landingAerodromeId" />
        <result column="arrival_aerodrome_id" property="arrivalAerodromeId" />
        <result column="alternate_aerodrome_id" property="alternateAerodromeId" />
        <result column="planed_altitude" property="planedAltitude" />
        <result column="real_ame_verification" property="realAmeVerification" />
        <result column="flight_unit" property="flightUnit" />
        <result column="flight_unit_id" property="flightUnitId" />
        <result column="apply_user_name" property="applyUserName" />
        <result column="airspace_id" property="airspaceId" />
        <result column="scence_code" property="scenceCode" />
        <result column="biz_no" property="bizNo" />
        <result column="tenant_id" property="tenantId" />
        <result column="related_id" property="relatedId" />
        <result column="apply_type" property="applyType" />
        <result column="out_plan_no" property="outPlanNo" />
        <result column="plan_name" property="planName" />
        <result column="requirement_name" property="requirementName" />
        <result column="uav_model" property="uavModel" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, mission_id, plan_id, uav_id, `status`, planed_takeoff_time, planed_landing_time, operator_user, operator_phone, gmt_created, gmt_modified, landing_aerodrome_id, arrival_aerodrome_id, alternate_aerodrome_id, planed_altitude, real_ame_verification, flight_unit, flight_unit_id, apply_user_name, airspace_id, scence_code, biz_no, tenant_id, related_id, apply_type, out_plan_no, plan_name, requirement_name, uav_model
    </sql>

    <sql id="page_column_list">
        fp.id, fp.mission_id, fp.plan_id, fp.uav_id, fp.status, fp.planed_takeoff_time, fp.planed_landing_time, fp.operator_user, fp.operator_phone, fp.gmt_created, fp.gmt_modified, fp.landing_aerodrome_id, fp.arrival_aerodrome_id, fp.alternate_aerodrome_id, fp.planed_altitude, fp.real_ame_verification, fp.flight_unit, fp.flight_unit_id, fp.apply_user_name, fp.airspace_id, fp.scence_code, fp.biz_no, fp.tenant_id, fp.related_id, fp.apply_type, fp.out_plan_no, fp.plan_name, fp.requirement_name, fp.uav_model
    </sql>
    <select id="getFlightPlanListByPage"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO">
        SELECT
            <include refid="page_column_list" />
        FROM
            flight_plan fp
        <if test="(planQuery.userNo != null and planQuery.userNo.size() > 0)
                  or (planQuery.orderNo != null and planQuery.orderNo != '')
                  or (planQuery.startTime != null and planQuery.endTime != null)
                  or (planQuery.requirementType != null and planQuery.requirementType != '')">
            LEFT JOIN flight_demand fd ON fp.biz_no = fd.demand_no
        </if>
        <where>
            <if test="planQuery.userNo != null and planQuery.userNo.size() > 0">
                fd.publisher_no in
                <foreach collection="planQuery.userNo" item="userNo" open="(" separator="," close=")">
                    #{userNo}
                </foreach>
            </if>
            <if test="planQuery.orderNo != null and planQuery.orderNo != ''">
                AND fd.flight_order_no = #{planQuery.orderNo}
            </if>
            <if test="planQuery.flightUnitId != null and planQuery.flightUnitId != ''">
                AND fp.flight_unit_id = #{planQuery.flightUnitId}
            </if>
            <if test="planQuery.flightUNitName != null and planQuery.flightUnitName != ''">
                AND fp.flight_unit like CONCAT('%', #{planQuery.flightUnitName}, '%')
            </if>
            <if test="planQuery.requirementType != null and planQuery.requirementType != ''">
                AND fd.type = #{planQuery.requirementType}
            </if>
            <if test="planQuery.startTime != null and planQuery.endTime != null">
                AND (fd.publish_time &gt;= #{planQuery.startTime} AND fd.publish_time &lt;= #{planQuery.endTime})
            </if>
            <if test="planQuery.planName != null and planQuery.planName != ''">
                AND fp.plan_name like CONCAT('%', #{planQuery.planName}, '%')
            </if>
            <if test="planQuery.requirementId != null and planQuery.requirementId != ''">
                AND fp.biz_no = #{planQuery.requirementId}
            </if>
            <if test="planQuery.requirementName != null and planQuery.requirementName != ''">
                AND fp.requirement_name like CONCAT('%', #{planQuery.requirementName}, '%')
            </if>
            <if test="planQuery.uavModel != null and planQuery.uavModel != ''">
                AND fp.uav_model like CONCAT('%', #{planQuery.uavModel}, '%')
            </if>
        </where>
        ORDER BY fp.planed_takeoff_time DESC, fp.id desc
    </select>

</mapper>
