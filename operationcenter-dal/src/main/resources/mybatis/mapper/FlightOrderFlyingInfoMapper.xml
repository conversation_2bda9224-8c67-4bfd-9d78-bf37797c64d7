<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.FlightOrderFlyingInfoDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderFlyingInfoDO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="flying_frequency" property="flyingFrequency" />
        <result column="flying_num" property="flyingNum" />
        <result column="except_flying_num" property="exceptFlyingNum" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="validity_period_start" property="validityPeriodStart" />
        <result column="validity_period_end" property="validityPeriodEnd" />
        <result column="flying_area" property="flyingArea" />
        <result column="remark" property="remark" />
        <result column="is_deleted" property="isDeleted" />
        <result column="tenant_id" property="tenantId" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="apply_scenarios" property="applyScenarios" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, flying_frequency, flying_num, except_flying_num, start_time, end_time, validity_period_start, validity_period_end, flying_area, remark, is_deleted, tenant_id, gmt_created, gmt_modified, apply_scenarios
    </sql>

</mapper>
