<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.ScheduleTaskDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.skyflow.operationcenter.dal.dataobject.ScheduleTaskDO">
        <id column="id" property="id" />
        <result column="biz_no" property="bizNo" />
        <result column="task_type" property="taskType" />
        <result column="status" property="status" />
        <result column="source" property="source" />
        <result column="env" property="env" />
        <result column="trigger_time" property="triggerTime" />
        <result column="execute_time" property="executeTime" />
        <result column="retry_count" property="retryCount" />
        <result column="tenant_id" property="tenantId" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, biz_no, task_type, status, source, env, trigger_time, execute_time, retry_count, tenant_id, gmt_create, gmt_modified
    </sql>

</mapper>
