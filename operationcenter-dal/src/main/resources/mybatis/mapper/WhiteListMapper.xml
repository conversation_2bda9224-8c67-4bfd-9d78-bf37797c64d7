<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.WhiteListMapper">

    <!-- 基础列 -->
    <sql id="baseColumns">
        id, customer_user_no, supplier_user_no, customer_user_name, supplier_user_name, 
        is_deleted, gmt_created, gmt_modified, tenant_id
    </sql>

    <!-- 查询条件 -->
    <sql id="whereClause">
        <where>
            <if test="customerUserName != null and customerUserName != ''">
                AND customer_user_name LIKE CONCAT('%', #{customerUserName}, '%')
            </if>
            <if test="supplierUserName != null and supplierUserName != ''">
                AND supplier_user_name LIKE CONCAT('%', #{supplierUserName}, '%')
            </if>
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
            AND is_deleted = false
        </where>
    </sql>

    <!-- 按客户分组查询白名单分页数据 -->
    <select id="findDistinctCustomersWithPaging" resultType="java.util.Map">
        SELECT 
            customer_user_no, 
            customer_user_name, 
            tenant_id, 
            COUNT(*) as total_suppliers
        FROM white_list
        <include refid="whereClause" />
        GROUP BY customer_user_no, customer_user_name, tenant_id
        ORDER BY MIN(gmt_created) DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 按客户分组统计白名单总数 -->
    <select id="countDistinctCustomers" resultType="long">
        SELECT 
            COUNT(*)
        FROM (
            SELECT 
                customer_user_no
            FROM white_list
            <include refid="whereClause" />
            GROUP BY customer_user_no
        ) t
    </select>

</mapper> 