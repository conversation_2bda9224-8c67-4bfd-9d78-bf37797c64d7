package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 * Date: 2025/4/16
 * Author: lijunheng
 */
@Data
@ApiModel(description = "物流信息")
public class LogisticsInfo implements Serializable {

    @ApiModelProperty(value = "收取货地址")
    private String address;

    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "联系人")
    private String contact;

    @ApiModelProperty(value = "联系方式")
    private String phone;
}
