package com.deepinnet.skyflow.operationcenter.vo;

import com.deepinnet.skyflow.operationcenter.enums.ValidityPeriodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 价格视图对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "价格信息")
public class PriceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "价格ID")
    private Integer id;

    @ApiModelProperty(value = "关联产品编号")
    private String productNo;

    @ApiModelProperty(value = "基础价格")
    private BigDecimal basePrice;

    @ApiModelProperty(value = "计费单位")
    private String billingUnit;

    @ApiModelProperty(value = "有效期")
    private ValidityPeriodEnum validityPeriod;

    @ApiModelProperty(value = "有效次数")
    private Integer validityTimes;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

} 