package com.deepinnet.skyflow.operationcenter.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 类目类型
 *
 * <AUTHOR>
 */
@Getter
public enum CategoryTypeEnum {

    /**
     * 飞行方式
     */
    FLIGHT_UAV_FLY_TYPE("FLIGHT_UAV_FLY_TYPE", "飞行方式"),

    /**
     * 飞行产品
     */
    FLIGHT("FLIGHT", "飞行产品");

    /**
     * 编码
     */
    @EnumValue
    @JsonValue
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    CategoryTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static CategoryTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (CategoryTypeEnum item : CategoryTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        
        return null;
    }
} 