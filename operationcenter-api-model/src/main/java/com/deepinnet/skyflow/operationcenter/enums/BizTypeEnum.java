package com.deepinnet.skyflow.operationcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum BizTypeEnum {
    FLIGHT_UAV_STATION("FLIGHT_UAV_STATION", "机巢"),
    FLIGHT_UAV_BM("FLIGHT_UAV_BM", "无人机机型"),
    FLIGHT_UAV_BM_IMPORT("FLIGHT_UAV_BM_IMPORT", "飞行无人机品牌型号导入批次号"),
    FLIGHT_UAV("FLIGHT_UAV", "无人机"),
    CATEGORY("CATEGORY", "类目"),
    PRODUCT("PRODUCT", "产品"),
    PRICE("PRICE", "定价"),
    ;

    private final String type;
    private final String desc;
} 