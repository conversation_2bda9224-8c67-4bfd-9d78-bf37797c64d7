package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 飞行无人机品牌型号导入请求参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行无人机品牌型号导入请求参数")
public class FlightUavBmImportRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "导入批次号", hidden = true)
    private String importBatchNo;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private String tenantId;

    @ApiModelProperty(value = "创建人", hidden = true)
    private String creator;
} 