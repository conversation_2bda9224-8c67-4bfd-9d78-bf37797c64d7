package com.deepinnet.skyflow.operationcenter.client.task;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.FlightAlgorithmMonitorCreateDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightTaskNotifyDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/24
 */
@FeignClient(name = "flightTaskNotifyClient", url = "${sfoc.service.url}")
public interface FlightTaskNotifyClient {

    @PostMapping("/task/notify/start")
    @ApiOperation("开始飞行任务")
    Result<Boolean> startFlightTask(@RequestBody @Valid FlightAlgorithmMonitorCreateDTO monitorCreateDTO);

    @PostMapping("/task/notify/end")
    @ApiOperation("通知结束飞行任务")
    Result<Boolean> endFlightTask(@RequestBody @Valid FlightTaskNotifyDTO flightTaskNotifyDTO);

}
