package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Description:
 * Date: 2025/4/16
 * Author: lijunheng
 */
@Data
@ApiModel(description = "物流运输飞行需求信息")
public class LogisticsTransportationFlightDemandDTO {

    @ApiModelProperty(value = "取货时间")
    private String pickUpTime;

    @ApiModelProperty(value = "取货信息")
    private LogisticsInfo pickUpInfo;

    @ApiModelProperty(value = "收获信息")
    private LogisticsInfo receiveInfo;
}
