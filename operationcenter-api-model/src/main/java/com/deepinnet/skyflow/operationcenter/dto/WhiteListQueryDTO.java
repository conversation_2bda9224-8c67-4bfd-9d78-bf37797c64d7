package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 白名单查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "白名单查询参数")
public class WhiteListQueryDTO extends PageQueryDTO {

    @ApiModelProperty(value = "客户编号")
    private String customerUserNo;

    @ApiModelProperty(value = "服务商编号")
    private String supplierUserNo;

    @ApiModelProperty(value = "客户名称")
    private String customerUserName;

    @ApiModelProperty(value = "服务商名称")
    private String supplierUserName;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 