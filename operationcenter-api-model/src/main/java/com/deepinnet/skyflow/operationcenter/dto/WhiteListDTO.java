package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 白名单DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "白名单数据传输对象")
public class WhiteListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "客户编号", required = true)
    @NotBlank(message = "客户编号不能为空")
    private String customerUserNo;

    @ApiModelProperty(value = "服务商编号", required = true)
    @NotBlank(message = "服务商编号不能为空")
    private String supplierUserNo;

    @ApiModelProperty(value = "客户名称")
    private String customerUserName;

    @ApiModelProperty(value = "服务商名称")
    private String supplierUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreated;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 