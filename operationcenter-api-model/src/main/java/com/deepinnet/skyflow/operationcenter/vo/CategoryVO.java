package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类目视图对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "类目信息")
public class CategoryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类目ID")
    private Integer id;

    @ApiModelProperty(value = "类目编码")
    private String categoryNo;

    @ApiModelProperty(value = "类目名称")
    private String categoryName;

    @ApiModelProperty(value = "类目描述")
    private String categoryDescription;

    @ApiModelProperty(value = "父类目编码")
    private String parentCategoryNo;

    @ApiModelProperty(value = "类目层级")
    private Integer categoryLevel;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "类目图片列表")
    private String[] categoryPictureList;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime modifyTime;
} 