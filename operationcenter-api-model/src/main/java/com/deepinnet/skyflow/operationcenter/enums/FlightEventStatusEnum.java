package com.deepinnet.skyflow.operationcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 * Date: 2025/5/9
 * Author: lijunheng
 */
@AllArgsConstructor
@Getter
public enum FlightEventStatusEnum {
    UNSTART("unstart", "未开始"),
    RUNNING("running", "运行中"),
    CANCEL("cancel", "已取消"),
    STOPPING("stopping", "停止中"),
    DONE("done", "已结束"),
    FAIL("fail", "已失败");

    private final String code;
    private final String desc;
}
