package com.deepinnet.skyflow.operationcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/18
 */

@Getter
@AllArgsConstructor
public enum FlightOrderApproveStatusEnum {

    /**
     * 提交
     */
    SUBMIT("SUBMIT", "提交"),

    /**
     * 审核中
     */
    APPROVING("APPROVING", "审核中"),
    /**
     * 审核通过
     */
    APPROVED("APPROVED", "审核通过"),
    /**
     * 审核不通过
     */
    REJECTED("REJECTED", "审核不通过"),
    ;


    private final String statusCode;

    private final String statusName;

    public static FlightOrderApproveStatusEnum getEnumByStatusName(String statusCode) {
        for (FlightOrderApproveStatusEnum statusEnum : FlightOrderApproveStatusEnum.values()) {
            if (statusEnum.getStatusCode().equals(statusCode)) {
                return statusEnum;
            }
        }
        return null;
    }
}
