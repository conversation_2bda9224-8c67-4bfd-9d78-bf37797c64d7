package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.skyflow.operationcenter.enums.FlightEventStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightEventTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 飞行事件记录DTO
 *
 * <AUTHOR>
 */
@Data
public class FlightEventsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 事件唯一标识
     */
    private String id;

    /**
     * 事件类型，例如违停、闯入禁区等
     */
    private FlightEventTypeEnum eventType;

    /**
     * 事件发生时间
     */
    private LocalDateTime eventTime;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件描述，详细说明事件内容
     */
    private String description;

    /**
     * 事件发生地点的经纬度坐标，WGS84坐标系
     */
    private PointCoordinate eventPoint;

    /**
     * 事件发生地点的文字描述（如：某某街口）
     */
    private String eventLocation;

    /**
     * 事件持续时长，单位：分钟
     */
    private Integer duration;

    /**
     * 事件状态，例如持续中、已结束
     */
    private FlightEventStatusEnum status;

    /**
     * 涉事车辆的车牌号
     */
    private String licensePlate;

    /**
     * 取证图片的 JSON 数组，包含图片地址和描述
     */
    private String evidenceImages;

    /**
     * 取证视频的 JSON 数组，包含视频地址和描述
     */
    private String evidenceVideos;

    /**
     * 飞行任务id
     */
    private String flightTaskCode;

    /**
     * 租户id
     */
    private String tenantId;
} 