package com.deepinnet.skyflow.operationcenter.vo;

import lombok.*;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/16
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FlightOrderApprovalVO implements Serializable {

    private final static long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 审核状态
     */
    private String approvalStatus;

    /**
     * 审核人编码
     */
    private String approvalUserNo;

    /**
     * 审核人名称
     */
    private String approvalUserName;

    /**
     * 审核时间
     */
    private Long approvalTime;

    /**
     * 审核备注
     */
    private String remark;

}
