package com.deepinnet.skyflow.operationcenter.client.demand;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/24
 */
@FeignClient(name = "flightDemandClient", url = "${sfoc.service.url}")
public interface FlightDemandClient {

    @PostMapping("/demand/create")
    Result<String> createFlightDemand(@RequestBody @Valid FlightDemandDTO flightDemandDTO);

    @GetMapping("/demand/customer/get")
    Result<FlightDemandVO> getFlightDemandByNo(@RequestParam(value = "demandNo") @NotBlank(message = "需求编号不能为空")
                                                      @ApiParam(value = "需求编号", required = true) String demandNo);
}
