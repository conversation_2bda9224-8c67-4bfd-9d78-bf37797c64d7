package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/14
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FlightOrderFileVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty(value = "文件ID")
    private Long id;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /**
     * 附件地址
     */
    @ApiModelProperty(value = "附件地址")
    private String filePath;

}
