package com.deepinnet.skyflow.operationcenter.dto.openapi;

import com.deepinnet.skyflow.operationcenter.common.constants.OpenApiConstant;
import com.deepinnet.skyflow.operationcenter.common.request.HttpApiRequest;
import com.deepinnet.skyflow.operationcenter.common.request.UrlParams;
import lombok.Data;

/**
 * 逆地理编码请求
 * 高德开放平台逆地理编码：https://lbs.amap.com/api/webservice/guide/api/georegeo
 *
 * <AUTHOR>
 */
@Data
public class ReverseGeoCodingRequest implements HttpApiRequest {

    private ReverseGeocodingParam urlParams;


    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        String location = urlParams.getLocation();
        String key = urlParams.getKey();
        return String.format(OpenApiConstant.REVERSE_GEOCODING_URL, location, key);
    }
} 