package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 撮合订单查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "撮合订单查询条件")
public class MatchOrderQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 撮合订单编号
     */
    @ApiModelProperty(value = "撮合订单编号")
    private String matchOrderNo;

    /**
     * 飞行产品编号
     */
    @ApiModelProperty(value = "飞行产品编号")
    private String flightProductNo;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 撮合状态
     */
    @ApiModelProperty(value = "撮合状态")
    private String matchStatus;

    /**
     * 服务提供商编号
     */
    @ApiModelProperty(value = "服务提供商编号")
    private String serviceProviderNo;

    /**
     * 飞行需求编号
     */
    @ApiModelProperty(value = "飞行需求编号")
    private String flightDemandNo;

    /**
     * 同步订单编号
     */
    @ApiModelProperty(value = "同步订单编号")
    private String syncOrderNo;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 