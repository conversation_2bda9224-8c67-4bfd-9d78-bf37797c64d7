package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.ValidityPeriodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 价格查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "价格查询参数")
public class PriceQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关联产品编号")
    private String productNo;

    @ApiModelProperty(value = "计费单位")
    private String billingUnit;
    
    @ApiModelProperty(value = "有效期")
    private ValidityPeriodEnum validityPeriod;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 