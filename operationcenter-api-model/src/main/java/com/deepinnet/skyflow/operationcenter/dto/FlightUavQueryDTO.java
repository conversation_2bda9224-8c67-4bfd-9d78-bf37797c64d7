package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 飞行无人机查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "飞行无人机查询参数")
public class FlightUavQueryDTO extends PageQueryDTO {

    @ApiModelProperty(value = "无人机编号")
    private String flightUavNo;
    
    @ApiModelProperty(value = "无人机编号列表")
    private List<String> flightUavNoList;
    
    @ApiModelProperty(value = "飞行器名称")
    private String flightUavName;

    @ApiModelProperty(value = "归属机巢")
    private String flightStationNo;

    @ApiModelProperty(value = "SN码")
    private String flightUavSn;

    @ApiModelProperty(value = "机型编号")
    private String flightUavBmNo;
    
    @ApiModelProperty(value = "服务商编号")
    private String supplierUserNo;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 