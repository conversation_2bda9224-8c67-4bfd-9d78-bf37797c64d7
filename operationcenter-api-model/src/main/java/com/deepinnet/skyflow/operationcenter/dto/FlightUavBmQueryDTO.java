package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightUavCrewTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavFlyTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavWeightClassificationEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 飞行无人机品牌型号查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "飞行无人机品牌型号查询参数")
public class FlightUavBmQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "系统内部型号编码（系统自动生成的唯一标识符）")
    private String flightUavBmNo;

    @ApiModelProperty(value = "系统内部型号编码列表")
    private List<String> flightUavBmNoList;

    @ApiModelProperty(value = "品牌名")
    private String flightUavBmName;

    @ApiModelProperty(value = "第三方厂商的机器型号编号（厂商原始出厂型号）")
    private String flightUavBmModelNo;

    @ApiModelProperty(value = "载人类型")
    private FlightUavCrewTypeEnum flightUavCrewType;

    @ApiModelProperty(value = "重量分类")
    private FlightUavWeightClassificationEnum flightUavWeightClassfication;

    @ApiModelProperty(value = "飞行方式分类")
    private FlightUavFlyTypeEnum flightUavFlyType;
    
    @ApiModelProperty(value = "最小飞行半径(米)")
    private BigDecimal minFlightUavRadius;
    
    @ApiModelProperty(value = "最大飞行半径(米)")
    private BigDecimal maxFlightUavRadius;

    @ApiModelProperty(value = "是否支持视频拍摄")
    private Boolean flightUavSupportVedio;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 