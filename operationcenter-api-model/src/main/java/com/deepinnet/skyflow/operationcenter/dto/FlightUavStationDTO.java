package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightUavStationStatusEnum;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavBmVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 飞行无人机站点传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行无人机站点传输对象")
public class FlightUavStationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "站点ID")
    private Integer id;

    @ApiModelProperty(value = "机巢编号", required = true)
    private String flightStationNo;

    @ApiModelProperty(value = "服务商编号")
    private String companyUserNo;

    @ApiModelProperty(value = "机巢位置坐标（格式：\"x,y\"）")
    private String coordinate;

    @ApiModelProperty(value = "可用无人机数量")
    private Integer availableQuantity;

    @ApiModelProperty(value = "站点覆盖半径(米)")
    private BigDecimal radius;
    
    @ApiModelProperty(value = "关联的飞行器编号")
    private String flightUavNo;

    @ApiModelProperty(value = "站点名称")
    private String name;

    @ApiModelProperty(value = "省份")
    private String province;
    
    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    @ApiModelProperty(value = "城市")
    private String city;
    
    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "区县")
    private String district;
    
    @ApiModelProperty(value = "区县编码")
    private String districtCode;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "站点状态：NORMAL-正常，MAINTENANCE-维护，FAULT-故障，IDLE-闲置")
    private FlightUavStationStatusEnum status;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
    
    @ApiModelProperty(value = "飞行器信息")
    private FlightUavVO flightUav;
    
    @ApiModelProperty(value = "飞行器机型信息")
    private FlightUavBmVO flightUavBm;
} 