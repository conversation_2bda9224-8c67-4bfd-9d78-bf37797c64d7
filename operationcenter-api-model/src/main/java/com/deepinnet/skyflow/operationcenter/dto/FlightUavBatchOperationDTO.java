package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 飞行无人机批量操作传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行无人机批量操作传输对象")
public class FlightUavBatchOperationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 要删除的飞行无人机ID列表
     */
    @ApiModelProperty(value = "要删除的飞行无人机ID列表")
    private List<Integer> deleteIds;

    /**
     * 要新增的飞行无人机列表
     */
    @ApiModelProperty(value = "要新增的飞行无人机列表")
    private List<FlightUavDTO> createItems;
    
    /**
     * 要更新的飞行无人机列表
     */
    @ApiModelProperty(value = "要更新的飞行无人机列表")
    private List<FlightUavDTO> updateItems;

    /**
     * 服务商编号
     */
    @NotNull(message = "服务商编号不能为空")
    @ApiModelProperty(value = "服务商编号", required = true)
    private String supplierUserNo;
} 