package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 分页查询参数传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "分页查询参数")
public class PageQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于等于1")
    @ApiModelProperty(value = "页码", required = true, example = "1")
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    @NotNull(message = "每页数量不能为空")
    @Min(value = 1, message = "每页数量必须大于等于1")
    @ApiModelProperty(value = "每页数量", required = true, example = "10")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private String tenantId;
} 