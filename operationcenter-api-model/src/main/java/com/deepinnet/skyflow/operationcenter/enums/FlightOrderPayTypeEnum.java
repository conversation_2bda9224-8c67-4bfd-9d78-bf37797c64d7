package com.deepinnet.skyflow.operationcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/18
 */

@Getter
@AllArgsConstructor
public enum FlightOrderPayTypeEnum {

    /**
     * 月结
     */
    MONTHLY_PAY("MONTHLY_PAY", "月结"),

    /**
     * 立即支付
     */
    IMMEDIATE_PAY("IMMEDIATE_PAY", "立即支付");

    private final String payType;

    private final String payTypeName;
}
