package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 白名单分组展示对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "白名单分组展示对象")
public class WhiteListGroupVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerUserNo;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerUserName;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 服务商白名单列表
     */
    @ApiModelProperty(value = "服务商白名单列表")
    private List<WhiteListVO> whiteListItems;

    /**
     * 服务商总数
     */
    @ApiModelProperty(value = "服务商总数")
    private Integer totalSuppliers;
} 