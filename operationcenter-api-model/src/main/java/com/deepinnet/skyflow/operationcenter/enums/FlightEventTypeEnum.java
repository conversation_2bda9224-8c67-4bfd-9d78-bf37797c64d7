package com.deepinnet.skyflow.operationcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 * Date: 2025/5/9
 * Author: lijunheng
 */
@AllArgsConstructor
@Getter
public enum FlightEventTypeEnum {

    //车辆违停、道路积水、道路有抛撒物品、闯红灯、压实线、违法掉头
    ILLEGAL_PARKING("illegal_parking", "车辆违停"),
    ROAD_WATER("road_water", "道路积水"),
    ROAD_OBJECT("road_object", "道路有抛撒物品"),
    CROSS_RED_LIGHT("cross_red_light", "闯红灯"),
    COMPACTION_LINE("compaction_line", "压实线"),
    ILLEGAL_TURN("illegal_turn", "违法掉头");

    private final String code;

    private final String desc;
}
