package com.deepinnet.skyflow.operationcenter.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 飞行无人机重量分类枚举
 *
 * <AUTHOR>
 */
@Getter
public enum FlightUavWeightClassificationEnum {
    
    /**
     * 微型
     */
    MICRO("MICRO", "微型"),
    
    /**
     * 轻型
     */
    LIGHT("LIGHT", "轻型"),
    
    /**
     * 小型
     */
    SMALL("SMALL", "小型"),
    
    /**
     * 中型
     */
    MEDIUM("MEDIUM", "中型"),
    
    /**
     * 大型
     */
    LARGE("LARGE", "大型");
    
    /**
     * 编码
     */
    @EnumValue
    @JsonValue
    private final String code;
    
    /**
     * 描述
     */
    private final String desc;
    
    FlightUavWeightClassificationEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static FlightUavWeightClassificationEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (FlightUavWeightClassificationEnum item : FlightUavWeightClassificationEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        
        return null;
    }
} 