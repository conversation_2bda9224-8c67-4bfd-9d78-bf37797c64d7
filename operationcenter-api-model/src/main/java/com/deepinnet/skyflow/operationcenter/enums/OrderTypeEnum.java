package com.deepinnet.skyflow.operationcenter.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/13
 */

@Getter
@AllArgsConstructor
public enum OrderTypeEnum {
    NORMAL("NORMAL", "普通订单"),

    DEMAND_PLAN("DEMAND_PLAN", "需求计划");
    
    private final String code;
    
    private final String name;
    
    
    public static OrderTypeEnum getEnumByCode(String code) {
        for (OrderTypeEnum orderTypeEnum : OrderTypeEnum.values()) {
            if (StrUtil.equals(code, orderTypeEnum.getCode())) {
                return orderTypeEnum;
            }
        }

        return null;
    }
    
}
