package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightUavCrewTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavFlyTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavWeightClassificationEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 飞行无人机品牌型号查询参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行无人机品牌型号查询参数")
public class FlightUavBmBaseDTO  {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

} 