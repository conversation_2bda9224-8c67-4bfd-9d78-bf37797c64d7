package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 站点距离查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "站点距离查询参数")
public class StationDistanceQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "区域多边形WKT字符串")
    private String polygonWkt;

    @ApiModelProperty(value = "站点状态")
    private String stationStatus;

    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "区县编码")
    private String districtCode;
    
    @ApiModelProperty(value = "飞行器型号编号(关联flight_uav_bm表的flight_uav_bm_model_no字段)")
    private String flightUavBmModelNo;
    
    @ApiModelProperty(value = "飞行器型号编号列表(关联FlightUav表的FlightUavBmNo字段)")
    private List<String> flightUavBmList;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 