package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/20
 */

@Data
public class FlightOrderProductUsageDTO implements Serializable {

    private final static long serialVersionUID = 1L;

    @ApiModelProperty(value = "需求编号")
    private String requirementNo;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

}
