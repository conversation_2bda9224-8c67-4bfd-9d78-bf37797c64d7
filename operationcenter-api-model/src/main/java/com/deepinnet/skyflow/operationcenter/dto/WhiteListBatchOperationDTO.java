package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 白名单批量操作传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "白名单批量操作传输对象")
public class WhiteListBatchOperationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 要删除的白名单ID列表
     */
    @ApiModelProperty(value = "要删除的白名单ID列表")
    private List<Integer> deleteIds;

    /**
     * 要新增的白名单列表
     */
    @ApiModelProperty(value = "要新增的白名单列表")
    private List<WhiteListDTO> createItems;
    
    /**
     * 要更新的白名单列表
     */
    @ApiModelProperty(value = "要更新的白名单列表")
    private List<WhiteListDTO> updateItems;
} 