package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 飞行需求与计划关联信息DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行需求与计划关联信息")
public class FlightDemandPlanRelationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 飞行需求编号
     */
    @ApiModelProperty(value = "飞行需求编号", required = true)
    @NotBlank(message = "飞行需求编号不能为空")
    private String flightDemandCode;

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID", required = true)
    @NotBlank(message = "计划ID不能为空")
    private String planId;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 