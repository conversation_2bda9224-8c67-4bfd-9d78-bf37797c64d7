package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 站点距离VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "按距离排序的站点视图对象")
public class StationDistanceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "站点ID")
    private Integer id;

    @ApiModelProperty(value = "站点编号")
    private String stationNo;

    @ApiModelProperty(value = "站点名称")
    private String stationName;

    @ApiModelProperty(value = "站点状态")
    private String stationStatus;

    @ApiModelProperty(value = "站点地址")
    private String stationAddress;

    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "区县编码")
    private String districtCode;

    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    @ApiModelProperty(value = "距离（米）")
    private Double distance;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
    
    @ApiModelProperty(value = "关联的飞行器编号")
    private String flightUavNo;
    
    @ApiModelProperty(value = "飞行器信息")
    private FlightUavVO flightUav;
    
    @ApiModelProperty(value = "飞行器机型信息")
    private FlightUavBmVO flightUavBm;
} 