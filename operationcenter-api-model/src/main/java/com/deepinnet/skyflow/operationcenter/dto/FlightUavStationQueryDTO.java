package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightUavStationStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavFlyTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 飞行无人机站点查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "飞行无人机站点查询参数")
public class FlightUavStationQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机巢编号")
    private String flightStationNo;

    @ApiModelProperty(value = "机巢名称")
    private String name;

    @ApiModelProperty(value = "服务商编号")
    private String companyUserNo;
    
    @ApiModelProperty(value = "最小覆盖半径")
    private Integer minRadius;
    
    @ApiModelProperty(value = "最大覆盖半径")
    private Integer maxRadius;
    
    @ApiModelProperty(value = "关联的飞行器编号")
    private String flightUavNo;
    
    @ApiModelProperty(value = "飞行器SN码")
    private String flightUavSn;
    
    @ApiModelProperty(value = "省份编码")
    private String provinceCode;
    
    @ApiModelProperty(value = "城市编码")
    private String cityCode;
    
    @ApiModelProperty(value = "区县编码")
    private String districtCode;
    
    @ApiModelProperty(value = "站点状态")
    private FlightUavStationStatusEnum status;

    @ApiModelProperty(value = "飞行器型号编号列表，关联FlightUav表的FlightUavBmNo字段")
    private List<String> flightUavBmList;

    @ApiModelProperty(value = "飞行器飞行方式类型，关联FlightUavBm表的FlightUavFlyType字段")
    private FlightUavFlyTypeEnum flightUavFlyType;
    
    @ApiModelProperty(value = "飞行器机型型号，关联FlightUavBm表的FlightUavBmModelNo字段")
    private String flightUavBmModelNo;
    
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 