package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 白名单按客户批量覆盖传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "白名单按客户批量覆盖传输对象")
public class WhiteListBatchReplaceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户编号
     */
    @NotBlank(message = "客户编号不能为空")
    @ApiModelProperty(value = "客户编号", required = true)
    private String customerUserNo;
    
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerUserName;
    
    /**
     * 要新增的白名单列表
     */
    @NotNull(message = "白名单列表不能为空")
    @ApiModelProperty(value = "要新增的白名单列表", required = true)
    private List<WhiteListDTO> whiteListItems;
} 