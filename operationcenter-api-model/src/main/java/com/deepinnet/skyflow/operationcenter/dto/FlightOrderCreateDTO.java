package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightOrderPayTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *    飞行订单创建DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/18
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightOrderCreateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户编号
     */
    @ApiModelProperty(value = "用户编号", example = "123456", hidden = true)
    private String userNo;

    /**
     * 组织编号
     */
    @ApiModelProperty(value = "组织编号", example = "ORGANIZATION_001", hidden = true)
    private String organizationNo;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号", example = "123456")
    private String orderNo;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额", example = "1200", hidden = true)
    private String orderAmount;

    /**
     * 付款方式
     */
    @ApiModelProperty(value = "付款方式(MONTHLY_PAY-月结; IMMEDIATE_PAY-立即支付)", example = "MONTHLY_PAY")
    private FlightOrderPayTypeEnum payType;

    /**
     * 主产品编号
     */
    @ApiModelProperty(value = "产品编号", example = "123456", hidden = true)
    private String productNo;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称", example = "大疆值保服务")
    private String productName;

    /**
     * 主要产品类型
     */
    @ApiModelProperty(value = "主要产品类型", example = "FLIGHT_UAV", hidden = true)
    private String productType;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型", example = "NORMAL, DEMAND_PLAN")
    private String orderType = OrderTypeEnum.NORMAL.getCode();

    /**
     * 类目
     */
    @ApiModelProperty(value = "类目")
    private String categoryNo;

    /**
     * 类目名称
     */
    @ApiModelProperty(value = "类目名称")
    private String categoryName;

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    /**
     * 应用场景
     */
    @ApiModelProperty(value = "应用场景")
    private String scene;

    /**
     * 文件
     */
    private List<FlightOrderFileDTO> files;

    /**
     * 飞行信息
     */
    private FlightOrderFlyingInfoDTO flyingInfo;

    /**
     * 产品列表
     */
    @ApiModelProperty(value = "产品列表")
    private List<FlightOrderProductDTO> products;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", example = "123456", hidden = true)
    private String tenantId;

}
