package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 飞行无人机品牌型号导入明细视图对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行无人机品牌型号导入明细信息")
public class FlightUavBmImportDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "导入批次号")
    private String importBatchNo;

    @ApiModelProperty(value = "所在行号")
    private Integer rowNum;

    @ApiModelProperty(value = "无人机品牌型号编号")
    private String flightUavBmNo;

    @ApiModelProperty(value = "飞行无人机品牌型号名称（原始数据）")
    private String rawData;

    @ApiModelProperty(value = "处理状态")
    private String processStatusStr;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreated;
} 