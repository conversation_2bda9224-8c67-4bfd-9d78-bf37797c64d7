package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderPageQueryDTO extends PageQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单状态:APPROVING-审核中; IN_PROGRESS-进行中; FINISHED-已完成; CLOSED-已关闭", example = "APPROVING")
    private String status;

    @ApiModelProperty(value = "服务名称", example = "大疆植保农药")
    private String serviceName;

    @ApiModelProperty(value = "用户编号", example = "123456")
    private String userNo;

    @ApiModelProperty(value = "订单编号", example = "ORDER_001")
    private String orderNo;

    @ApiModelProperty(value = "类目编号", example = "CATEGORY_001")
    private String categoryNo;

    @ApiModelProperty(value = "客户ID", example = "CUSTOMER_001")
    private String customerId;

    @ApiModelProperty(value = "组织ID", example = "ORG_SZ_JZ_001")
    private String organizationId;

    @ApiModelProperty(value = "组织名称", example = "晓组织")
    private String organizationName;

    @ApiModelProperty(value = "订单类型(NORMAL-普通订单; DEMAND-PLAN-需求计划订单)")
    private String orderType = OrderTypeEnum.NORMAL.getCode();

    @ApiModelProperty(value = "租户编号", example = "deepinnet")
    private String tenantId;

}
