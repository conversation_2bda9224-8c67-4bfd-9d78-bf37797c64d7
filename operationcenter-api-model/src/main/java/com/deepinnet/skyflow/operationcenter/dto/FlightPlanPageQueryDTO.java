package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/21
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FlightPlanPageQueryDTO extends PageQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "计划名称", example = "空军一号飞行计划")
    private String planName;

    @ApiModelProperty(value = "需求ID", example = "REQUIREMENT_001")
    private String requirementId;

    @ApiModelProperty(value = "需求名称", example = "国庆节飞行需求")
    private String requirementName;

    @ApiModelProperty(value = "需求类型", hidden = true)
    private String requirementType;

    @ApiModelProperty(value = "无人机型号", example = "大疆精灵4Pro")
    private String uavModel;

    @ApiModelProperty(value = "用户编号", example = "juju", hidden = true)
    private List<String> userNo;

    @ApiModelProperty(value = "订单编号", example = "123456789000")
    private String orderNo;

    @ApiModelProperty(value = "飞行单位名称", example = "深度智联001", hidden = true)
    private String flightUnitId;

    @ApiModelProperty(value = "服务商名称", example = "00001")
    private String flightUnitName;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

}
