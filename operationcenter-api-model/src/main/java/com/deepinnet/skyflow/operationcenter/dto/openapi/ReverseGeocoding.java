package com.deepinnet.skyflow.operationcenter.dto.openapi;

import com.deepinnet.skyflow.operationcenter.common.response.HttpOpenApiResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 逆地理编码响应
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReverseGeocoding extends HttpOpenApiResponse {

    /**
     * 逆地理编码结果
     */
    private RegeoCode regeocode;
} 