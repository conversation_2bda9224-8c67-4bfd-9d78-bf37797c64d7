package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <p>
 *    订单审核
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderApproveDTO extends OrderQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审核状态(APPROVED-审核通过; REJECT-审核拒绝)
     */
    @ApiModelProperty(value = "审核状体(APPROVED-审核通过; REJECT-审核拒绝)")
    private String approveStatus;

    @ApiModelProperty(value = "审核原因 / 备注")
    private String remark;

}
