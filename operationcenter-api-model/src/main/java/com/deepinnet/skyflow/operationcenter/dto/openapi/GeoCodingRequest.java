package com.deepinnet.skyflow.operationcenter.dto.openapi;

import com.deepinnet.skyflow.operationcenter.common.constants.OpenApiConstant;
import com.deepinnet.skyflow.operationcenter.common.request.HttpApiRequest;
import com.deepinnet.skyflow.operationcenter.common.request.UrlParams;
import lombok.Data;

/**
 * 地理编码请求
 *
 * <AUTHOR>
 */
@Data
public class GeoCodingRequest implements HttpApiRequest {

    private GeocodingParam urlParams;


    @Override
    public UrlParams urlParams() {
        return this.urlParams;
    }

    @Override
    public String apiUrl() {
        String address = urlParams.getAddress();
        String city = urlParams.getCity() == null ? "" : urlParams.getCity();
        String key = urlParams.getKey();
        return String.format(OpenApiConstant.GEOCODE_URL, address, key, city);
    }
} 