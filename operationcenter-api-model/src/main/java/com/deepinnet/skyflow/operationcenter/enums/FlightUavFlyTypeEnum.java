package com.deepinnet.skyflow.operationcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 飞行无人机飞行方式枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum FlightUavFlyTypeEnum {
    FIXED_WING("FIXED_WING", "固定翼无人机"),
    SINGLE_ROTOR("SINGLE_ROTOR", "单旋翼无人机"),
    MULTI_ROTOR("MULTI_ROTOR", "多旋翼无人机"),
    MULTI_ROTOR_HELICOPTER("MULTI_ROTOR_HELICOPTER", "多旋翼直升机"),
    FLAPPING_WING("FLAPPING_WING", "扑翼无人机"),
    GLIDER("GLIDER", "滑翔无人机"),
    SINGLE_ROTOR_HELICOPTER("SINGLE_ROTOR_HELICOPTER", "单旋翼直升机"),
    ;

    private final String type;
    private final String desc;
} 