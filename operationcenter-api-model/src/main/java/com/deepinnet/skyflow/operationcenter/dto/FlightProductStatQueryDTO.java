package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 飞行产品统计查询DTO
 */
@Data
@ApiModel("飞行产品统计查询DTO")
public class FlightProductStatQueryDTO {

    @ApiModelProperty("租户ID")
    private String tenantId;

    @ApiModelProperty("类目编号")
    private String categoryNo;

    @ApiModelProperty("飞行器飞行类型")
    private String flightUavFlyType;

    @ApiModelProperty("飞行器品牌名称")
    private String flightUavBmName;
} 