package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightDemandMatchStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandSyncStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightServiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 飞行需求查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "飞行需求查询条件")
public class FlightDemandQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    private String demandNo;

    /**
     * 需求名称
     */
    @ApiModelProperty(value = "需求名称")
    private String name;

    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private FlightDemandTypeEnum type;

    /**
     * 发布者编号
     */
    @ApiModelProperty(value = "发布者编号")
    private String publisherNo;

    /**
     * 发布者名称
     */
    @ApiModelProperty(value = "发布者名称")
    private String publisherName;

    /**
     * 飞行订单编号
     */
    @ApiModelProperty(value = "飞行订单编号")
    private String flightOrderNo;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "服务名称(主产品服务名称)")
    private String productName;

    /**
     * 飞行无人机型号
     */
    @ApiModelProperty(value = "飞行无人机型号")
    private String flightUavBm;

    /**
     * 服务类型
     */
    @ApiModelProperty(value = "服务类型")
    private FlightServiceTypeEnum serviceType;

    /**
     * 详细类目编号
     */
    @ApiModelProperty(value = "类目编号")
    private String categoryNo;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private FlightDemandMatchStatusEnum matchStatus;

    @ApiModelProperty(value = "同步状态")
    private FlightDemandSyncStatusEnum syncStatus;

    /**
     * 服务提供商编号
     */
    @ApiModelProperty(value = "服务提供商编号")
    private String serviceProviderNo;

    /**
     * 服务提供商名称
     */
    @ApiModelProperty(value = "服务提供商名称")
    private String serviceProviderName;

    /**
     * 服务提供商公司名称
     */
    @ApiModelProperty(value = "服务提供商公司名称")
    private String serviceProviderCompanyName;

    /**
     * 服务提供商组织机构ID
     */
    @ApiModelProperty(value = "服务提供商组织机构ID")
    private String serviceProviderOrganizationId;

    @ApiModelProperty(value = "客户组织ID")
    private String organizationId;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "客户组织名称")
    private String organizationName;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 