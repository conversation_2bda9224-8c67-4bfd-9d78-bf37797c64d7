package com.deepinnet.skyflow.operationcenter.enums;

import com.deepinnet.skyflow.operationcenter.dto.EmergencyResponseFlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.LogisticsTransportationFlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.RoutineInspectionFlightDemandDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 * Date: 2025/4/16
 * Author: lijunheng
 */
@Getter
@AllArgsConstructor
public enum FlightDemandTypeEnum {
    ROUTINE_INSPECTION("routine_inspection", RoutineInspectionFlightDemandDTO.class, "日常巡检"),
    LOGISTICS_TRANSPORTATION("logistics_transportation",  LogisticsTransportationFlightDemandDTO.class, "物流运输"),
    EMERGENCY_RESPONSE("emergency_response",  EmergencyResponseFlightDemandDTO.class, "应急处置");

    private final String type;

    private final Class clazz;

    private final String desc;
}
