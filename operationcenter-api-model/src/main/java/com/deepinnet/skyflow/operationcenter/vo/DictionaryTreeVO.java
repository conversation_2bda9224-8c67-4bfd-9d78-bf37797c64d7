package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 字典树结构VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "字典树结构VO")
public class DictionaryTreeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "字典名称")
    private String name;

    @ApiModelProperty(value = "字典类型")
    private String type;

    @ApiModelProperty(value = "备注")
    private String comment;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;


    @ApiModelProperty(value = "字典项列表")
    private List<DictionaryItemTreeVO> items;
} 