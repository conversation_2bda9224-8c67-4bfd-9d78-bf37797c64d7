package com.deepinnet.skyflow.operationcenter.vo;

import com.deepinnet.skyflow.operationcenter.enums.FlightUavFlyTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 飞行无人机品牌型号视图对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行无人机品牌型号信息")
public class FlightUavBmVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "型号编码")
    private String flightUavBmNo;

    @ApiModelProperty(value = "第三方厂商的机器型号编号（厂商原始出厂型号）")
    private String flightUavBmModelNo;

    @ApiModelProperty(value = "型号描述")
    private String flightUavBmDesc;

    @ApiModelProperty(value = "品牌名")
    private String flightUavBmName;

    @ApiModelProperty(value = "载人类型")
    private String flightUavCrewType;

    @ApiModelProperty(value = "重量分类")
    private String flightUavWeightClassfication;

    @ApiModelProperty(value = "飞行方式分类")
    private FlightUavFlyTypeEnum flightUavFlyType;

    @ApiModelProperty(value = "最大载物重量(kg)")
    private BigDecimal flightUavMaxCarrierWeight;

    @ApiModelProperty(value = "最长飞行时间(分钟)")
    private Integer flightUavMaxFlyMinute;

    @ApiModelProperty(value = "最大续航里程(公里)")
    private BigDecimal flightUavMaxFlyRange;

    @ApiModelProperty(value = "最大飞行海拔高度(米)")
    private Integer flightUavMaxFlyHeight;

    @ApiModelProperty(value = "飞行半径(米)")
    private BigDecimal flightUavRadius;

    @ApiModelProperty(value = "是否支持视频拍摄")
    private Boolean flightUavSupportVedio;

    @ApiModelProperty(value = "相机像素")
    private String flightUavCameraPixel;

    @ApiModelProperty(value = "应用场景")
    private String[] flightUavAppliedScenarios;

    @ApiModelProperty(value = "图片列表")
    private String[] flightUavPictures;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime modifyTime;
} 