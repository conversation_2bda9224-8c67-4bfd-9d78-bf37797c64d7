package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 飞行产品类目统计结果VO
 */
@Data
@ApiModel("飞行产品类目统计结果VO")
public class FlightProductCategoryStatVO {

    @ApiModelProperty("类目编号")
    private String categoryNo;

    @ApiModelProperty("类目名称")
    private String categoryName;

    @ApiModelProperty("该类目下FLIGHT_UAV产品总数量")
    private Long totalCount;
    
    @ApiModelProperty("飞行方式列表")
    private List<FlightFlyTypeStatVO> flyTypeList;
    
    /**
     * 飞行方式统计结果
     */
    @Data
    @ApiModel("飞行方式统计结果")
    public static class FlightFlyTypeStatVO {
        
        @ApiModelProperty("飞行器飞行类型")
        private String flightUavFlyType;
        
        @ApiModelProperty("该类目下指定飞行方式的产品数量")
        private Long flyTypeCount;
        
        @ApiModelProperty("品牌列表")
        private List<FlightBrandStatVO> brandList;
    }
    
    /**
     * 品牌统计结果
     */
    @Data
    @ApiModel("品牌统计结果")
    public static class FlightBrandStatVO {
        
        @ApiModelProperty("品牌名")
        private String flightUavBmName;
        
        @ApiModelProperty("该类目下指定飞行方式和品牌名的产品数量")
        private Long brandCount;
    }
} 