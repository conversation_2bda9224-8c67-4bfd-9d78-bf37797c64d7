package com.deepinnet.skyflow.operationcenter.dto.openapi;

import com.deepinnet.skyflow.operationcenter.common.constants.OpenApiConstant;
import com.deepinnet.skyflow.operationcenter.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 地理编码参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GeocodingParam extends UrlParams {

    /**
     * 用户key
     */
    private String key = OpenApiConstant.USER_KEY;
    
    /**
     * 结构化地址信息:省份＋城市＋区县＋城镇＋乡村＋街道＋门牌号码
     */
    private String address;
    
    /**
     * 查询城市，可选：城市中文、中文全拼、citycode、adcode
     */
    private String city;
} 