package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 白名单VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "白名单视图对象")
public class WhiteListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "客户编号")
    private String customerUserNo;

    @ApiModelProperty(value = "服务商编号")
    private String supplierUserNo;

    @ApiModelProperty(value = "客户名称")
    private String customerUserName;

    @ApiModelProperty(value = "服务商名称")
    private String supplierUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreated;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;
} 