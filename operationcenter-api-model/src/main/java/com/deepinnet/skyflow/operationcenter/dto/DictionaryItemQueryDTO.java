package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典项查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "字典项查询对象")
public class DictionaryItemQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "字典ID")
    private Long dictionaryId;

    @ApiModelProperty(value = "字典项编码")
    private String code;

    @ApiModelProperty(value = "字典项名称")
    private String name;

    @ApiModelProperty(value = "状态")
    private String state;

    @ApiModelProperty(value = "父编码")
    private String parentCode;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "排序", notes = "数字越小排序越靠前")
    private Integer sort;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 