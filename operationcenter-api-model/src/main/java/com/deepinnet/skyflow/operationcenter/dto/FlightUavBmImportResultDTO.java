package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 飞行无人机品牌型号导入结果传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行无人机品牌型号导入结果")
public class FlightUavBmImportResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "导入批次号")
    private String importBatchNo;

    @ApiModelProperty(value = "导入状态：1-成功，0-失败")
    private Integer importStatus;

    @ApiModelProperty(value = "导入总条数")
    private Integer totalCount;

    @ApiModelProperty(value = "成功条数")
    private Integer successCount;

    @ApiModelProperty(value = "失败条数")
    private Integer failCount;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "失败详情")
    private List<FlightUavBmImportErrorDTO> errorList;

    @Data
    @ApiModel(description = "飞行无人机品牌型号导入错误详情")
    public static class FlightUavBmImportErrorDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "所在行号")
        private Integer rowNum;

        @ApiModelProperty(value = "错误原因")
        private String errorReason;
        
        @ApiModelProperty(value = "错误列名")
        private String errorColumnName;
        
        @ApiModelProperty(value = "错误值")
        private String errorValue;
    }
} 