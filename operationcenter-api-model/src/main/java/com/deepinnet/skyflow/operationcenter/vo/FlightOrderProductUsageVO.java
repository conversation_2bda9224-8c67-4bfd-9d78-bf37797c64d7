package com.deepinnet.skyflow.operationcenter.vo;

import com.deepinnet.skyflow.operationcenter.dto.FlightProductDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 飞行订单产品使用记录VO
 *
 * <AUTHOR>
 */
@Data
public class FlightOrderProductUsageVO {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 产品编号
     */
    @ApiModelProperty(value = "产品编号")
    private String productNo;

    /**
     * 产品类型(flight_service - 增值服务产品； flight_uav - 机型服务产品；flight_scenario - 场景服务产品)
     */
    @ApiModelProperty(value = "产品类型(flight_service - 增值服务产品； flight_uav - 机型服务产品；flight_scenario - 场景服务产品)")
    private String productType;

    /**
     * 无人机型号
     */
    @ApiModelProperty(value = "无人机型号")
    private String uavModel;

    /**
     * 订单产品用量
     */
    @ApiModelProperty(value = "订单产品用量")
    private Integer totalQuantity;

    /**
     * 产品当前用量
     */
    @ApiModelProperty(value = "当前用量")
    private Integer useQuantity;

    /**
     * 产品有效期类型(single-按次; week-7天; month-包月; year-包年)
     */
    @ApiModelProperty(value = "产品有效期类型(single-按次; week-7天; month-包月; year-包年)")
    private String validityPeriodType;

    /**
     * 购买数量
     */
    @ApiModelProperty(value = "购买数量")
    private Integer count;

    /**
     * 购买价格(数量 * 单价 = 当前产品价格)
     */
    @ApiModelProperty(value = "购买价格(数量 * 单价 = 当前产品价格)")
    private String price;

    /**
     * 产品单价
     */
    @ApiModelProperty(value = "当前产品单价")
    private String basePrice;

    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间")
    private Long validityPeriodStart;

    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间(产品类型为[次]的时候无结束时间)")
    private Long validityPeriodEnd;

    /**
     * 产品信息
     */
    private FlightProductDTO flightProduct;

    /**
     * 租户ID
     */
    private String tenantId;
} 