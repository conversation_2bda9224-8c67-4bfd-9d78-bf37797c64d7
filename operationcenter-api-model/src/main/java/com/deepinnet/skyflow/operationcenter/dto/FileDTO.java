package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 * Date: 2025/5/16
 * Author: lijunheng
 */
@ApiModel
@Data
public class FileDTO implements Serializable {

    @ApiModelProperty(value = "文件名")
    private String name;

    @ApiModelProperty(value = "文件地址")
    private String url;
}
