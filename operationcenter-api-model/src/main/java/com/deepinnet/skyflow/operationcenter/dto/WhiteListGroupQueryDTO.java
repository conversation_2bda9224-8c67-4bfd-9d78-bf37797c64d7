package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 白名单分组查询传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "白名单分组查询传输对象")
public class WhiteListGroupQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    @ApiModelProperty(value = "页码", required = true, example = "1")
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数最小为1")
    @Max(value = 1000, message = "每页记录数最大为1000")
    @ApiModelProperty(value = "每页记录数", required = true, example = "10")
    private Integer pageSize = 10;

    /**
     * 客户用户名称（模糊查询）
     */
    @ApiModelProperty(value = "客户用户名称（模糊查询）")
    private String customerUserName;

    /**
     * 服务商用户名称（模糊查询）
     */
    @ApiModelProperty(value = "服务商用户名称（模糊查询）")
    private String supplierUserName;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 