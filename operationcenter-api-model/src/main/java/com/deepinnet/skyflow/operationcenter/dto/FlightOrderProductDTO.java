package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightProductTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.ValidityPeriodEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/18
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightOrderProductDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品编号
     */
    @ApiModelProperty(value = "产品编号", example = "12345678")
    private String productNo;

    /**
     * 无人机型号
     */
    @ApiModelProperty(value = "无人机型号", example = "大疆御3000")
    private String uavModel;

    /**
     * 产品有效期类型
     */
    @ApiModelProperty(value = "产品有效期类型", example = "SINGLE")
    private ValidityPeriodEnum validityPeriodType;

    /**
     * 产品总用量
     */
    @ApiModelProperty(value = "产品总用量", example = "100", hidden = true)
    private Integer totalQuantity;

    /**
     * 产品总量
     */
    @ApiModelProperty(value = "总量)", example = "100")
    private Integer count;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价", example = "100", hidden = true)
    private String basePrice;

    /**
     * 当前产品总价
     */
    @ApiModelProperty(value = "当前产品总价(总量 * 单价)", example = "100", hidden = true)
    private String price;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型(flight_uav - 机型服务产品；flight_scenario - 场景服务产品 为主类型)", example = "FLIGHT_UAV")
    private FlightProductTypeEnum productType;

    /**
     * 产品有效期开始时间
     */
    @ApiModelProperty(value = "产品有效期开始时间", example = "2025-04-18 10:00:00")
    private LocalDateTime validityPeriodStart;

    /**
     * 产品有效期结束时间
     */
    @ApiModelProperty(value = "产品有效期结束时间", example = "2025-04-18 10:00:00")
    private LocalDateTime validityPeriodEnd;

}
