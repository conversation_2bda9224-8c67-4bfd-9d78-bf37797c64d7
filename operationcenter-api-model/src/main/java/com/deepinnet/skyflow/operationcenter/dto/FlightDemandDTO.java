package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightDemandSceneEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandMatchStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandSyncStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 飞行需求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行需求信息")
public class FlightDemandDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    private String demandNo;

    /**
     * 需求名称
     */
    @ApiModelProperty(value = "需求名称")
    private String name;

    /**
     * 需求描述
     */
    @ApiModelProperty(value = "需求描述")
    private String demandDesc;

    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private FlightDemandTypeEnum type;

    @ApiModelProperty(value = "需求来源场景")
    private FlightDemandSceneEnum scene = FlightDemandSceneEnum.SKY_FLOW_ECONOMY_MATCHING_PLATFORM;

    /**
     * 发布者编号
     */
    @ApiModelProperty(value = "发布者编号")
    private String publisherNo;

    /**
     * 发布者名称
     */
    @ApiModelProperty(value = "发布者名称")
    private String publisherName;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;

    /**
     * 飞行订单编号
     */
    @ApiModelProperty(value = "飞行订单编号")
    private String flightOrderNo;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "服务名称(主产品服务名称)")
    private String productName;

    /**
     * 飞行无人机型号
     */
    @ApiModelProperty(value = "飞行无人机型号，要么是单个无人机机型，要么是不限机型，直接是中文名称，只用于列表展示")
    private String flightUavBm;

    /**
     * 增值服务
     */
    @ApiModelProperty(value = "增值服务")
    private List<String> incrementService;

    /**
     * 附加文件请求
     */
    @ApiModelProperty(value = "附加文件请求")
    private List<FileDTO> requestAdditionalFiles;

    /**
     * 详细类目编号
     */
    @ApiModelProperty(value = "类目编号")
    private String categoryNo;

    @ApiModelProperty(value = "类目名称")
    private String categoryFullName;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private FlightDemandMatchStatusEnum matchStatus;

    @ApiModelProperty(value = "同步状态")
    private FlightDemandSyncStatusEnum syncStatus;

    @ApiModelProperty(value = "巡检区域名称")
    private String inspectionAreaName;

    @ApiModelProperty(value = "巡检区域编码, 格式: provinceCode/cityCode/countryCode/streetCode")
    private String inspectionAreaCode;

    @ApiModelProperty(value = "区域面积，平方公里")
    private Double area;

    @ApiModelProperty(value = "中心点坐标-经度")
    private String centerPointLongitude;

    @ApiModelProperty(value = "中心点坐标-纬度")
    private String centerPointLatitude;

    @ApiModelProperty(value = "区域坐标，WKT字符串")
    private String areaCoordinate;

    /**
     * 服务提供商编号
     */
    @ApiModelProperty(value = "服务提供商编号")
    private String serviceProviderNo;

    /**
     * 服务提供商名称
     */
    @ApiModelProperty(value = "服务提供商名称")
    private String serviceProviderName;

    /**
     * 服务提供商公司名称
     */
    @ApiModelProperty(value = "服务提供商公司名称")
    private String serviceProviderCompanyName;

    /**
     * 服务提供商组织机构ID
     */
    @ApiModelProperty(value = "服务提供商组织机构ID")
    private String serviceProviderOrganizationId;

    @ApiModelProperty(value = "客户组织ID")
    private String organizationId;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "客户组织名称")
    private String organizationName;

    /**
     * 同步订单编号
     */
    @ApiModelProperty(value = "同步订单编号")
    private String syncOrderNo;

    /**
     * 业务数据
     */
    @ApiModelProperty(value = "业务数据")
    private String bizData;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 飞行次数
     */
    @ApiModelProperty(value = "飞行次数")
    private Integer flightCount;

    /**
     * 日常巡检需求详情（当type=ROUTINE_INSPECTION时使用）
     */
    @ApiModelProperty(value = "日常巡检需求详情", notes = "当需求类型为ROUTINE_INSPECTION时使用")
    private RoutineInspectionFlightDemandDTO routineInspectionDetail;

    /**
     * 物流运输需求详情（当type=LOGISTICS_TRANSPORTATION时使用）
     */
    @ApiModelProperty(value = "物流运输需求详情", notes = "当需求类型为LOGISTICS_TRANSPORTATION时使用")
    private LogisticsTransportationFlightDemandDTO logisticsTransportationDetail;

    /**
     * 应急处置需求详情（当type=EMERGENCY_RESPONSE时使用）
     */
    @ApiModelProperty(value = "应急处置需求详情", notes = "当需求类型为EMERGENCY_RESPONSE时使用")
    private EmergencyResponseFlightDemandDTO emergencyResponseDetail;

    public Object queryDetail() {
        switch (this.type) {
            case ROUTINE_INSPECTION:
                return this.routineInspectionDetail;
            case LOGISTICS_TRANSPORTATION:
                return this.logisticsTransportationDetail;
            case EMERGENCY_RESPONSE:
                return this.emergencyResponseDetail;
            default:
                return null;
        }
    }

    public void createDetail(Object detail) {
        switch (this.type) {
            case ROUTINE_INSPECTION:
                this.routineInspectionDetail = (RoutineInspectionFlightDemandDTO) detail;
                break;
            case LOGISTICS_TRANSPORTATION:
                this.logisticsTransportationDetail = (LogisticsTransportationFlightDemandDTO) detail;
                break;
            case EMERGENCY_RESPONSE:
                this.emergencyResponseDetail = (EmergencyResponseFlightDemandDTO) detail;
        }
    }
} 