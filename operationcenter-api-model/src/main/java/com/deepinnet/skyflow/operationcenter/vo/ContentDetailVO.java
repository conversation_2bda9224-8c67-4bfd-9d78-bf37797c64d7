package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 首页内容视图对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "首页内容信息")
public class ContentDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "内容ID")
    private Integer id;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "内容JSON")
    private String contentJson;

    @ApiModelProperty(value = "内容类型")
    private String contentType;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreated;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "是否删除")
    private Boolean isDeleted;
} 