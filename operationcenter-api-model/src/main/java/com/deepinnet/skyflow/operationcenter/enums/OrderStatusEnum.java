package com.deepinnet.skyflow.operationcenter.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *    订单状态枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    /**
     * APPROVING-审核中
     */
    APPROVING("APPROVING", "审核中"),

    /**
     * IN_PROGRESS-进行中
     */
    IN_PROGRESS("IN_PROGRESS", "进行中"),

    /**
     * FINISHED-已完成
     */
    FINISHED("FINISHED", "已完成"),

    /**
     * CLOSED-已关闭
     */
    CLOSED("CLOSED", "已关闭");

    private final String code;

    private final String desc;

    public static OrderStatusEnum getByCode(String code) {
        for (OrderStatusEnum orderStatusEnum : OrderStatusEnum.values()) {
            if (StrUtil.equals(orderStatusEnum.getCode(), code)) {
                return orderStatusEnum;
            }
        }
        return null;
    }
}
