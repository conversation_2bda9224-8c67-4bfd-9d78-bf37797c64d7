package com.deepinnet.skyflow.operationcenter.dto.openapi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 地址组件信息
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddressComponent implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 省份名称
     */
    private String province;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 区县名称
     */
    private String district;

    /**
     * 乡镇名称
     */
    private String township;

    /**
     * 社区名称
     */
    private String neighborhood;

    /**
     * 楼栋名称
     */
    private String building;

    /**
     * 街道名称
     */
    private String street;

    /**
     * 门牌号码
     */
    private String number;

    /**
     * 行政区编码
     */
    private String adcode;

    /**
     * 城市编码
     */
    private String citycode;
} 