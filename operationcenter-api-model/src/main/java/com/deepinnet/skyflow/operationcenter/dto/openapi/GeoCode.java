package com.deepinnet.skyflow.operationcenter.dto.openapi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 地理编码结果
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GeoCode implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 结构化地址
     */
    private String formatted_address;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String district;

    /**
     * 乡镇
     */
    private String township;

    /**
     * 社区
     */
    private String neighborhood;

    /**
     * 楼栋
     */
    private String building;

    /**
     * 行政区编码
     */
    private String adcode;

    /**
     * 城市编码
     */
    private String citycode;

    /**
     * 坐标点
     */
    private String location;
} 