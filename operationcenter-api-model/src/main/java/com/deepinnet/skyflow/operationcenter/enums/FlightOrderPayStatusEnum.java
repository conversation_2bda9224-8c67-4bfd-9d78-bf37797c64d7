package com.deepinnet.skyflow.operationcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/18
 */

@Getter
@AllArgsConstructor
public enum FlightOrderPayStatusEnum {

    /**
     * UN_PAY-未支付
     */
    UN_PAY("UN_PAY", "未支付"),

    /**
     * 已支付
     */
    PAID("PAID", "已支付");

    private final String status;

    private final String desc;
}
