package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类目查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "类目查询参数")
public class CategoryQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类目编码")
    private String categoryNo;

    @ApiModelProperty(value = "类目名称")
    private String categoryName;

    @ApiModelProperty(value = "父类目编码")
    private String parentCategoryNo;

    @ApiModelProperty(value = "类目层级")
    private Integer categoryLevel;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "类目图片列表")
    private String[] categoryPictureList;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 