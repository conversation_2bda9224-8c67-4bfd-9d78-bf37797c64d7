package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 飞行需求与计划关联查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "飞行需求与计划关联查询条件")
public class FlightDemandPlanRelationQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 飞行需求编号
     */
    @ApiModelProperty(value = "飞行需求编号")
    private String flightDemandCode;

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID")
    private String planId;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 