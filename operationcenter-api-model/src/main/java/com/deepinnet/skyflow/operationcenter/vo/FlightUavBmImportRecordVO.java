package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 飞行无人机品牌型号导入记录视图对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行无人机品牌型号导入记录信息")
public class FlightUavBmImportRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "导入批次号")
    private String importBatchNo;

    @ApiModelProperty(value = "导入文件名")
    private String fileName;

    @ApiModelProperty(value = "导入总条数")
    private Integer totalCount;

    @ApiModelProperty(value = "成功条数")
    private Integer successCount;

    @ApiModelProperty(value = "失败条数")
    private Integer failCount;

    @ApiModelProperty(value = "导入状态")
    private String importStatusStr;

    @ApiModelProperty(value = "导入失败原因")
    private String failReason;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreated;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "创建人")
    private String creator;
} 