package com.deepinnet.skyflow.operationcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 * Date: 2025/4/16
 * Author: lijunheng
 */
@AllArgsConstructor
@Getter
public enum FlyingFrequencyEnum {
    DAILY("daily", "每天"),
    WEEKLY("weekly", "每周"),
    MONTHLY("monthly", "每月"),
    YEARLY("yearly", "每年"),
    WEEKLY_MONDAY("weekly_monday", "每周一"),
    WEEKLY_TUESDAY("weekly_tuesday", "每周二"),
    WEEKLY_WEDNESDAY("weekly_wednesday", "每周三"),
    WEEKLY_THURSDAY("weekly_thursday", "每周四"),
    WEEKLY_FRIDAY("weekly_friday", "每周五"),
    WEEKLY_SATURDAY("weekly_saturday", "每周六"),
    WEEKLY_SUNDAY("weekly_sunday", "每周日");

    private final String frequency;
    private final String desc;
}
