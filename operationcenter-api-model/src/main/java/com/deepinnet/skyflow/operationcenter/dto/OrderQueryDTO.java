package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "用户编号")
    private List<String> userNo;

    @ApiModelProperty(value = "订单类型(NORMAL-普通订单; DEMAND_PLAN-需求计划订单)", example = "NORMAL")
    private String orderType = OrderTypeEnum.NORMAL.getCode();

    @ApiModelProperty(value = "租户编号")
    private String tenantId;

}
