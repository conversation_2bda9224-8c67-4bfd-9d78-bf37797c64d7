package com.deepinnet.skyflow.operationcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/18
 */

@Getter
@AllArgsConstructor
public enum FlightOrderRefundStatusEnum {

    /**
     * UN_REFUND
     */
    UN_REFUND("UN_REFUND", "未退款"),

    /**
     * REFUNDING
     */
    REFUNDING("REFUNDING", "退款中"),

    /**
     * REFUNDED
     */
    REFUNDED("REFUNDED", "已退款");

    private final String status;

    private final String desc;
}
