package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightProductTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavFlyTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.ProductServiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 飞行产品查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "飞行产品查询条件")
public class FlightProductQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productNo;

    /**
     * 产品编码列表
     */
    @ApiModelProperty(value = "产品编码列表")
    private List<String> productNoList;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private FlightProductTypeEnum productType;

    /**
     * 产品服务类型
     */
    @ApiModelProperty(value = "产品服务类型: PILOT_SERVICE-飞手服务，NORMAL_SERVICE-普通服务")
    private ProductServiceTypeEnum productServiceType;

    /**
     * 关联类目编号
     */
    @ApiModelProperty(value = "关联类目编号")
    private String categoryNo;

    /**
     * 服务提供商编号
     */
    @ApiModelProperty(value = "服务提供商编号")
    private String serviceProviderNo;

    /**
     * 产品状态
     */
    @ApiModelProperty(value = "产品状态")
    private String productStatus;

    /**
     * 是否提供飞手服务
     */
    @ApiModelProperty(value = "是否提供飞手服务")
    private Boolean supportUavPilot;
    
    /**
     * 飞行方式分类
     */
    @ApiModelProperty(value = "飞行方式分类")
    private FlightUavFlyTypeEnum flightUavFlyType;
    
    /**
     * 无人机品牌名称
     */
    @ApiModelProperty(value = "无人机品牌名称")
    private String flightUavBmName;

    /**
     * 无人机型号编号
     */
    @ApiModelProperty(value = "无人机型号编号")
    private String flightUavBmModelNo;

    /**
     * 租户
     */
    @ApiModelProperty(value = "租户")
    private String tenantId;
} 