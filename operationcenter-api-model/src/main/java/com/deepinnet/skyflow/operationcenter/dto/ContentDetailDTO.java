package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 首页内容传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "首页内容传输对象")
public class ContentDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "内容ID")
    private Integer id;

    @ApiModelProperty(value = "租户ID", required = true)
    private String tenantId;

    @NotBlank(message = "内容JSON不能为空")
    @ApiModelProperty(value = "内容JSON", required = true)
    private String contentJson;

    @NotBlank(message = "内容类型不能为空")
    @ApiModelProperty(value = "内容类型", required = true)
    private String contentType;


} 