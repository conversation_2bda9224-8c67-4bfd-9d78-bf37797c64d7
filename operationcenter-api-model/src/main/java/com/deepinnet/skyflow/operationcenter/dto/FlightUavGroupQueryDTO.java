package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 按机型分组的飞行无人机查询DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "按机型分组的飞行无人机查询参数")
public class FlightUavGroupQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务商编号
     */
    @ApiModelProperty(value = "服务商编号", required = true)
    private String supplierUserNo;

    /**
     * 无人机品牌名称
     */
    @ApiModelProperty(value = "无人机品牌名称")
    private String flightUavBmName;

    /**
     * 第三方厂商的机器型号编号（厂商原始出厂型号）
     */
    @ApiModelProperty(value = "第三方厂商的机器型号编号")
    private String flightUavBmModelNo;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 