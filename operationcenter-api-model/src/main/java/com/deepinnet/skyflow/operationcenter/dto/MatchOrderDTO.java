package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 撮合订单DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "撮合订单信息")
public class MatchOrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 撮合订单编号
     */
    @ApiModelProperty(value = "撮合订单编号")
    private String matchOrderNo;

    /**
     * 飞行产品编号
     */
    @ApiModelProperty(value = "飞行产品编号")
    private String flightProductNo;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 撮合状态
     */
    @ApiModelProperty(value = "撮合状态")
    private String matchStatus;

    /**
     * 服务提供商编号
     */
    @ApiModelProperty(value = "服务提供商编号")
    private String serviceProviderNo;

    /**
     * 服务提供商名称
     */
    @ApiModelProperty(value = "服务提供商名称")
    private String serviceProviderName;

    /**
     * 飞行需求编号
     */
    @ApiModelProperty(value = "飞行需求编号")
    private String flightDemandNo;

    /**
     * 同步订单编号
     */
    @ApiModelProperty(value = "同步订单编号")
    private String syncOrderNo;

    /**
     * 订单创建时间
     */
    @ApiModelProperty(value = "订单创建时间")
    private LocalDateTime orderCreateTime;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
} 