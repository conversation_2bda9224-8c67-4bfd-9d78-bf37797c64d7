package com.deepinnet.skyflow.operationcenter.enums;

import lombok.Getter;

/**
 * 产品服务类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ProductServiceTypeEnum {

    /**
     * 飞手服务
     */
    PILOT_SERVICE("PILOT_SERVICE", "飞手服务"),
    
    /**
     * 数据采集
     */
    DATA_COLLECTION("DATA_COLLECTION", "数据采集"),
    
    /**
     * 数据处理
     */
    DATA_PROCESSING("DATA_PROCESSING", "数据处理"),
    
    /**
     * 算法识别
     */
    ALGORITHM_RECOGNITION("ALGORITHM_RECOGNITION", "算法识别"),

    /**
     * 普通服务
     */
    NORMAL_SERVICE("NORMAL_SERVICE", "普通服务");

    private final String code;
    private final String desc;

    ProductServiceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ProductServiceTypeEnum getByCode(String code) {
        for (ProductServiceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 通过code获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(String code) {
        ProductServiceTypeEnum type = getByCode(code);
        return type != null ? type.getDesc() : null;
    }
} 