package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.ValidityPeriodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 价格传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "价格传输对象")
public class PriceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "价格ID")
    private Integer id;

    @NotBlank(message = "关联产品编号不能为空")
    @ApiModelProperty(value = "关联产品编号", required = true)
    private String productNo;

    @NotNull(message = "基础价格不能为空")
    @ApiModelProperty(value = "基础价格", required = true)
    private BigDecimal basePrice;

    @ApiModelProperty(value = "计费单位")
    private String billingUnit;

    @ApiModelProperty(value = "有效期")
    private ValidityPeriodEnum validityPeriod;

    @ApiModelProperty(value = "有效次数")
    private Integer validityTimes;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

} 