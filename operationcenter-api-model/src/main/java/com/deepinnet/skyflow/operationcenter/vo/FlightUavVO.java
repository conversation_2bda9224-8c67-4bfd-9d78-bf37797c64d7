package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 飞行无人机VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行无人机视图对象")
public class FlightUavVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "无人机编号")
    private String flightUavNo;

    @ApiModelProperty(value = "飞行器名称")
    private String flightUavName;

    @ApiModelProperty(value = "归属机巢")
    private String flightStationNo;

    @ApiModelProperty(value = "SN码")
    private String flightUavSn;

    @ApiModelProperty(value = "机型编号")
    private String flightUavBmNo;
    
    @ApiModelProperty(value = "服务商编号")
    private String supplierUserNo;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreated;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;
} 