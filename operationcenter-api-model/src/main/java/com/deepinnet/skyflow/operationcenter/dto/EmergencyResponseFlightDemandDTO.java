package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description:
 * Date: 2025/4/16
 * Author: lijunheng
 */
@Data
@ApiModel(description = "应急处置飞行需求信息")
public class EmergencyResponseFlightDemandDTO {

    @ApiModelProperty(value = "起飞时间")
    private LocalDateTime takeOffTime;

    @ApiModelProperty(value = "应急事件地址")
    private String emergencyEventAddress;

    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;
}
