package com.deepinnet.skyflow.operationcenter.common.response;

import lombok.Data;

import java.io.Serializable;

/**
 * HTTP 开放API响应基类
 *
 * <AUTHOR>
 */
@Data
public class HttpOpenApiResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 返回状态，1：成功，0：失败
     */
    private String status;

    /**
     * 返回状态说明
     */
    private String info;

    /**
     * 返回状态码
     */
    private String infocode;
} 