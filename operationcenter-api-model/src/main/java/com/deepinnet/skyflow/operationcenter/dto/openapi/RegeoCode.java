package com.deepinnet.skyflow.operationcenter.dto.openapi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 逆地理编码结果
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RegeoCode implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 结构化地址
     */
    private String formatted_address;

    /**
     * 地址组件
     */
    private AddressComponent addressComponent;
} 