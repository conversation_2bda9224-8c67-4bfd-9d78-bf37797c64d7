package com.deepinnet.skyflow.operationcenter.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 飞行无人机载人类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum FlightUavCrewTypeEnum {
    
    /**
     * 无人
     */
    UNMANNED("UNMANNED", "无人"),
    
    /**
     * 载人
     */
    MANNED("MANNED", "载人");
    
    /**
     * 编码
     */
    @EnumValue
    @JsonValue
    private final String code;
    
    /**
     * 描述
     */
    private final String desc;
    
    FlightUavCrewTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static FlightUavCrewTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (FlightUavCrewTypeEnum item : FlightUavCrewTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        
        return null;
    }
} 