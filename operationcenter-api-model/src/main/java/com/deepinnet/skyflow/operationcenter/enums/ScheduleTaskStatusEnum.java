package com.deepinnet.skyflow.operationcenter.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ScheduleTaskStatusEnum {

    WAIT_EXECUTE("wait_execute", "待执行"),

    EXECUTING("executing", "执行中"),

    SUCCESS("success", "执行成功"),

    FAIL("fail", "执行失败"),

    TERMINATED("terminated", "已终止"),

    CANCELED("canceled", "已取消");

    /**
     * 类型
     */
    private String status;

    /**
     * 说明
     */
    private String desc;

    public static ScheduleTaskStatusEnum getByStatus(String status) {
        if (status == null) {
            return null;
        }
        for (ScheduleTaskStatusEnum an : ScheduleTaskStatusEnum.values()) {
            if (StrUtil.equals(an.getStatus(), status))
                return an;
        }
        return null;
    }

}