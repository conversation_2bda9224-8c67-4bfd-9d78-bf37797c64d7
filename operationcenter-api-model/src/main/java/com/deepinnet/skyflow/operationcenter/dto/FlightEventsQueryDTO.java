package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightEventStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightEventTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 飞行事件查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlightEventsQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 事件id
     */
    private String id;

    /**
     * 事件类型，例如违停、闯入禁区等
     */
    private FlightEventTypeEnum eventType;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件状态
     */
    private FlightEventStatusEnum status;

    /**
     * 事件发生地点
     */
    private String eventLocation;

    /**
     * 涉事车辆的车牌号
     */
    private String licensePlate;

    /**
     * 飞行任务id
     */
    private String flightTaskCode;

    /**
     * 事件发生开始时间
     */
    private LocalDateTime startTime;

    /**
     * 事件发生结束时间
     */
    private LocalDateTime endTime;
} 