package com.deepinnet.skyflow.operationcenter.dto.openapi;

import com.deepinnet.skyflow.operationcenter.common.constants.OpenApiConstant;
import com.deepinnet.skyflow.operationcenter.common.request.UrlParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 逆地理编码参数
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReverseGeocodingParam extends UrlParams {

    /**
     * 用户key
     */
    private String key = OpenApiConstant.USER_KEY;

    /**
     * 坐标，用,分割，例如：116.481488,39.990464
     */
    private String location;
} 