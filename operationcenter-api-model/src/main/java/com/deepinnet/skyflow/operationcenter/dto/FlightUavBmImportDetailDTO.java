package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 飞行无人机品牌型号导入明细传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行无人机品牌型号导入明细传输对象")
public class FlightUavBmImportDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "导入批次号")
    private String importBatchNo;

    @ApiModelProperty(value = "所在行号")
    private Integer rowNum;

    @ApiModelProperty(value = "无人机品牌型号编号")
    private String flightUavBmNo;

    @ApiModelProperty(value = "飞行无人机品牌型号名称（原始数据）")
    private String rawData;

    @ApiModelProperty(value = "处理状态（0-处理中，1-成功，2-失败）")
    private Integer processStatus;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreated;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "修改人")
    private String modifier;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;
} 