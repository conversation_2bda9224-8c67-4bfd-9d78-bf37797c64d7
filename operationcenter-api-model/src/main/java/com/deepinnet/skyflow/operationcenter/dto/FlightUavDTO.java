package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 飞行无人机DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行无人机数据传输对象")
public class FlightUavDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @NotBlank(message = "无人机编号不能为空")
    private String flightUavNo;
    
    @ApiModelProperty(value = "飞行器名称")
    private String flightUavName;

    @ApiModelProperty(value = "归属机巢")
    private String flightStationNo;

    @ApiModelProperty(value = "SN码", required = true)
    @NotBlank(message = "SN码不能为空")
    private String flightUavSn;

    @ApiModelProperty(value = "机型编号", required = true)
    @NotBlank(message = "机型编号不能为空")
    private String flightUavBmNo;
    
    @ApiModelProperty(value = "服务商编号")
    private String supplierUserNo;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreated;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;
} 