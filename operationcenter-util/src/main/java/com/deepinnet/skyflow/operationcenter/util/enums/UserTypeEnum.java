package com.deepinnet.skyflow.operationcenter.util.enums;

import cn.hutool.core.util.StrUtil;
import lombok.*;

/**
 * <AUTHOR> wong
 * @create 2025/4/17 13:46
 * @Description
 */
@AllArgsConstructor
@Getter
public enum UserTypeEnum {

    SUPPLIER("supplier", "服务商"),

    CUSTOMER("customer", "客户"),

    OPERATION("operation", "运营"),

    OTHER("other", "其他"),


    ;

    private final String code;

    private final String desc;

    public static UserTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }

        for (UserTypeEnum type : UserTypeEnum.values()) {
            if (StrUtil.equals(type.code, code)) {
                return type;
            }
        }
        return null;
    }

}
