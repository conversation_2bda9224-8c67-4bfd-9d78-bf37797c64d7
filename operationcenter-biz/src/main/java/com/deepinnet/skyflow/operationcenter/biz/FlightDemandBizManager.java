package com.deepinnet.skyflow.operationcenter.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.deepinnet.infra.api.dto.UserDetailDTO;
import com.deepinnet.infra.api.dto.UserQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.service.client.FlightPlanQueryClient;
import com.deepinnet.skyflow.operationcenter.service.client.FlightUserClient;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightDemandConvert;
import com.deepinnet.skyflow.operationcenter.service.demand.FlightDemandService;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.service.event.FlightDemandCreateEvent;
import com.deepinnet.skyflow.operationcenter.service.order.FlightOrderHelper;
import com.deepinnet.skyflow.operationcenter.service.product.FlightProductService;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.deepinnet.spatiotemporalplatform.dto.BatchQueryPlanDTO;
import com.deepinnet.spatiotemporalplatform.vo.FlightCountVO;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: 飞行需求业务处理类，用于整合飞行需求领域服务和其他领域服务
 * Date: 2025/4/17
 * Author: lijunheng
 */
@Service
public class FlightDemandBizManager {

    @Resource
    private FlightDemandService demandService;

    @Resource
    private FlightOrderHelper orderHelper;

    @Resource
    private ApplicationEventPublisher publisher;

    @Resource
    private FlightProductService productService;

    @Resource
    private FlightDemandConvert demandConvert;

    @Resource
    private FlightUserClient userClient;

    @Resource
    private FlightPlanQueryClient flightPlanQueryClient;

    public String saveFlightDemand(FlightDemandDTO flightDemandDTO) {
        String userNo = UserUtil.getUserNo();
        flightDemandDTO.setPublisherNo(userNo);
        UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setUserNos(List.of(userNo));
        List<UserDetailDTO> userDetailList = userClient.getUserDetailList(userQueryDTO);
        flightDemandDTO.setPublisherName(userDetailList.get(0).getUserName());

        FlightOrderVO flightOrder = orderHelper.getFlightOrder(flightDemandDTO);
        flightDemandDTO.setProductName(flightOrder.getProductName());
        flightDemandDTO.setOrganizationId(flightOrder.getOrganizationId());
        flightDemandDTO.setOrganizationName(flightOrder.getOrganizationName());
        String demandCode = demandService.saveFlightDemand(flightDemandDTO);
        //发布需求创建事件，异步通知需要关心的业务
        publisher.publishEvent(new FlightDemandCreateEvent(demandCode));
        return demandCode;
    }

    public FlightDemandVO getFlightDemandByNo(String demandNo) {
        FlightDemandDTO flightDemand = demandService.getFlightDemandByNo(demandNo);
        if (flightDemand == null) {
            return null;
        }

        FlightDemandVO flightDemandVO = demandConvert.convertToVO(flightDemand);
        //获取增值服务
        List<String> incrementServiceProductNoList = flightDemand.getIncrementService();
        if (CollectionUtil.isNotEmpty(incrementServiceProductNoList)) {
            List<FlightProductDTO> incrementServiceList = productService.listByProductNoList(incrementServiceProductNoList);
            flightDemandVO.setIncrementServiceList(incrementServiceList);
        }

        //获取需求目前已经飞了多少次
        BatchQueryPlanDTO batchQueryPlanDTO = new BatchQueryPlanDTO();
        batchQueryPlanDTO.setBizNo(ListUtil.of(demandNo));
        batchQueryPlanDTO.setTenantId(flightDemand.getTenantId());
        List<FlightCountVO> flightCountList = flightPlanQueryClient.planCountByBizNo(batchQueryPlanDTO);
        flightDemandVO.setFlightCount((CollectionUtil.isEmpty(flightCountList) ? 0 : flightCountList.get(0).getCount()));

        return flightDemandVO;
    }

    public CommonPage<FlightDemandDTO> pageQueryFlightDemand(FlightDemandQueryDTO queryDTO) {
        CommonPage<FlightDemandDTO> flightDemandDTOCommonPage = demandService.pageQueryFlightDemand(queryDTO);
        List<String> demandNoList = flightDemandDTOCommonPage.getList().stream().map(FlightDemandDTO::getDemandNo).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(demandNoList)) {
            BatchQueryPlanDTO batchQueryPlanDTO = new BatchQueryPlanDTO();
            batchQueryPlanDTO.setBizNo(demandNoList);
            batchQueryPlanDTO.setTenantId(queryDTO.getTenantId());
            List<FlightCountVO> flightCountList = flightPlanQueryClient.planCountByBizNo(batchQueryPlanDTO);
            Map<String, Integer> demandFlightCountMap = flightCountList.stream().collect(Collectors.toMap(FlightCountVO::getBizNo, FlightCountVO::getCount));
            flightDemandDTOCommonPage.getList().forEach(flightDemandDTO -> flightDemandDTO.setFlightCount(demandFlightCountMap.getOrDefault(flightDemandDTO.getDemandNo(), 0)));
        }

        return flightDemandDTOCommonPage;
    }

    /**
     * YF同步飞行需求和计划绑定关系
     *
     * @param dto
     * @return
     */
    public Boolean syncPlanBind(DemandSyncPlanBindDTO dto) {
        return demandService.saveDemandPlanBind(dto);
    }

    public FlightDemandDTO getFlightDemandByPlanId(String planId) {
        return demandService.getFlightDemandByPlanId(planId);
    }
}
