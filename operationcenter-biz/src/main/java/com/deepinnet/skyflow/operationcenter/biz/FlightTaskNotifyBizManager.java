package com.deepinnet.skyflow.operationcenter.biz;

import com.deepinnet.localdata.integration.FlightAlgorithmClient;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.enums.FlightEventTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.RedisTaskStateManager;
import com.deepinnet.skyflow.operationcenter.service.demand.FlightDemandService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description: 飞行任务通知管理，需要做幂等，可能会发多条同类请求
 * 上游请求过来是不知道租户id的，需要先查询
 * Date: 2025/4/17
 * Author: lijunheng
 */
@Service
public class FlightTaskNotifyBizManager {

    @Resource
    private FlightAlgorithmClient flightAlgorithmClient;

    @Resource
    private RedisTaskStateManager redisTaskStateManager;

    @Resource
    private FlightDemandService flightDemandService;

    /**
     * 创建飞行任务
     *
     * @param monitorCreateDTO
     */
    public void startFlightTask(FlightAlgorithmMonitorCreateDTO monitorCreateDTO) {
        //保险起见，设置2天的过期时间
        boolean ok = redisTaskStateManager.tryStart(monitorCreateDTO.getFlightTaskId(), 2 * 24 * 3600);
        if (Boolean.TRUE.equals(ok)) {
            FlightDemandDTO flightDemand = flightDemandService.getFlightDemandByPlanId(monitorCreateDTO.getFlightTaskId());
            monitorCreateDTO.setTenantId(flightDemand.getTenantId());
            flightAlgorithmClient.createFlightMonitorTask(convert(monitorCreateDTO));
        }
    }

    /**
     * 结束飞行任务
     *
     * @param flightTaskNotifyDTO
     */
    public void endFlightTask(FlightTaskNotifyDTO flightTaskNotifyDTO) {
        boolean ok = redisTaskStateManager.tryEnd(flightTaskNotifyDTO.getFlightTaskId(), 2 * 24 * 3600);
        if (Boolean.TRUE.equals(ok)) {
            FlightDemandDTO flightDemand = flightDemandService.getFlightDemandByPlanId(flightTaskNotifyDTO.getFlightTaskId());
            flightTaskNotifyDTO.setTenantId(flightDemand.getTenantId());
            flightAlgorithmClient.notifyFlightTaskEnd(convert(flightTaskNotifyDTO));
        }
    }

    private com.deepinnet.localdata.integration.model.outsidebean.FlightAlgorithmMonitorCreateDTO convert(FlightAlgorithmMonitorCreateDTO monitorCreateDTO) {
        com.deepinnet.localdata.integration.model.outsidebean.FlightAlgorithmMonitorCreateDTO dto = new com.deepinnet.localdata.integration.model.outsidebean.FlightAlgorithmMonitorCreateDTO();
        dto.setFlight_task_id(monitorCreateDTO.getFlightTaskId());
        dto.setDrone_video_url(monitorCreateDTO.getVideoUrl());
        dto.setTenant_id(monitorCreateDTO.getTenantId());
        List<String> eventTypeList = Optional.ofNullable(monitorCreateDTO.getEventTypeList()).orElseGet(ArrayList::new).stream().map(FlightEventTypeEnum::getCode).collect(Collectors.toList());
        dto.setTask_type_list(eventTypeList);
        return dto;
    }

    private com.deepinnet.localdata.integration.model.outsidebean.FlightTaskNotifyDTO convert(FlightTaskNotifyDTO flightTaskNotifyDTO) {
        com.deepinnet.localdata.integration.model.outsidebean.FlightTaskNotifyDTO dto = new com.deepinnet.localdata.integration.model.outsidebean.FlightTaskNotifyDTO();
        dto.setFlight_task_id(flightTaskNotifyDTO.getFlightTaskId());
        dto.setTenant_id(flightTaskNotifyDTO.getTenantId());
        return dto;
    }
}
