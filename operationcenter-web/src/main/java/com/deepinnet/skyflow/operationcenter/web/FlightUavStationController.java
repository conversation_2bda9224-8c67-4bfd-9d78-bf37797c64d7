package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavStationDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavStationQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.IdDTO;
import com.deepinnet.skyflow.operationcenter.enums.BizTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavStationService;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavStationConvert;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavStationVO;
import com.deepinnet.skyflow.operationcenter.dto.StationDistanceQueryDTO;
import com.deepinnet.skyflow.operationcenter.vo.StationDistanceVO;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 飞行无人机站点控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/flight/uav/station")
@Api(tags = "飞行无人机站点管理")
@Validated
@Slf4j
public class FlightUavStationController {

    @Resource
    private FlightUavStationService flightUavStationService;

    @Resource
    private FlightUavStationConvert flightUavStationConvert;

    @PostMapping("/create")
    @ApiOperation("创建飞行无人机站点")
    public Result<String> createFlightUavStation(@RequestBody @Valid FlightUavStationDTO flightUavStationDTO) {
        // 设置租户ID
        flightUavStationDTO.setTenantId(TenantIdUtil.getTenantId());
        String no = flightUavStationService.saveFlightUavStation(flightUavStationDTO);
        return Result.success(no);
    }

    @PostMapping("/update")
    @ApiOperation("更新飞行无人机站点")
    public Result<Boolean> updateFlightUavStation(@RequestBody @Valid FlightUavStationDTO flightUavStationDTO) {
        // 设置租户ID
        flightUavStationDTO.setTenantId(TenantIdUtil.getTenantId());
        boolean success = flightUavStationService.updateFlightUavStation(flightUavStationDTO);
        return Result.success(success);
    }

    @PostMapping("/get")
    @ApiOperation("根据ID获取飞行无人机站点")
    public Result<FlightUavStationVO> getFlightUavStationById(@RequestBody @Valid @NotNull(message = "ID不能为空") 
                                                            @ApiParam(value = "飞行无人机站点ID DTO", required = true) IdDTO idDTO) {
        FlightUavStationDTO flightUavStationDTO = flightUavStationService.getFlightUavStationById(idDTO.getId());
        FlightUavStationVO flightUavStationVO = flightUavStationConvert.convertToVO(flightUavStationDTO);
        return Result.success(flightUavStationVO);
    }

    @GetMapping("/getByNo")
    @ApiOperation("根据机巢编号获取飞行无人机站点")
    public Result<FlightUavStationVO> getFlightUavStationByNo(@RequestParam @Valid @NotBlank(message = "机巢编号不能为空")
                                                           @ApiParam(value = "机巢编号", required = true) String flightStationNo) {
        FlightUavStationDTO flightUavStationDTO = flightUavStationService.getFlightUavStationByNo(flightStationNo);
        FlightUavStationVO flightUavStationVO = flightUavStationConvert.convertToVO(flightUavStationDTO);
        return Result.success(flightUavStationVO);
    }

    @GetMapping("/listByCompany")
    @ApiOperation("根据服务商编号获取飞行无人机站点列表")
    public Result<List<FlightUavStationVO>> getFlightUavStationsByCompanyUserNo(@RequestParam @Valid @NotBlank(message = "服务商编号不能为空")
                                                                          @ApiParam(value = "服务商编号", required = true) String companyUserNo) {
        List<FlightUavStationDTO> flightUavStationDTOList = flightUavStationService.getFlightUavStationsByCompanyUserNo(companyUserNo);
        List<FlightUavStationVO> flightUavStationVOList = flightUavStationConvert.convertToVOList(flightUavStationDTOList);
        return Result.success(flightUavStationVOList);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询飞行无人机站点 - 支持通过飞行器型号编号列表(flightUavBmList)、飞行方式类型(flightUavFlyType)和机型型号(flightUavBmModelNo)查询, 包含飞行器和机型信息")
    public Result<CommonPage<FlightUavStationVO>> pageQueryFlightUavStation(@RequestBody @Valid FlightUavStationQueryDTO queryDTO) {
        // 设置租户ID
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        queryDTO.setCompanyUserNo(UserUtil.getUserNo());
        CommonPage<FlightUavStationDTO> page = flightUavStationService.pageQueryFlightUavStation(queryDTO);
        List<FlightUavStationVO> flightUavStationVOList = flightUavStationConvert.convertToVOList(page.getList());
        return Result.success(CommonPage.copyMetaWithNewData(page, flightUavStationVOList));
    }

    @PostMapping("/delete")
    @ApiOperation("删除飞行无人机站点")
    public Result<Boolean> deleteFlightUavStation(@RequestBody @Validated @NotNull(message = "ID不能为空")
                                                @ApiParam(value = "飞行无人机站点ID DTO", required = true) IdDTO idDTO) {
        boolean success = flightUavStationService.deleteFlightUavStation(idDTO.getId());
        return Result.success(success);
    }

    @PostMapping("/queryByPolygon")
    @ApiOperation("按区域查询站点距离并排序 - 当提供polygonWkt时按距离排序，否则按创建时间倒序; 支持按飞行器型号编号(flightUavBmModelNo)和飞行器型号编号列表(flightUavBmList)过滤; 返回包含飞行器和机型信息")
    public Result<CommonPage<StationDistanceVO>> queryStationsByPolygon(@RequestBody @Validated StationDistanceQueryDTO queryDTO) {
        // 设置租户ID
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        CommonPage<StationDistanceVO> page = flightUavStationService.pageQueryStationsByPolygon(queryDTO);
        return Result.success(page);
    }
} 