package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsStatDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsStatQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.algorithm.EventMonitorService;
import com.deepinnet.skyflow.operationcenter.service.sse.FlightEventSseEmitterManager;
import com.deepinnet.skyflow.operationcenter.service.sse.SseEventsInterestKey;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> wong
 * @create 2024/11/27 11:39
 * @Description
 */
@RestController
@Api(tags = "飞行任务事件管理")
@Validated
@RequestMapping("/event")
public class FlightEventController {

    @Resource
    private FlightEventSseEmitterManager eventSseEmitterManager;

    @Resource
    private EventMonitorService eventMonitorService;

    @ApiOperation("无人机任务事件SSE")
    @GetMapping("/sse/task/list")
    public SseEmitter subscribe(@RequestParam String taskCode) {
        SseEventsInterestKey sseClient = new SseEventsInterestKey();
        sseClient.setTaskId(taskCode);
        sseClient.setUserId(UserUtil.getUserNo());
        String sseKey = sseClient.buildKey();
        SseEmitter emitter = eventSseEmitterManager.createEmitter(sseKey);
        // 异步发送已有事件，避免阻塞 servlet 线程
        CompletableFuture.runAsync(() -> {
            //查询任务目前已经存在的事件列表
            List<FlightEventsDTO> flightEventsDTOS = eventMonitorService.queryListByFlightTaskCode(taskCode);
            eventSseEmitterManager.sendData(sseKey, flightEventsDTOS);
        });
        return emitter;
    }

    @ApiOperation("租户下所有事件SSE")
    @GetMapping("/sse/tenant/list")
    public SseEmitter subscribe() {
        String tenantId = TenantIdUtil.getTenantId();
        SseEventsInterestKey sseClient = new SseEventsInterestKey();
        sseClient.setUserId(UserUtil.getUserNo());
        sseClient.setTenantId(tenantId);
        String sseKey = sseClient.buildKey();
        SseEmitter emitter = eventSseEmitterManager.createEmitter(sseKey);
        CompletableFuture.runAsync(() -> {
            //查询租户下目前已经存在的事件列表
            FlightEventsQueryDTO queryDTO = new FlightEventsQueryDTO();
            queryDTO.setTenantId(tenantId);
            queryDTO.setPageSize(200);
            queryDTO.setPageNum(1);
            List<FlightEventsDTO> list = eventMonitorService.pageQuery(queryDTO).getList();

            eventSseEmitterManager.sendData(sseKey, list);
        });
        return emitter;
    }

    @ApiOperation("查询事件统计排行")
    @PostMapping("/stat")
    public Result<List<FlightEventsStatDTO>> queryFlightEventsStat(@RequestBody FlightEventsStatQueryDTO queryDTO) {
        List<FlightEventsStatDTO> list = eventMonitorService.queryFlightEventsStat(queryDTO);
        return Result.success(list);
    }

    @ApiOperation("根据计划id查询事件列表")
    @GetMapping("/list/plan")
    public Result<List<FlightEventsDTO>> queryFlightEventsByPlanId(@RequestParam String planId) {
        List<FlightEventsDTO> list = eventMonitorService.queryListByFlightTaskCode(planId);
        return Result.success(list);
    }
}