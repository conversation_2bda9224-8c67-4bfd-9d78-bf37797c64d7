package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.UserDetailDTO;
import com.deepinnet.infra.api.dto.UserQueryDTO;
import com.deepinnet.skyflow.operationcenter.biz.FlightDemandBizManager;
import com.deepinnet.skyflow.operationcenter.dto.DemandSyncPlanBindDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.client.FlightUserClient;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.util.enums.UserTypeEnum;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import com.deepinnet.skyflow.operationcenter.service.context.DataPermission;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> wong
 * @create 2024/11/27 11:39
 * @Description
 */
@RestController
@Api(tags = "飞行需求管理")
@Validated
@RequestMapping("/demand")
public class FlightDemandController {

    @Resource
    private FlightDemandBizManager demandBizManager;

    @Resource
    private FlightUserClient userClient;

    @PostMapping("/create")
    @ApiOperation("创建飞行需求")
    public Result<String> createFlightDemand(@RequestBody @Valid FlightDemandDTO flightDemandDTO) {
        String tenantId = TenantIdUtil.getTenantId();
        flightDemandDTO.setTenantId(tenantId);
        String demandCode = demandBizManager.saveFlightDemand(flightDemandDTO);
        return Result.success(demandCode);
    }

    @GetMapping("/customer/get")
    @ApiOperation("客户平台-根据需求编号获取需求详情")
    @DataPermission
    public Result<FlightDemandVO> getCustomerFlightDemandByNo(@RequestParam @NotBlank(message = "需求编号不能为空")
                                                      @ApiParam(value = "需求编号", required = true) String demandNo) {
        FlightDemandVO flightDemandVO = demandBizManager.getFlightDemandByNo(demandNo);
        return Result.success(flightDemandVO);
    }

    @GetMapping("/op/get")
    @ApiOperation("服务平台、运营管理平台-根据需求编号获取需求详情")
    public Result<FlightDemandVO> getFlightDemandByNo(@RequestParam @NotBlank(message = "需求编号不能为空")
                                                      @ApiParam(value = "需求编号", required = true) String demandNo) {
        FlightDemandVO flightDemandVO = demandBizManager.getFlightDemandByNo(demandNo);
        //判断权限
        UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setUserNos(List.of(UserUtil.getUserNo()));
        List<UserDetailDTO> userDetailList = userClient.getUserDetailList(userQueryDTO);
        UserDetailDTO loginUser = userDetailList.get(0);
        String userType = loginUser.getUserType();
        //服务商请求时需要判断是不是匹配到的服务商
        if (Objects.equals(userType, UserTypeEnum.SUPPLIER.getCode())) {
            if (flightDemandVO.getServiceProviderNo().equals(loginUser.getUserNo())) {
                return Result.success(flightDemandVO);
            }
        }
        //运营后台的不需要校验
        if (Objects.equals(userType, UserTypeEnum.OPERATION.getCode())) {
            return Result.success(flightDemandVO);
        }
        return Result.success(null);
    }

    @PostMapping("/page/customer/matchmaking")
    @ApiOperation("客户平台-撮合场景-分页查询飞行需求")
    public Result<CommonPage<FlightDemandDTO>> pageQueryFlightDemandCustomerMatchmaking(@RequestBody @Valid FlightDemandQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();
        queryDTO.setTenantId(tenantId);
        queryDTO.setPublisherNo(UserUtil.getUserNo());
        CommonPage<FlightDemandDTO> page = demandBizManager.pageQueryFlightDemand(queryDTO);
        return Result.success(page);
    }

    @PostMapping("/page/customer/onenet")
    @ApiOperation("客户平台-一网统飞场景-分页查询飞行需求")
    @DataPermission
    public Result<CommonPage<FlightDemandDTO>> pageQueryFlightDemandCustomerOneNet(@RequestBody @Valid FlightDemandQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();
        queryDTO.setTenantId(tenantId);
        CommonPage<FlightDemandDTO> page = demandBizManager.pageQueryFlightDemand(queryDTO);
        return Result.success(page);
    }

    @PostMapping("/page/service")
    @ApiOperation("服务平台-分页查询飞行需求")
    public Result<CommonPage<FlightDemandDTO>> pageQueryFlightDemandService(@RequestBody @Valid FlightDemandQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();
        queryDTO.setTenantId(tenantId);
        queryDTO.setServiceProviderNo(UserUtil.getUserNo());
        CommonPage<FlightDemandDTO> page = demandBizManager.pageQueryFlightDemand(queryDTO);
        return Result.success(page);
    }

    @PostMapping("/page/op/manage")
    @ApiOperation("运营管理平台-分页查询飞行需求")
    public Result<CommonPage<FlightDemandDTO>> pageQueryFlightDemandOpManage(@RequestBody @Valid FlightDemandQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();
        queryDTO.setTenantId(tenantId);
        CommonPage<FlightDemandDTO> page = demandBizManager.pageQueryFlightDemand(queryDTO);
        return Result.success(page);
    }

    @ApiOperation(value = "[飞行接口] => YF同步飞行需求和计划绑定关系")
    @PostMapping("/sync/plan/bind")
    public Result<Boolean> syncPlanBind(@RequestBody DemandSyncPlanBindDTO dto) {
        return Result.success(demandBizManager.syncPlanBind(dto));
    }

    @ApiOperation(value = "[飞行接口] => 根据计划id查询需求")
    @GetMapping("/get/by/plan/id")
    public Result<FlightDemandDTO> getFlightDemandByPlanId(@RequestParam String planId) {
        return Result.success(demandBizManager.getFlightDemandByPlanId(planId));
    }


}
