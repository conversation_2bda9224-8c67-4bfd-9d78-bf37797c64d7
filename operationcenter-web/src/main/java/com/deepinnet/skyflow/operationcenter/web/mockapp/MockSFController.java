package com.deepinnet.skyflow.operationcenter.web.mockapp;

import com.deepinnet.digitaltwin.common.util.JsonConvertUtil;
import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.skyflow.operationcenter.dto.FlightAlgorithmMonitorCreateDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightTaskNotifyDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightEventStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightEventTypeEnum;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Description: mock算法服务
 * Date: 2025/5/14
 * Author: lijunheng
 */
@RestController
@ConditionalOnProperty(name = "mock.sf", havingValue = "true")
@Slf4j
public class MockSFController {

    @Value("${algorithm.redis.stream.topic}")
    private String stream;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    // 使用Guava Cache存储任务信息
    private final Cache<String, TaskInfo> taskCache = CacheBuilder.newBuilder()
            .expireAfterWrite(2, TimeUnit.HOURS)
            .build();

    // 线程池用于定时发送事件
    private final ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(5);

    // 任务信息类
    private static class TaskInfo {
        private final String taskId;
        private final String videoUrl;
        private final List<FlightEventTypeEnum> eventTypes;
        private final String tenantId;
        private volatile boolean active = true;

        public TaskInfo(String taskId, String videoUrl, List<FlightEventTypeEnum> eventTypes, String tenantId) {
            this.taskId = taskId;
            this.videoUrl = videoUrl;
            this.eventTypes = eventTypes;
            this.tenantId = tenantId;
        }

        public String getTaskId() {
            return taskId;
        }

        public String getVideoUrl() {
            return videoUrl;
        }

        public List<FlightEventTypeEnum> getEventTypes() {
            return eventTypes;
        }

        public String getTenantId() {
            return tenantId;
        }

        public boolean isActive() {
            return active;
        }

        public void setActive(boolean active) {
            this.active = active;
        }
    }

    //接收无人机视频
    @PostMapping("/create/flight/monitor/task")
    public String createFlightMonitorTask(@RequestBody FlightAlgorithmMonitorCreateDTO flightAlgorithmMonitorCreateDTO) {
        String taskId = flightAlgorithmMonitorCreateDTO.getFlightTaskId();

        // 创建任务信息并存入缓存
        TaskInfo taskInfo = new TaskInfo(
                taskId,
                flightAlgorithmMonitorCreateDTO.getVideoUrl(),
                flightAlgorithmMonitorCreateDTO.getEventTypeList(),
                flightAlgorithmMonitorCreateDTO.getTenantId()
        );

        taskCache.put(taskId, taskInfo);

        log.info("创建飞行监控任务: {}", taskId);

        // 启动定时任务，每20秒发送一个事件，最多发送50个
        for (int i = 0; i < 50; i++) {
            final int index = i;
            scheduledExecutorService.schedule(() -> {
                try {
                    TaskInfo currentTask = taskCache.getIfPresent(taskId);
                    if (currentTask != null && currentTask.isActive()) {
                        sendMockEvent(currentTask);
                        // 达到50个事件后，停止发送
                        if (index == 49) {
                            currentTask.setActive(false);
                        }
                    }
                } catch (Exception e) {
                    log.error("发送模拟事件异常", e);
                }
            }, 5 + 20 * index, TimeUnit.SECONDS);
        }
        scheduledExecutorService.schedule(() -> taskCache.invalidate(taskId), 50 * 20, TimeUnit.SECONDS);
        return "ok";
    }

    //接收无人机飞行任务结束通知，中断结束掉往redis里放事件
    @PostMapping("/notify/flight/task/end")
    public Boolean notifyFlightTaskEnd(@RequestBody FlightTaskNotifyDTO flightTaskNotifyDTO) {
        String taskId = flightTaskNotifyDTO.getFlightTaskId();
        TaskInfo taskInfo = taskCache.getIfPresent(taskId);

        if (taskInfo != null) {
            log.info("结束飞行监控任务: {}", taskId);
            taskInfo.setActive(false);
        }

        return true;
    }

    /**
     * 发送模拟事件到Redis流
     */
    private void sendMockEvent(TaskInfo taskInfo) {
        if (!taskInfo.isActive() || taskInfo.getEventTypes().isEmpty()) {
            return;
        }

        // 随机选择一个事件类型
        FlightEventTypeEnum eventType = taskInfo.getEventTypes().get(
                (int) (Math.random() * taskInfo.getEventTypes().size())
        );

        FlightEventsDTO eventDTO = new FlightEventsDTO();
        eventDTO.setId(IdGenerateUtil.getId("flight_event_sf"));
        eventDTO.setEventType(eventType);
        eventDTO.setEventTime(LocalDateTime.now());
        eventDTO.setEventName(eventType.getDesc());
        eventDTO.setDescription("检测到" + eventType.getDesc() + "事件");

        // 生成随机坐标
        double longitude = 114.0 + Math.random();
        double latitude = 22.5 + Math.random();

        // 由于类型不匹配，我们先跳过设置eventPoint
        // 在实际项目中，需要确保类型兼容或者进行适当的类型转换

        eventDTO.setEventLocation("深圳市南山区科技园");
        eventDTO.setDuration(1);
        eventDTO.setStatus(FlightEventStatusEnum.RUNNING);
        eventDTO.setFlightTaskCode(taskInfo.getTaskId());
        eventDTO.setTenantId(taskInfo.getTenantId());
        PointCoordinate eventPoint = new PointCoordinate(longitude, latitude);
        eventDTO.setEventPoint(eventPoint);

        // 模拟发送到Redis流
        try {
            log.info("发送事件到Redis: {}", eventDTO);
            // 将DTO转换为Map
            Map<String, Object> eventMap = JsonConvertUtil.parseJson(JsonConvertUtil.toJsonStr(eventDTO), Map.class);
            redisTemplate.opsForStream().add(stream, eventMap);
        } catch (Exception e) {
            log.error("发送事件到Redis异常", e);
        }
    }
}
