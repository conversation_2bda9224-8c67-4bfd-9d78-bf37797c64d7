package com.deepinnet.skyflow.operationcenter.web;

import cn.hutool.core.util.StrUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.service.file.MinioService;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.deepinnet.skyflow.operationcenter.dto.FileInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.nio.channels.WritableByteChannel;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文件上传下载控制器
 * Date: 2025/5/14
 * Author: lijunheng
 */
@Slf4j
@RestController
@RequestMapping("/api/file")
@Api(tags = "文件管理接口")
public class FileController {

    @Resource
    private MinioService minioService;

    /**
     * 上传文件
     *
     * @param file       文件
     * @return 文件URL
     */
    @PostMapping("/upload")
    @ApiOperation("上传文件")
    public Result<FileInfoDTO> uploadFile(
            @ApiParam(value = "文件", required = true) @RequestParam("file") MultipartFile file) {

        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        String objectName = generateObjectName(fileExtension);

        // 上传文件
        String fileUrl = minioService.uploadFile(file, objectName);

        FileInfoDTO resultDTO = FileInfoDTO.builder()
                .url(fileUrl)
                .filename(originalFilename)
                .objectName(objectName)
                .size(file.getSize())
                .contentType(file.getContentType())
                .bucketName(minioService.getDefaultBucketName())
                .build();

        return Result.success(resultDTO);
    }

    /**
     * 下载文件
     *
     * @param objectName 对象名称
     * @param filename   下载时的文件名（可选，默认为对象名称）
     * @return 文件流
     */
    @GetMapping("/download/objectName")
    @ApiOperation("下载文件")
    public ResponseEntity<StreamingResponseBody> downloadFileByObjectName(
            @ApiParam(value = "对象名称", required = true) @RequestParam("objectName") String objectName,
            @ApiParam(value = "下载文件名（可选）") @RequestParam(value = "filename", required = false) String filename) {

        try {
            // 设置文件名
            String downloadFilename = filename;

            if (StringUtils.isBlank(filename)) {
                downloadFilename = objectName.substring(objectName.lastIndexOf(File.separator) + 1);
            }

            // 调用公共下载方法
            return downloadFile(objectName, downloadFilename);
        } catch (Exception e) {
            log.error("文件下载失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * 下载文件
     *
     * @param fileUrl   文件路径
     * @return 文件流
     */
    @GetMapping("/download/fileUrl")
    @ApiOperation("下载文件")
    public ResponseEntity<StreamingResponseBody> downloadFileByUrl(
            @ApiParam(value = "文件路径", required = true) @RequestParam("fileUrl") String fileUrl) {
        try {
            // 解析URL，获取对象名称
            String objectName = extractObjectNameFromUrl(fileUrl);
            if (objectName == null) {
                log.error("无法从URL解析对象名称: {}", fileUrl);
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
            }

            // 设置文件名
            String downloadFilename = objectName.substring(objectName.lastIndexOf(File.separator) + 1);
            
            // 调用公共下载方法
            return downloadFile(objectName, downloadFilename);
        } catch (Exception e) {
            log.error("文件下载失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * 公共下载文件方法
     *
     * @param objectName 对象名称
     * @param filename 下载文件名
     * @return 文件流响应
     */
    private ResponseEntity<StreamingResponseBody> downloadFile(String objectName, String filename) throws Exception {
        // 获取文件输入流
        InputStream inputStream = minioService.downloadFile(objectName);

        // 获取文件大小
        long contentLength = minioService.getFileSize(objectName);

        // 使用StreamingResponseBody实现零拷贝传输
        StreamingResponseBody responseBody = outputStream -> {
            try (
                    // 使用NIO通道传输数据
                    ReadableByteChannel inputChannel = Channels.newChannel(inputStream);
                    WritableByteChannel outputChannel = Channels.newChannel(outputStream)
            ) {
                // 使用transferTo方法实现零拷贝
                ByteBuffer buffer = ByteBuffer.allocateDirect(8192);
                while (inputChannel.read(buffer) != -1) {
                    buffer.flip();
                    outputChannel.write(buffer);
                    buffer.clear();
                }
            } catch (IOException e) {
                log.error("文件传输过程中发生错误: {}", e.getMessage(), e);
            }
        };

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDisposition(ContentDisposition.attachment().filename(filename, StandardCharsets.UTF_8).build());
        headers.setContentLength(contentLength);

        return new ResponseEntity<>(responseBody, headers, HttpStatus.OK);
    }

    /**
     * 从URL中提取对象名称
     *
     * @param fileUrl 文件URL
     * @return 对象名称
     */
    private String extractObjectNameFromUrl(String fileUrl) {
        // 这是一个带ip端口的url地址，例如http(https)://127.0.0.1:8089/deepinnet/file_xxx.jpg，我现在想取其余的部分
        try {
            // 移除URL的协议部分 (http:// 或 https://)
            int protocolEnd = fileUrl.indexOf("://");
            if (protocolEnd > 0) {
                fileUrl = fileUrl.substring(protocolEnd + 3);
            }
            
            // 移除域名和端口部分
            int pathStart = fileUrl.indexOf("/");
            if (pathStart > 0) {
                fileUrl = fileUrl.substring(pathStart + 1);
            }
            
            // 移除可能的bucket名称前缀
            String bucketName = minioService.getDefaultBucketName();
            if (fileUrl.startsWith(bucketName + "/")) {
                fileUrl = fileUrl.substring(bucketName.length() + 1);
            }
            
            return fileUrl;
        } catch (Exception e) {
            log.error("提取对象名称失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取文件URL
     *
     * @param objectName 对象名称
     * @return 文件URL
     */
    @GetMapping("/url")
    @ApiOperation("获取文件URL")
    public Result<String> getFileUrl(
            @ApiParam(value = "对象名称", required = true) @RequestParam("objectName") String objectName) {

        // 获取文件URL
        String fileUrl = minioService.getFileUrl(objectName);
        return Result.success(fileUrl);
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 扩展名
     */
    private String getFileExtension(String filename) {
        if (StrUtil.isBlank(filename)) {
            return "";
        }
        int dotIndex = filename.lastIndexOf(".");
        if (dotIndex == -1) {
            return "";
        }
        return filename.substring(dotIndex);
    }

    /**
     * 生成对象名称
     *
     * @param fileExtension 文件扩展名
     * @return 对象名称
     */
    private String generateObjectName(String fileExtension) {
        // 使用UUID和时间戳生成唯一文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String id = IdGenerateUtil.getId("file");
        return timestamp + File.separator + id + fileExtension;
    }
}
