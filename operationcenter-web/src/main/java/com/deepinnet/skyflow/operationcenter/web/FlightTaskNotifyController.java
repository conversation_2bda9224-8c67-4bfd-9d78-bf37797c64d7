package com.deepinnet.skyflow.operationcenter.web;

import cn.hutool.core.collection.ListUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.biz.FlightTaskNotifyBizManager;
import com.deepinnet.skyflow.operationcenter.dto.FlightAlgorithmMonitorCreateDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightTaskNotifyDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightEventTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> wong
 * @create 2024/11/27 11:39
 * @Description
 */
@RestController
@Api(tags = "飞行任务通知管理")
@Validated
@RequestMapping("/task/notify")
public class FlightTaskNotifyController {

    @Resource
    private FlightTaskNotifyBizManager taskNotifyBizManager;

    @PostMapping("/start")
    @ApiOperation("开始飞行任务")
    public Result<Boolean> startFlightTask(@RequestBody @Valid FlightAlgorithmMonitorCreateDTO monitorCreateDTO) {
        //mock全部时间类型
        monitorCreateDTO.setEventTypeList(ListUtil.toList(
                FlightEventTypeEnum.ILLEGAL_TURN, FlightEventTypeEnum.ILLEGAL_PARKING,
                FlightEventTypeEnum.COMPACTION_LINE, FlightEventTypeEnum.CROSS_RED_LIGHT,
                FlightEventTypeEnum.ROAD_OBJECT, FlightEventTypeEnum.ROAD_WATER));
        taskNotifyBizManager.startFlightTask(monitorCreateDTO);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/end")
    @ApiOperation("通知结束飞行任务")
    public Result<Boolean> endFlightTask(@RequestBody @Valid FlightTaskNotifyDTO flightTaskNotifyDTO) {
        taskNotifyBizManager.endFlightTask(flightTaskNotifyDTO);
        return Result.success(Boolean.TRUE);
    }
}
