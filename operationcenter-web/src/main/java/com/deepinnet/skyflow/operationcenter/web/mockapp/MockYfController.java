package com.deepinnet.skyflow.operationcenter.web.mockapp;

import cn.hutool.core.lang.TypeReference;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.digitaltwin.common.util.JsonConvertUtil;
import com.deepinnet.localdata.integration.http.BaseHttpRequestClient;
import com.deepinnet.localdata.integration.http.HttpMethodEnum;
import com.deepinnet.localdata.integration.http.HttpRequestParam;
import com.deepinnet.localdata.integration.model.output.FlightPlanDetailResponseVO;
import com.deepinnet.localdata.integration.model.output.FlightPlanResponseVO;
import com.deepinnet.localdata.integration.model.output.FlightResponseVO;
import com.deepinnet.localdata.integration.model.outsidebean.FlightDemandCreateRequest;
import com.deepinnet.localdata.integration.model.outsidebean.FlightDemandCreateResponse;
import com.deepinnet.localdata.integration.model.outsidebean.SupplierDetailDTO;
import com.deepinnet.localdata.integration.model.outsidebean.YuanFeiCommonResult;
import com.deepinnet.skyflow.operationcenter.dto.DemandSyncPlanBindDTO;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Description: mock鸳飞的接口，只有打开了mock.yf=true时，才会生效
 * Date: 2025/5/14
 * Author: lijunheng
 */
@ConditionalOnProperty(name = "mock.yf", havingValue = "true")
@RestController
@Slf4j
public class MockYfController {

    // 使用Guava Cache存储需求和计划信息
    private final Cache<String, DemandInfo> demandCache = CacheBuilder.newBuilder()
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();
    
    private final Cache<String, FlightPlanDetailResponseVO> planCache = CacheBuilder.newBuilder()
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();

    // 线程池用于定时创建计划
    private final ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(3);

    @Autowired
    private MqttPahoMessageHandler mqttOutbound;

    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 需求信息类
    @Data
    private static class DemandInfo {
        private final String code;
        private final String name;
        private final String startTime;
        private final String endTime;
        private final String area;
        private final String centerPoint;
        private final String providerCode;
        private final String providerName;

        public DemandInfo(String code, String name, String startTime, String endTime, String area, String centerPoint, String providerCode, String providerName) {
            this.code = code;
            this.name = name;
            this.startTime = startTime;
            this.endTime = endTime;
            this.area = area;
            this.centerPoint = centerPoint;
            this.providerCode = providerCode;
            this.providerName = providerName;
        }
    }
    
    // 飞行数据消息体类已移至 com.deepinnet.skyflow.operationcenter.web.mockapp.RealTimeFlyDataMsgBody

    //关系是先创建需求，然后按照需求生成计划，然后按计划生成飞行记录，飞行

    //接收需求同步
    @PostMapping("/api/task/demand/insertDemand")
    public YuanFeiCommonResult<FlightDemandCreateResponse> createDemand(@RequestBody FlightDemandCreateRequest flightDemandCreateRequest) {
        String demandCode = flightDemandCreateRequest.getCode();
        DemandInfo demandInfo = getDemandInfo(flightDemandCreateRequest);

        demandCache.put(demandCode, demandInfo);
        log.info("创建飞行需求: {}", demandCode);
        
        // 启动定时任务，每30秒创建一个计划，一共创建5个
        for (int i = 0; i < 5; i++) {
            final int planIndex = i;
            scheduledExecutorService.schedule(() -> {
                try {
                    createPlanForDemand(demandInfo, planIndex);
                } catch (Exception e) {
                    log.error("创建计划异常", e);
                }
            }, 75 * (i + 1), TimeUnit.SECONDS);
        }
        
        // 构建响应
        FlightDemandCreateResponse response = new FlightDemandCreateResponse();
        response.setId(UUID.randomUUID().toString());
        response.setCode(demandCode);
        
        YuanFeiCommonResult<FlightDemandCreateResponse> result = new YuanFeiCommonResult<>();
        result.setStatus(200);
        result.setMessage("success");
        result.setData(response);
        
        return result;
    }

    //提供时空平台计划查询接口
    @GetMapping("/api/plan/flyRelation/index/queryList")
    public YuanFeiCommonResult<FlightResponseVO> queryTodayFlightPlan(HttpServletRequest request) {
        // 从请求参数中获取分页信息
        String pageNoStr = request.getParameter("pageNo");
        String pageSizeStr = request.getParameter("pageSize");
        
        int pageNo = pageNoStr != null ? Integer.parseInt(pageNoStr) : 1;
        int pageSize = pageSizeStr != null ? Integer.parseInt(pageSizeStr) : 10;

        // 收集所有计划
        List<FlightPlanResponseVO> planList = planCache.asMap().values().stream()
                .map(this::convertVO).collect(Collectors.toList());

        // 分页处理
        int total = planList.size();
        int fromIndex = (pageNo - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, total);
        
        List<FlightPlanResponseVO> pagedList = fromIndex < total ?
                planList.subList(fromIndex, toIndex) : new ArrayList<>();
        
        FlightResponseVO responseVO = new FlightResponseVO();
        responseVO.setTotal(total);
        responseVO.setTotalOther(0);
        responseVO.setRows(pagedList);
        
        YuanFeiCommonResult<FlightResponseVO> result = new YuanFeiCommonResult<>();
        result.setStatus(200);
        result.setMessage("success");
        result.setData(responseVO);
        
        return result;
    }

    @GetMapping("/api/plan/flightPlan/detail/{planId}")
    public YuanFeiCommonResult<FlightPlanDetailResponseVO> queryFlightPlanDetail(@PathVariable("planId") String planId) {
        FlightPlanDetailResponseVO detailVO = planCache.getIfPresent(planId);
        
        YuanFeiCommonResult<FlightPlanDetailResponseVO> result = new YuanFeiCommonResult<>();
        
        if (detailVO != null) {
            result.setStatus(200);
            result.setMessage("success");
            result.setData(detailVO);
        } else {
            result.setStatus(-1);
            result.setMessage("plan not found");
        }
        
        return result;
    }

    @GetMapping("/api/device/live-address/getBySn")
    public YuanFeiCommonResult<String> queryLiveVideoUrl(HttpServletRequest request) {
        // 从请求参数中获取SN码
        String sn = request.getParameter("sn");
        
        // 随机生成一个视频URL
        String videoUrl = "rtmp://live.example.com/uav/" + (sn != null ? sn : UUID.randomUUID().toString());
        
        YuanFeiCommonResult<String> result = new YuanFeiCommonResult<>();
        result.setStatus(200);
        result.setMessage("success");
        result.setData(videoUrl);
        
        return result;
    }

    private static DemandInfo getDemandInfo(FlightDemandCreateRequest flightDemandCreateRequest) {
        SupplierDetailDTO supplierDetail = flightDemandCreateRequest.getSupplierDetail();
        // 创建需求信息并存入缓存
        DemandInfo demandInfo = new DemandInfo(
                flightDemandCreateRequest.getCode(),
                flightDemandCreateRequest.getName(),
                flightDemandCreateRequest.getStartTime(),
                flightDemandCreateRequest.getEndTime(),
                flightDemandCreateRequest.getArea(),
                flightDemandCreateRequest.getCenterPoint(),
                supplierDetail.getUserNo(),
                supplierDetail.getCompanyName()
        );
        return demandInfo;
    }

    /**
     * 为需求创建飞行计划
     */
    private void createPlanForDemand(DemandInfo demandInfo, int index) {
        String planId = IdGenerateUtil.getId("PLAN");

        FlightPlanDetailResponseVO planVO = new FlightPlanDetailResponseVO();
        planVO.setId(planId);
        planVO.setTaskId("TASK" + (1000 + index));
        planVO.setPlanNo(planId);
        planVO.setName(demandInfo.getName() + "-计划" + (index + 1));
        planVO.setPlanState("5"); // 已审批
        planVO.setStartDate(demandInfo.getStartTime());
        planVO.setEndDate(demandInfo.getEndTime());
        planVO.setStartTime("08:00");
        planVO.setEndTime("18:00");
        planVO.setFlightAreaPoints(demandInfo.getArea());
        planVO.setStatus(1); // 正常状态

        // 设置无人机信息
        List<FlightPlanDetailResponseVO.FlightPlanUav> uavList = new ArrayList<>();
        FlightPlanDetailResponseVO.FlightPlanUav uav = new FlightPlanDetailResponseVO.FlightPlanUav();
        uav.setPlanId(planId);
        uav.setUavId("UAV" + (2000 + index));
        uav.setUavName("大疆精灵" + (index + 1));
        uav.setUavTypeId("TYPE" + (100 + index));
        uav.setUavTypeName("Mavic Air 2");
        uav.setReg("B123" + index);
        uav.setUavTypeMannedType("2");
        uav.setAutopilotCode("SN111");
        uavList.add(uav);
        planVO.setFlightPlanUavs(uavList);

        // 设置飞手信息
        List<FlightPlanDetailResponseVO.FlightPlanFlyer> flyerList = new ArrayList<>();
        FlightPlanDetailResponseVO.FlightPlanFlyer flyer = new FlightPlanDetailResponseVO.FlightPlanFlyer();
        flyer.setPlanId(planId);
        flyer.setFlyerId("FLYER" + (3000 + index));
        flyer.setFlyerName("飞手" + (index + 1));
        flyer.setMobilePhone("1381234" + (1000 + index));
        flyerList.add(flyer);
        planVO.setFlightPlanFlyers(flyerList);

        // 设置空域信息
        List<FlightPlanDetailResponseVO.FlightPlanAirspace> airspaceList = new ArrayList<>();
        FlightPlanDetailResponseVO.FlightPlanAirspace airspace = new FlightPlanDetailResponseVO.FlightPlanAirspace();
        airspace.setPlanId(planId);
        airspace.setAirspaceId("AIRSPACE" + (4000 + index));
        airspace.setAirspaceName("测试空域" + (index + 1));
        airspace.setMinHeight(0);
        airspace.setMaxHeight(120);
        airspaceList.add(airspace);
        planVO.setFlightPlanAirspaces(airspaceList);

        // 根据buildFlightPlanDO方法需要的其他字段进行填充
        planVO.setRequirementNo(demandInfo.getCode()); // 需求编号
        planVO.setCrtTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))); // 创建时间
        planVO.setCrtUserId("USER" + (5000 + index)); // 创建用户ID
        planVO.setCrtUserName("测试用户" + (index + 1)); // 创建用户名称
        planVO.setUserName("申请用户" + (index + 1)); // 申请用户名称
        planVO.setRelatedId("REL" + (6000 + index)); // 关联ID
        planVO.setApplyType("3"); // 申请类型

        // 设置降落点信息
        List<String> landingIds = new ArrayList<>();
        landingIds.add("LANDING" + (7000 + index));
        planVO.setLandingIds(landingIds);

        // 设置航线类型的空域ID
        planVO.setAirspaceId("LINE" + (8000 + index));

        // 设置团队信息
        planVO.setTeamId(demandInfo.getProviderCode());
        planVO.setTeamName(demandInfo.getProviderName());

        // 保存计划
        planCache.put(planId, planVO);

        //将需求绑定关系同步给撮合平台
        BaseHttpRequestClient httpRequestClient = new BaseHttpRequestClient();
        DemandSyncPlanBindDTO bindDTO = new DemandSyncPlanBindDTO();
        bindDTO.setFlightDemandCode(demandInfo.getCode());
        bindDTO.setPlanId(planId);
        HttpRequestParam httpRequestParam = HttpRequestParam.builder()
                .address("http://localhost:8080")
                .action("/demand/sync/plan/bind")
                .method(HttpMethodEnum.POST)
                .requestBody(JsonConvertUtil.toJsonStr(bindDTO))
                .build();
        httpRequestClient.httpRequest(httpRequestParam, new TypeReference<Result<Boolean>>() {
        });

        log.info("创建飞行计划: {}, 需求: {}", planId, demandInfo.getCode());
        
        // 启动飞行记录模拟
        scheduledExecutorService.schedule(() -> {
            try {
                startFlightSimulation(planVO);
            } catch (Exception e) {
                log.error("启动飞行模拟异常", e);
            }
        }, 10, TimeUnit.SECONDS);
    }

    private FlightPlanResponseVO convertVO(FlightPlanDetailResponseVO detailVO) {
        FlightPlanResponseVO responseVO = new FlightPlanResponseVO();
        responseVO.setId(detailVO.getId());
        responseVO.setPlanNo(detailVO.getPlanNo());
        responseVO.setName(detailVO.getName());
        responseVO.setPlanState(detailVO.getPlanState());
        responseVO.setStartDate(detailVO.getStartDate());
        responseVO.setEndDate(detailVO.getEndDate());
        responseVO.setStartTime(detailVO.getStartTime());
        responseVO.setEndTime(detailVO.getEndTime());
        responseVO.setTeamId(detailVO.getTeamId());
        responseVO.setTeamName(detailVO.getTeamName());
        List<FlightPlanResponseVO.FlightPlanUav> flightPlanUavs = new ArrayList<>();
        for (FlightPlanDetailResponseVO.FlightPlanUav uav : detailVO.getFlightPlanUavs()) {
            FlightPlanResponseVO.FlightPlanUav flightPlanUav = new FlightPlanResponseVO.FlightPlanUav();
            flightPlanUav.setPlanId(uav.getPlanId());
            flightPlanUav.setUavId(uav.getUavId());
            flightPlanUav.setUavTypeId(uav.getUavTypeId());
            flightPlanUav.setUavName(uav.getUavName());
            flightPlanUavs.add(flightPlanUav);
        }
        responseVO.setFlightPlanUavs(flightPlanUavs);
        return responseVO;
    }
    
    /**
     * 开始飞行模拟，生成飞行记录
     */
    private void startFlightSimulation(FlightPlanDetailResponseVO planVO) {
        if (planVO.getFlightPlanUavs() == null || planVO.getFlightPlanUavs().isEmpty()) {
            return;
        }
        
        String planId = planVO.getId();
        String uavId = planVO.getFlightPlanUavs().get(0).getUavId();
        String flightId = IdGenerateUtil.getId("FLIGHT");

        // 解析中心点坐标
        String[] centerPoints = planVO.getFlightAreaPoints().split("\\|")[0].split(",");
        double baseLon = Double.parseDouble(centerPoints[0]);
        double baseLat = Double.parseDouble(centerPoints[1]);

        //需要开始、结束的标记数据
        // 模拟飞行状态变化：0-地面 1-起飞/进入 2-航线 3-返航 4-降落
        int[] states = {0, 0, 1, 1, 2, 2, 2, 3, 4, 4};
        
        for (int i = 0; i < states.length; i++) {
            final int stateIndex = i;
            
            scheduledExecutorService.schedule(() -> {
                int state = states[stateIndex];
                
                // 创建飞行数据
                RealTimeFlyDataMsgBody flyData = new RealTimeFlyDataMsgBody();
                flyData.setUavId(uavId);
                flyData.setFlightId(flightId);
                flyData.setPlanId(planId);
                flyData.setState(state);
                flyData.setCpuid(IdGenerateUtil.getId("fly_record"));
                
                // 根据状态设置不同的位置
                double offsetLon = (Math.random() - 0.5) * 0.01;
                double offsetLat = (Math.random() - 0.5) * 0.01;
                
                flyData.setLon((long) ((baseLon + offsetLon) * 10000000));
                flyData.setLat((long) ((baseLat + offsetLat) * 10000000));
                flyData.setAlt(100 + (int) (Math.random() * 20));
                flyData.setHeight(80 + (int) (Math.random() * 20));
                flyData.setRelativeHeight(80 + (int) (Math.random() * 20));
                flyData.setSpeed((int) (10 + Math.random() * 5));
                flyData.setTimestamp(System.currentTimeMillis());

                // 发送飞行数据
                sendRecord(flyData);
                
            }, 10 + i * 30, TimeUnit.SECONDS); // 每30秒发送一次状态
        }
    }

    /**
     * 飞行记录同步MQTT
     */
    private void sendRecord(RealTimeFlyDataMsgBody body) {
        try {
            // 将飞行数据转换为JSON字符串
            String payload = objectMapper.writeValueAsString(body);
            
            // 构建MQTT消息
            String topic = "/jtyy/realtime/fly/uav/";
            Message<String> message = MessageBuilder
                    .withPayload(payload)
                    .setHeader(MqttHeaders.TOPIC, topic)
                    .setHeader("mqtt_receivedTopic", topic)
                    .setHeader(MqttHeaders.QOS, 1) // QoS 1 - 至少一次
                    .build();
            
            // 发送MQTT消息
            mqttOutbound.handleMessage(message);
            
            log.info("发送飞行记录: planId={}, state={}, topic={}", body.getPlanId(), body.getState(), topic);
        } catch (JsonProcessingException e) {
            log.error("飞行数据序列化失败", e);
        } catch (Exception e) {
            log.error("MQTT消息发送失败", e);
        }
    }
}
