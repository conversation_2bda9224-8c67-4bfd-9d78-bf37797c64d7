package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.IdDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListBatchOperationDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListGroupQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.WhiteListBatchReplaceDTO;
import com.deepinnet.skyflow.operationcenter.service.WhiteListService;
import com.deepinnet.skyflow.operationcenter.service.convert.WhiteListConvert;
import com.deepinnet.skyflow.operationcenter.vo.WhiteListVO;
import com.deepinnet.skyflow.operationcenter.vo.WhiteListGroupVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 白名单控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/white/list")
@Api(tags = "白名单管理")
@Validated
public class WhiteListController {

    @Resource
    private WhiteListService whiteListService;
    
    @Resource
    private WhiteListConvert whiteListConvert;

    @PostMapping("/create")
    @ApiOperation("创建白名单")
    public Result<Integer> createWhiteList(@RequestBody @Valid WhiteListDTO whiteListDTO) {
        Integer id = whiteListService.saveWhiteList(whiteListDTO);
        return Result.success(id);
    }

    @PostMapping("/update")
    @ApiOperation("更新白名单")
    public Result<Boolean> updateWhiteList(@RequestBody @Valid WhiteListDTO whiteListDTO) {
        boolean success = whiteListService.updateWhiteList(whiteListDTO);
        return Result.success(success);
    }

    @PostMapping("/get")
    @ApiOperation("根据ID获取白名单")
    public Result<WhiteListVO> getWhiteListById(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                              @ApiParam(value = "白名单ID", required = true) IdDTO id) {
        WhiteListDTO whiteListDTO = whiteListService.getWhiteListById(id.getId());
        if (whiteListDTO == null) {
            return Result.success(null);
        }
        WhiteListVO whiteListVO = whiteListConvert.convertToVO(whiteListDTO);
        return Result.success(whiteListVO);
    }

    @GetMapping("/get-by-customer-supplier")
    @ApiOperation("根据客户编号和服务商编号获取白名单")
    public Result<WhiteListVO> getWhiteListByCustomerAndSupplier(
            @RequestParam @NotNull(message = "客户编号不能为空") @ApiParam(value = "客户编号", required = true) String customerUserNo,
            @RequestParam @NotNull(message = "服务商编号不能为空") @ApiParam(value = "服务商编号", required = true) String supplierUserNo) {
        WhiteListDTO whiteListDTO = whiteListService.getWhiteListByCustomerAndSupplier(customerUserNo, supplierUserNo);
        if (whiteListDTO == null) {
            return Result.success(null);
        }
        WhiteListVO whiteListVO = whiteListConvert.convertToVO(whiteListDTO);
        return Result.success(whiteListVO);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询白名单")
    public Result<CommonPage<WhiteListVO>> pageQueryWhiteList(@RequestBody @Valid WhiteListQueryDTO queryDTO) {
        CommonPage<WhiteListDTO> page = whiteListService.pageQueryWhiteList(queryDTO);
        List<WhiteListDTO> whiteListDTOList = page.getList();
        List<WhiteListVO> whiteListVOList = whiteListConvert.convertToVOList(whiteListDTOList);
        return Result.success(CommonPage.copyMetaWithNewData(page, whiteListVOList));
    }
    
    @PostMapping("/page-group-by-customer")
    @ApiOperation("按客户分组分页查询白名单")
    public Result<CommonPage<WhiteListGroupVO>> pageQueryWhiteListGroupByCustomer(@RequestBody @Valid WhiteListGroupQueryDTO queryDTO) {
        CommonPage<WhiteListGroupVO> page = whiteListService.pageQueryWhiteListGroupByCustomer(queryDTO);
        return Result.success(page);
    }

    @PostMapping("/delete")
    @ApiOperation("删除白名单")
    public Result<Boolean> deleteWhiteList(@RequestBody @Valid @NotNull(message = "ID不能为空") 
                                         @ApiParam(value = "白名单ID", required = true) IdDTO id) {
        boolean success = whiteListService.deleteWhiteList(id.getId());
        return Result.success(success);
    }
    
    @PostMapping("/batch-operation")
    @ApiOperation("批量操作白名单（删除、新增和更新）")
    public Result<Boolean> batchOperateWhiteLists(@RequestBody @Valid WhiteListBatchOperationDTO batchOperationDTO) {
        boolean success = whiteListService.batchOperateWhiteLists(batchOperationDTO);
        return Result.success(success);
    }
    
    @PostMapping("/batch-replace-by-customer")
    @ApiOperation("按客户编号批量覆盖白名单（先删除该客户所有记录，再批量新增）")
    public Result<Boolean> batchReplaceWhiteListsByCustomer(@RequestBody @Valid WhiteListBatchReplaceDTO batchReplaceDTO) {
        boolean success = whiteListService.batchReplaceWhiteListsByCustomer(batchReplaceDTO);
        return Result.success(success);
    }
} 