package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductStatQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.IdDTO;
import com.deepinnet.skyflow.operationcenter.service.product.FlightProductService;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightProductConvert;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavBmService;
import com.deepinnet.skyflow.operationcenter.service.product.PriceService;
import com.deepinnet.skyflow.operationcenter.vo.FlightProductStatVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightProductVO;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 飞行产品控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/flight/product")
@Api(tags = "飞行产品管理")
@Validated
public class FlightProductController {

    @Resource
    private FlightProductService flightProductService;
    @Resource
    private FlightUavBmService flightUavBmService;
    @Resource
    private PriceService priceService;

    @Resource
    private FlightProductConvert flightProductConvert;

    @PostMapping("/create")
    @ApiOperation("创建飞行产品")
    @Transactional(rollbackFor = Exception.class)
    public Result<String> createFlightProduct(@RequestBody FlightProductDTO flightProductDTO) {
        flightProductDTO.setTenantId(TenantIdUtil.getTenantId());
        String id = flightProductService.createFlightProduct(flightProductDTO);
        return Result.success(id);
    }

    @PostMapping("/update")
    @ApiOperation("更新飞行产品")
    public Result<Boolean> updateFlightProduct(@RequestBody @Valid FlightProductDTO flightProductDTO) {
        flightProductDTO.setTenantId(TenantIdUtil.getTenantId());
        boolean success = flightProductService.updateFlightProductWithRelated(flightProductDTO);
        return Result.success(success);
    }

    @PostMapping("/get")
    @ApiOperation("根据ID获取飞行产品")
    public Result<FlightProductVO> getFlightProductById(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                                        @ApiParam(value = "飞行产品ID DTO", required = true) IdDTO idDTO) {
        FlightProductVO flightProductVO = flightProductService.getFlightProductWithRelatedById(idDTO.getId());
        return Result.success(flightProductVO);
    }

    @GetMapping("/getByNo")
    @ApiOperation("根据产品编号获取飞行产品")
    public Result<FlightProductVO> getFlightProductByNo(@RequestParam @Valid @NotNull(message = "产品编号不能为空")
                                                        @ApiParam(value = "产品编号", required = false) String productNo) {
        FlightProductVO flightProductVO = flightProductService.getFlightProductWithRelatedByNo(productNo);
        return Result.success(flightProductVO);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询飞行产品")
    public Result<CommonPage<FlightProductVO>> pageQueryFlightProduct(@RequestBody @Valid FlightProductQueryDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        CommonPage<FlightProductDTO> page = flightProductService.pageQueryFlightProductWithRelated(queryDTO);
        List<FlightProductVO> flightProductVOList = flightProductConvert.convertToVOList(page.getList());
        return Result.success(CommonPage.copyMetaWithNewData(page, flightProductVOList));
    }


    @PostMapping("/list")
    @ApiOperation("列表")
    public Result<List<FlightProductDTO>> listByProductNoList(@RequestBody  FlightProductQueryDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        List<FlightProductDTO> page = flightProductService.listByProductNoList(queryDTO.getProductNoList());
        return Result.success(page);
    }

    @PostMapping("/delete")
    @ApiOperation("删除飞行产品")
    public Result<Boolean> deleteFlightProduct(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                               @ApiParam(value = "飞行产品ID DTO", required = true) IdDTO idDTO) {
        boolean success = flightProductService.deleteFlightProductWithRelated(idDTO.getId());
        return Result.success(success);
    }
    
    @PostMapping("/categoryStat")
    @ApiOperation("统计机型服务类目（返回树状结构：类目->飞行类型->品牌）")
    public Result<FlightProductStatVO> statCategoryFlightProduct(@RequestBody FlightProductStatQueryDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        FlightProductStatVO result = flightProductService.statCategoryFlightProduct(queryDTO);
        return Result.success(result);
    }
} 