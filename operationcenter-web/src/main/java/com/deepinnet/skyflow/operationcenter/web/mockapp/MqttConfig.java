package com.deepinnet.skyflow.operationcenter.web.mockapp;

import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.messaging.MessageChannel;

/**
 * MQTT配置类
 */
@Configuration
@ConditionalOnProperty(name = "mock.yf", havingValue = "true")
public class MqttConfig {

    @Value("${mqtt.protocol}")
    private String protocol;

    @Value("${mqtt.host}")
    private String host;

    @Value("${mqtt.port}")
    private String port;

    @Value("${mqtt.username}")
    private String username;

    @Value("${mqtt.password}")
    private String password;

    @Bean
    public MqttPahoClientFactory mqttClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(new String[]{protocol + "://" + host + ":" + port});
        options.setUserName(username);
        options.setPassword(password.toCharArray());
        factory.setConnectionOptions(options);
        return factory;
    }

    /**
     * 出站消息通道
     */
    @Bean
    public MessageChannel mqttOutboundChannel() {
        return new DirectChannel();
    }

    /**
     * MQTT消息处理器
     */
    @Bean
    @ServiceActivator(inputChannel = "mqttOutboundChannel")
    public MqttPahoMessageHandler mqttOutbound() {
        // 添加客户端ID后缀，确保唯一性
        String clientIdWithSuffix = "skyflow-operation-center-" + System.currentTimeMillis();
        MqttPahoMessageHandler messageHandler = new MqttPahoMessageHandler(clientIdWithSuffix, mqttClientFactory());
        messageHandler.setAsync(true); // 异步发送
        messageHandler.setDefaultQos(1); // 默认QoS 1
        messageHandler.setDefaultRetained(false); // 默认不保留消息
        return messageHandler;
    }
} 