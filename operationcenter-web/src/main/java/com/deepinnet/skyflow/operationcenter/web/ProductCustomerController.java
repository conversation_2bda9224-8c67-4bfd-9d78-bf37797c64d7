package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.ProductCustomerClient;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.vo.CustomerInfoVO;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> wong
 * @create 2025/4/19 15:20
 * @Description
 */

@RestController
@RequestMapping("/user/customer")
@Api(tags = "产品需求方接口")
@Validated
public class ProductCustomerController {

    @Resource
    private ProductCustomerClient productCustomerClient;

    @PostMapping("/save")
    @ApiOperation("保存产品需求方信息")
    public Result<String> saveCustomerInfo(@Valid @RequestBody CustomerInfoDTO customerInfoDTO) {
        return productCustomerClient.saveCustomerInfo(customerInfoDTO);
    }

    @PostMapping("/detail")
    @ApiOperation("保存产品需求方详情")
    public Result<CustomerDetailDTO> getCustomerDetail(@RequestBody CustomerQueryDetailDTO queryDetailDTO) {
        return productCustomerClient.getCustomerDetail(queryDetailDTO);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询产品需求方")
    public Result<CommonPage<CustomerInfoVO>> pageQueryCustomers(@Valid @RequestBody CustomerQueryDTO queryDTO) {
        return productCustomerClient.pageQueryCustomers(queryDTO);
    }
}
