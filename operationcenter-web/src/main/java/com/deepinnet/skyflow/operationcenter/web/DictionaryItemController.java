package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryItemDTO;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryItemQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryTreeDTO;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryItemBatchOperationDTO;
import com.deepinnet.skyflow.operationcenter.dto.IdDTO;
import com.deepinnet.skyflow.operationcenter.service.infra.DictionaryItemService;
import com.deepinnet.skyflow.operationcenter.service.convert.DictionaryItemConvert;
import com.deepinnet.skyflow.operationcenter.service.convert.DictionaryTreeConvert;
import com.deepinnet.skyflow.operationcenter.vo.DictionaryItemVO;
import com.deepinnet.skyflow.operationcenter.vo.DictionaryTreeVO;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 字典项控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dictionary/item")
@Api(tags = "字典项管理")
@Validated
public class DictionaryItemController {

    @Resource
    private DictionaryItemService dictionaryItemService;

    @Resource
    private DictionaryItemConvert dictionaryItemConvert;
    
    @Resource
    private DictionaryTreeConvert dictionaryTreeConvert;

    @PostMapping("/create")
    @ApiOperation("创建字典项")
    public Result<Long> createDictionaryItem(@RequestBody @Valid DictionaryItemDTO dictionaryItemDTO) {
        dictionaryItemDTO.setTenantId(TenantIdUtil.getTenantId());
        Long id = dictionaryItemService.saveDictionaryItem(dictionaryItemDTO);
        return Result.success(id);
    }

    @PostMapping("/batch-create")
    @ApiOperation("批量创建字典项")
    public Result<Boolean> batchCreateDictionaryItems(@RequestBody @Valid List<DictionaryItemDTO> dictionaryItemDTOList) {
        if (dictionaryItemDTOList != null) {
            for (DictionaryItemDTO item : dictionaryItemDTOList) {
                item.setTenantId(TenantIdUtil.getTenantId());
            }
        }
        boolean success = dictionaryItemService.batchSaveDictionaryItems(dictionaryItemDTOList);
        return Result.success(success);
    }

    @PostMapping("/update")
    @ApiOperation("更新字典项")
    public Result<Boolean> updateDictionaryItem(@RequestBody @Valid DictionaryItemDTO dictionaryItemDTO) {
        dictionaryItemDTO.setTenantId(TenantIdUtil.getTenantId());
        boolean success = dictionaryItemService.updateDictionaryItem(dictionaryItemDTO);
        return Result.success(success);
    }

    @PostMapping("/get")
    @ApiOperation("根据ID获取字典项")
    public Result<DictionaryItemVO> getDictionaryItemById(@RequestBody @Valid @NotNull(message = "ID不能为空") 
                                            @ApiParam(value = "字典项ID DTO", required = true) IdDTO idDTO) {
        Long id = (idDTO.getId() instanceof Number) ? ((Number) idDTO.getId()).longValue() : null;
        if (id == null) {
            // Handle error: ID is not a number or null
            // return Result.fail(...)
        }
        DictionaryItemDTO dictionaryItemDTO = dictionaryItemService.getDictionaryItemById(id);
        DictionaryItemVO dictionaryItemVO = dictionaryItemConvert.convertToVO(dictionaryItemDTO);
        return Result.success(dictionaryItemVO);
    }

    @GetMapping("/getByCode")
    @ApiOperation("根据字典ID和编码获取字典项")
    public Result<DictionaryItemVO> getDictionaryItemByCode(
            @RequestParam @Valid @NotNull(message = "字典ID不能为空")
            @ApiParam(value = "字典ID", required = true) Long dictionaryId,
            @RequestParam @Valid @NotNull(message = "字典项编码不能为空")
            @ApiParam(value = "字典项编码", required = true) String code) {
        DictionaryItemDTO dictionaryItemDTO = dictionaryItemService.getDictionaryItemByCode(dictionaryId, code);
        DictionaryItemVO dictionaryItemVO = dictionaryItemConvert.convertToVO(dictionaryItemDTO);
        return Result.success(dictionaryItemVO);
    }

    @GetMapping("/listByDictionaryId")
    @ApiOperation("根据字典ID获取字典项列表")
    public Result<List<DictionaryItemVO>> listDictionaryItemsByDictionaryId(
            @RequestParam @Valid @NotNull(message = "字典ID不能为空")
            @ApiParam(value = "字典ID", required = true) Long dictionaryId) {
        List<DictionaryItemDTO> dictionaryItemDTOList = dictionaryItemService.listDictionaryItemsByDictionaryId(dictionaryId);
        List<DictionaryItemVO> dictionaryItemVOList = dictionaryItemConvert.convertToVOList(dictionaryItemDTOList);
        return Result.success(dictionaryItemVOList);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询字典项")
    public Result<CommonPage<DictionaryItemVO>> pageQueryDictionaryItems(@RequestBody @Valid DictionaryItemQueryDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        CommonPage<DictionaryItemDTO> page = dictionaryItemService.pageQueryDictionaryItems(queryDTO);
        List<DictionaryItemVO> dictionaryItemVOList = dictionaryItemConvert.convertToVOList(page.getList());
        return Result.success(CommonPage.copyMetaWithNewData(page, dictionaryItemVOList));
    }

    @PostMapping("/delete")
    @ApiOperation("删除字典项")
    public Result<Boolean> deleteDictionaryItem(@RequestBody @Valid @NotNull(message = "ID不能为空") 
                                        @ApiParam(value = "字典项ID DTO", required = true) IdDTO idDTO) {
        Long id = (idDTO.getId() instanceof Number) ? ((Number) idDTO.getId()).longValue() : null;
        if (id == null) {
            // Handle error: ID is not a number or null
            // return Result.fail(...)
        }
        boolean success = dictionaryItemService.deleteDictionaryItem(id);
        return Result.success(success);
    }

    @GetMapping("/tree")
    @ApiOperation("根据字典类型和租户ID获取字典树")
    public Result<List<DictionaryTreeVO>> getDictionaryTreeByTypeAndTenantId(
            @RequestParam @Valid @NotBlank(message = "字典类型不能为空")
            @ApiParam(value = "字典类型", required = true) String type,
            @RequestParam @Valid @NotBlank(message = "租户ID不能为空")
            @ApiParam(value = "租户ID", required = true) String tenantId) {
        List<DictionaryTreeDTO> dictionaryTreeDTOList = dictionaryItemService.getDictionaryTreeByTypeAndTenantId(type, TenantIdUtil.getTenantId());
        List<DictionaryTreeVO> dictionaryTreeVOList = dictionaryTreeConvert.convertToVOList(dictionaryTreeDTOList);
        return Result.success(dictionaryTreeVOList);
    }

    @GetMapping("/listByTypeAndParentCode")
    @ApiOperation("根据租户ID、字典类型和父编码查询字典项列表")
    public Result<List<DictionaryItemVO>> listDictionaryItemsByTypeAndParentCode(
            @RequestParam @Valid @NotBlank(message = "字典类型不能为空")
            @ApiParam(value = "字典类型", required = true) String type,
            @RequestParam(required = false)
            @ApiParam(value = "父编码", required = true) String parentCode) {
        List<DictionaryItemDTO> dictionaryItemDTOList = dictionaryItemService.listDictionaryItemsByTypeAndParentCode(TenantIdUtil.getTenantId(), type, parentCode);
        List<DictionaryItemVO> dictionaryItemVOList = dictionaryItemConvert.convertToVOList(dictionaryItemDTOList);
        return Result.success(dictionaryItemVOList);
    }

    @PostMapping("/batch-operation")
    @ApiOperation("批量操作字典项（删除和新增）")
    public Result<Boolean> batchOperateDictionaryItems(@RequestBody @Valid DictionaryItemBatchOperationDTO batchOperationDTO) {
        boolean success = dictionaryItemService.batchOperateDictionaryItems(batchOperationDTO);
        return Result.success(success);
    }
} 