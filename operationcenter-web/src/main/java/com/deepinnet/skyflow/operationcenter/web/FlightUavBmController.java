package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmBaseDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportDetailDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportRecordDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportRequestDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmImportResultDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.PageQueryDTO;
import com.deepinnet.skyflow.operationcenter.enums.BizTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavBmService;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavBmImportService;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavBmConvert;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavBmImportDetailConvert;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavBmImportRecordConvert;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavBmVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavBmImportDetailVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavBmImportRecordVO;
import com.deepinnet.tenant.TenantIdUtil;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.util.StringUtils;
import org.apache.commons.io.IOUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.net.URLEncoder;
import java.io.InputStream;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 飞行无人机品牌型号控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/flight/uav/bm")
@Api(tags = "飞行无人机品牌型号管理")
@Validated
public class FlightUavBmController {

    private static final Logger log = LoggerFactory.getLogger(FlightUavBmController.class);

    @Resource
    private FlightUavBmService flightUavBmService;

    @Resource
    private FlightUavBmConvert flightUavBmConvert;

    @Resource
    private FlightUavBmImportService flightUavBmImportService;

    @Resource
    private FlightUavBmImportRecordConvert flightUavBmImportRecordConvert;

    @Resource
    private FlightUavBmImportDetailConvert flightUavBmImportDetailConvert;

    @PostMapping("/create")
    @ApiOperation("创建无人机品牌型号")
    public Result<String> createFlightUavBm(@RequestBody @Valid FlightUavBmDTO flightUavBmDTO) {
        flightUavBmDTO.setTenantId(TenantIdUtil.getTenantId());
        String flightUavBmNo = flightUavBmService.saveFlightUavBm(flightUavBmDTO);
        return Result.success(flightUavBmNo);
    }

    @PostMapping("/update")
    @ApiOperation("更新飞行无人机品牌型号")
    public Result<Boolean> updateFlightUavBm(@RequestBody @Valid FlightUavBmDTO flightUavBmDTO) {
        flightUavBmDTO.setTenantId(TenantIdUtil.getTenantId());
        boolean success = flightUavBmService.updateFlightUavBm(flightUavBmDTO);
        return Result.success(success);
    }

    @PostMapping("/get")
    @ApiOperation("根据ID获取飞行无人机品牌型号")
    public Result<FlightUavBmVO> getFlightUavBmById(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                                  @ApiParam(value = "飞行无人机品牌型号ID", required = true) FlightUavBmBaseDTO id) {

        FlightUavBmDTO flightUavBmDTO = flightUavBmService.getFlightUavBmById(id.getId());
        FlightUavBmVO flightUavBmVO = flightUavBmConvert.convertToVO(flightUavBmDTO);
        return Result.success(flightUavBmVO);
    }

    @GetMapping("/getByNo")
    @ApiOperation("根据型号编码获取飞行无人机品牌型号")
    public Result<FlightUavBmVO> getFlightUavBmByNo(@RequestParam @Valid @NotBlank(message = "型号编码不能为空")
                                                  @ApiParam(value = "型号编码", required = true) String flightUavBmNo) {
        FlightUavBmDTO flightUavBmDTO = flightUavBmService.getFlightUavBmByNo(flightUavBmNo);
        FlightUavBmVO flightUavBmVO = flightUavBmConvert.convertToVO(flightUavBmDTO);
        return Result.success(flightUavBmVO);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询飞行无人机品牌型号")
    public Result<CommonPage<FlightUavBmVO>> pageQueryFlightUavBm(@RequestBody @Valid FlightUavBmQueryDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        CommonPage<FlightUavBmDTO> page = flightUavBmService.pageQueryFlightUavBm(queryDTO);
        List<FlightUavBmVO> flightUavBmVOList = flightUavBmConvert.convertToVOList(page.getList());
        return Result.success(CommonPage.copyMetaWithNewData(page, flightUavBmVOList));
    }

    @PostMapping("/delete")
    @ApiOperation("删除飞行无人机品牌型号")
    public Result<Boolean> deleteFlightUavBm(@RequestBody @Valid @NotNull(message = "ID不能为空") 
                                           @ApiParam(value = "飞行无人机品牌型号ID", required = true) FlightUavBmBaseDTO id) {
        boolean success = flightUavBmService.deleteFlightUavBm(id.getId());
        return Result.success(success);
    }

    /**
     * 导入飞行无人机品牌型号
     */
    @PostMapping("/import")
    @ApiOperation("导入飞行无人机品牌型号")
    public Result<FlightUavBmImportResultDTO> importFlightUavBm(
            @ApiParam(value = "Excel文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "租户ID") @RequestParam(value = "tenantId", required = false) String tenantId) {
        
        log.info("开始导入飞行无人机品牌型号, 文件名: {}, 文件大小: {}KB", file.getOriginalFilename(), file.getSize() / 1024);
        
        try {
            // 校验文件
            if (file == null || file.isEmpty()) {
                return Result.fail(BizErrorCode.FILE_IS_EMPTY.getCode(), BizErrorCode.FILE_IS_EMPTY.getDesc());
            }
            
            // 校验文件类型
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return Result.fail(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请上传Excel文件(后缀名为.xlsx或.xls)");
            }
            
            // 校验文件大小
            if (file.getSize() > 10 * 1024 * 1024) { // 10MB
                return Result.fail(BizErrorCode.FILE_SIZE_EXCEEDS_THE_LIMIT.getCode(), BizErrorCode.FILE_SIZE_EXCEEDS_THE_LIMIT.getDesc());
            }
            
            // 生成导入批次号
            String importBatchNo = IdGenerateUtil.getId(BizTypeEnum.FLIGHT_UAV_BM_IMPORT.getType());
            
            // 准备请求参数
            FlightUavBmImportRequestDTO requestDTO = new FlightUavBmImportRequestDTO();
            requestDTO.setImportBatchNo(importBatchNo);
            requestDTO.setTenantId(tenantId != null ? tenantId : TenantIdUtil.getTenantId());
            requestDTO.setCreator(tenantId != null ? tenantId : TenantIdUtil.getTenantId());
            
            // 导入数据
            FlightUavBmImportResultDTO resultDTO = flightUavBmImportService.importFlightUavBm(file, requestDTO);
            
            // 处理导入结果
            if (resultDTO.getImportStatus() == 1) {
                log.info("导入成功: 批次号={}, 总记录数={}, 成功数={}", 
                    importBatchNo, resultDTO.getTotalCount(), resultDTO.getSuccessCount());
                return Result.success(resultDTO);
            } else {
                // 格式化错误信息
                String detailedErrorMessage = formatDetailedErrorMessage(resultDTO);
                log.warn("导入失败: 批次号={}, 错误原因={}", importBatchNo, detailedErrorMessage);
                
                resultDTO.setFailReason(detailedErrorMessage);
                return Result.fail(BizErrorCode.OPERATION_FAILED.getCode(), detailedErrorMessage);
            }
        } catch (Exception e) {
            String errorMsg = "导入飞行无人机品牌型号失败: " + e.getMessage();
            log.error(errorMsg, e);
            return Result.fail(BizErrorCode.SYSTEM_ERROR.getCode(), errorMsg);
        }
    }
    
    /**
     * 格式化详细错误信息
     *
     * @param resultDTO 导入结果
     * @return 格式化的错误信息
     */
    private String formatDetailedErrorMessage(FlightUavBmImportResultDTO resultDTO) {
        if (resultDTO.getFailReason() == null) {
            return "未知错误";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(resultDTO.getFailReason());
        
        // 添加具体错误详情
        List<FlightUavBmImportResultDTO.FlightUavBmImportErrorDTO> errorList = resultDTO.getErrorList();
        if (errorList != null && !errorList.isEmpty()) {
            int displayCount = Math.min(errorList.size(), 5); // 最多显示5条具体错误
            
            sb.append("，具体错误如下：");
            for (int i = 0; i < displayCount; i++) {
                FlightUavBmImportResultDTO.FlightUavBmImportErrorDTO error = errorList.get(i);
                sb.append("\n第").append(error.getRowNum()).append("行：");
                
                // 添加列名和错误值信息（如果有）
                if (error.getErrorColumnName() != null && error.getErrorValue() != null) {
                    sb.append(error.getErrorColumnName())
                      .append("'").append(error.getErrorValue()).append("'")
                      .append(" - ");
                }
                
                sb.append(error.getErrorReason());
            }
            
            // 如果错误超过显示数量，提示还有更多
            if (errorList.size() > displayCount) {
                sb.append("\n... 还有").append(errorList.size() - displayCount).append("条错误未显示");
            }
        }
        
        return sb.toString();
    }

    @GetMapping("/import/record")
    @ApiOperation("获取导入记录")
    public Result<FlightUavBmImportRecordVO> getImportRecord(@RequestParam @NotBlank(message = "导入批次号不能为空") 
                                                           @ApiParam(value = "导入批次号", required = true) String importBatchNo) {
        FlightUavBmImportRecordDTO recordDTO = flightUavBmImportService.getImportRecord(importBatchNo);
        FlightUavBmImportRecordVO recordVO = flightUavBmImportRecordConvert.convertToVO(recordDTO);
        return Result.success(recordVO);
    }

    @PostMapping("/import/record/page")
    @ApiOperation("分页查询导入记录")
    public Result<CommonPage<FlightUavBmImportRecordVO>> pageQueryImportRecord(@RequestBody @Valid PageQueryDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        CommonPage<FlightUavBmImportRecordDTO> page = flightUavBmImportService.pageQueryImportRecord(queryDTO);
        List<FlightUavBmImportRecordVO> recordVOList = flightUavBmImportRecordConvert.convertToVOList(page.getList());
        return Result.success(CommonPage.copyMetaWithNewData(page, recordVOList));
    }

    @GetMapping("/import/detail/list")
    @ApiOperation("获取导入详情列表")
    public Result<List<FlightUavBmImportDetailVO>> listImportDetail(@RequestParam @NotBlank(message = "导入批次号不能为空") 
                                                                  @ApiParam(value = "导入批次号", required = true) String importBatchNo) {
        List<FlightUavBmImportDetailDTO> detailDTOList = flightUavBmImportService.listImportDetail(importBatchNo);
        List<FlightUavBmImportDetailVO> detailVOList = flightUavBmImportDetailConvert.convertToVOList(detailDTOList);
        return Result.success(detailVOList);
    }

    @PostMapping("/import/detail/page")
    @ApiOperation("分页查询导入详情")
    public Result<CommonPage<FlightUavBmImportDetailVO>> pageQueryImportDetail(
            @RequestParam @NotBlank(message = "导入批次号不能为空") @ApiParam(value = "导入批次号", required = true) String importBatchNo,
            @RequestBody @Valid PageQueryDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        CommonPage<FlightUavBmImportDetailDTO> page = flightUavBmImportService.pageQueryImportDetail(importBatchNo, queryDTO);
        List<FlightUavBmImportDetailVO> detailVOList = flightUavBmImportDetailConvert.convertToVOList(page.getList());
        return Result.success(CommonPage.copyMetaWithNewData(page, detailVOList));
    }

    @GetMapping("/template/download")
    @ApiOperation("下载导入模板")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("机型导入模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        
        // 从资源文件中获取模板并写入响应
        try (InputStream inputStream = this.getClass().getResourceAsStream("/机型导入模板.xlsx")) {
            if (inputStream == null) {
                throw new IOException("模板文件不存在");
            }
            IOUtils.copy(inputStream, response.getOutputStream());
        }
    }
} 