package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.DepartmentClient;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/26 15:04
 * @Description
 */
@RestController
@RequestMapping("/admin/department")
@Api(tags = "部门服务")
public class DepartmentController {

    @Resource
    private DepartmentClient departmentClient;

    @PostMapping("/save")
    @ApiOperation("保存部门")
    public Result<Long> saveDepartment(@RequestBody DepartmentSaveDTO saveDTO) {
        if (saveDTO == null || StringUtils.isBlank(saveDTO.getName())) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        if (saveDTO.getName().length() > 20) {
            throw new BizException(BizErrorCode.LENGTH_EXCEEDS_LIMIT.getCode(), "组织名称不能超过20个字");
        }

        return departmentClient.saveDepartment(saveDTO);
    }

    @PostMapping("/add/member")
    @ApiOperation("添加或者更新部门成员")
    public Result<Boolean> addMember(@RequestBody DepartmentAddMemberDTO addMemberDTO) {
        if (addMemberDTO == null || StringUtils.isBlank(addMemberDTO.getName())) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        if (addMemberDTO.getName().length() > 20) {
            throw new BizException(BizErrorCode.LENGTH_EXCEEDS_LIMIT.getCode(), "组织成员名称不能超过20字");
        }

        return departmentClient.addMember(addMemberDTO);
    }

    @PostMapping("/update")
    @ApiOperation("更新部门")
    public Result<Boolean> updateDepartment(@RequestBody DepartmentUpdateDTO updateDTO) {
        if (updateDTO == null || StringUtils.isBlank(updateDTO.getName())) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        if (updateDTO.getName().length() > 20) {
            throw new BizException(BizErrorCode.LENGTH_EXCEEDS_LIMIT.getCode(), "组织名称不能超过20个字");
        }

        return departmentClient.updateDepartment(updateDTO);
    }

    @GetMapping("/delete")
    @ApiOperation("删除部门")
    public Result<Boolean> deleteDepartment(Long id) {
        if (id == null) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        return departmentClient.deleteDepartment(id);
    }

    @GetMapping("/delete/member")
    @ApiOperation("删除部门成员")
    public Result<Boolean> deleteMember(Long id) {
        if (id == null) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        return departmentClient.deleteMember(id);
    }

    @GetMapping("/tree")
    @ApiOperation("获取部门树")
    public Result<List<DepartmentDTO>> getDepartmentByTree(Long parentId) {
        return departmentClient.getDepartmentByTree(parentId);
    }

    @PostMapping("/subTree")
    @ApiOperation("获取子部门")
    public Result<List<DepartmentDTO>> getSubDepartments(@RequestBody QuerySubDepartmentDTO queryDTO) {
        return departmentClient.getSubDepartments(queryDTO);
    }
}
