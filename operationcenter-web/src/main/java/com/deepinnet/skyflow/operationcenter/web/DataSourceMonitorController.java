package com.deepinnet.skyflow.operationcenter.web;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import com.deepinnet.digitaltwin.common.response.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "数据源监控")
@RestController
@RequestMapping("/api/v1/monitor/datasource")
public class DataSourceMonitorController {

    @Resource
    private DataSource dataSource;

    private static final String PG_ACTIVITY_SQL = 
        "SELECT datname, usename, application_name, client_addr, backend_start, state, query " +
        "FROM pg_stat_activity " +
        "WHERE datname IS NOT NULL";

    private static final String PG_MAX_CONNECTIONS_SQL = "SHOW max_connections";
    
    private static final String PG_CURRENT_CONNECTIONS_SQL = 
        "SELECT count(*) as connection_count FROM pg_stat_activity " +
        "WHERE datname IS NOT NULL";

    @ApiOperation("获取数据源连接池状态")
    @GetMapping("/status")
    public Result<Map<String, Object>> getDataSourceStatus() {
        DynamicRoutingDataSource ds = (DynamicRoutingDataSource) dataSource;
        Map<String, DataSource> currentDataSources = ds.getDataSources();
        Map<String, Object> result = new HashMap<>();

        for (Map.Entry<String, DataSource> entry : currentDataSources.entrySet()) {
            String poolName = entry.getKey();
            DataSource poolDataSource = entry.getValue();
            
            try {
                // 尝试获取真实的数据源
                String className = poolDataSource.getClass().getName();
                log.info("数据源类型: {}", className);
                
                if (className.contains("com.baomidou.dynamic.datasource.ds.ItemDataSource")) {
                    Method method = poolDataSource.getClass().getDeclaredMethod("getRealDataSource");
                    method.setAccessible(true);
                    poolDataSource = (DataSource) method.invoke(poolDataSource);
                }

                if (poolDataSource instanceof HikariDataSource) {
                    HikariDataSource hikariDataSource = (HikariDataSource) poolDataSource;
                    HikariPoolMXBean poolMXBean = hikariDataSource.getHikariPoolMXBean();
                    Map<String, Object> poolInfo = new HashMap<>();
                    
                    // 基本配置信息
                    poolInfo.put("poolName", poolName);
                    poolInfo.put("jdbcUrl", hikariDataSource.getJdbcUrl());
                    poolInfo.put("username", hikariDataSource.getUsername());
                    poolInfo.put("driverClassName", hikariDataSource.getDriverClassName());
                    
                    // 连接池容量信息
                    int maximumPoolSize = hikariDataSource.getMaximumPoolSize();
                    int minimumIdle = hikariDataSource.getMinimumIdle();
                    poolInfo.put("maximumPoolSize", maximumPoolSize);
                    poolInfo.put("minimumIdle", minimumIdle);
                    
                    // 当前使用情况
                    int activeConnections = poolMXBean.getActiveConnections();
                    int idleConnections = poolMXBean.getIdleConnections();
                    int totalConnections = poolMXBean.getTotalConnections();
                    int threadsAwaitingConnection = poolMXBean.getThreadsAwaitingConnection();
                    
                    poolInfo.put("activeConnections", activeConnections);
                    poolInfo.put("idleConnections", idleConnections);
                    poolInfo.put("totalConnections", totalConnections);
                    poolInfo.put("threadsAwaitingConnection", threadsAwaitingConnection);
                    
                    // 计算各种使用率和占用率
                    double usageRate = totalConnections > 0 ? (double) activeConnections / totalConnections * 100 : 0;
                    double poolUtilization = totalConnections > 0 ? (double) totalConnections / maximumPoolSize * 100 : 0;
                    double activeRate = maximumPoolSize > 0 ? (double) activeConnections / maximumPoolSize * 100 : 0;
                    
                    poolInfo.put("usageRate", String.format("%.2f%%", usageRate));  // 活跃连接占总连接数的比率
                    poolInfo.put("poolUtilization", String.format("%.2f%%", poolUtilization));  // 总连接数占最大池大小的比率
                    poolInfo.put("activeRate", String.format("%.2f%%", activeRate));  // 活跃连接占最大池大小的比率
                    
                    // 连接池健康状态
                    poolInfo.put("isRunning", hikariDataSource.isRunning());
                    poolInfo.put("hasWaitingThreads", threadsAwaitingConnection > 0);  // 是否有线程在等待连接
                    
                    // 连接池配置
                    poolInfo.put("connectionTimeout", hikariDataSource.getConnectionTimeout());
                    poolInfo.put("idleTimeout", hikariDataSource.getIdleTimeout());
                    poolInfo.put("maxLifetime", hikariDataSource.getMaxLifetime());
                    
                    // 连接池压力指标
                    String poolPressure = "LOW";
                    if (threadsAwaitingConnection > 0 || activeRate > 90) {
                        poolPressure = "HIGH";
                    } else if (activeRate > 70) {
                        poolPressure = "MEDIUM";
                    }
                    poolInfo.put("poolPressure", poolPressure);

                    // 获取数据库当前连接信息
                    List<Map<String, Object>> dbConnections = getDatabaseConnections(hikariDataSource);
                    poolInfo.put("databaseConnections", dbConnections);

                    // 获取数据库最大连接数和当前连接数
                    Map<String, Object> dbConnectionStats = getDatabaseConnectionStats(hikariDataSource);
                    poolInfo.put("databaseConnectionStats", dbConnectionStats);
                    
                    result.put(poolName, poolInfo);
                }
            } catch (Exception e) {
                log.error("获取连接池信息失败: {}", e.getMessage(), e);
                Map<String, Object> errorInfo = new HashMap<>();
                errorInfo.put("error", "Failed to get pool information: " + e.getMessage());
                result.put(poolName, errorInfo);
            }
        }

        return Result.success(result);
    }

    private List<Map<String, Object>> getDatabaseConnections(HikariDataSource dataSource) {
        List<Map<String, Object>> connections = new ArrayList<>();
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement ps = conn.prepareStatement(PG_ACTIVITY_SQL);
             ResultSet rs = ps.executeQuery()) {
            
            while (rs.next()) {
                Map<String, Object> connection = new HashMap<>();
                connection.put("database", rs.getString("datname"));
                connection.put("user", rs.getString("usename"));
                connection.put("application", rs.getString("application_name"));
                connection.put("clientAddress", rs.getString("client_addr"));
                connection.put("connectionStart", rs.getTimestamp("backend_start"));
                connection.put("state", rs.getString("state"));
                connection.put("currentQuery", rs.getString("query"));
                connections.add(connection);
            }
        } catch (Exception e) {
            log.error("获取数据库连接信息失败: {}", e.getMessage(), e);
        }
        
        return connections;
    }

    private Map<String, Object> getDatabaseConnectionStats(HikariDataSource dataSource) {
        Map<String, Object> stats = new HashMap<>();
        
        try (Connection conn = dataSource.getConnection()) {
            // 获取最大连接数
            try (PreparedStatement ps = conn.prepareStatement(PG_MAX_CONNECTIONS_SQL);
                 ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    int maxConnections = Integer.parseInt(rs.getString(1));
                    stats.put("maxConnections", maxConnections);
                }
            }
            
            // 获取当前连接数
            try (PreparedStatement ps = conn.prepareStatement(PG_CURRENT_CONNECTIONS_SQL);
                 ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    int currentConnections = rs.getInt("connection_count");
                    stats.put("currentConnections", currentConnections);
                    
                    // 计算数据库连接占用率
                    int maxConnections = (Integer) stats.get("maxConnections");
                    double connectionUsageRate = maxConnections > 0 
                        ? (double) currentConnections / maxConnections * 100 
                        : 0;
                    stats.put("connectionUsageRate", String.format("%.2f%%", connectionUsageRate));
                    
                    // 添加连接使用状态评估
                    String connectionStatus = "GOOD";
                    if (connectionUsageRate > 90) {
                        connectionStatus = "CRITICAL";
                    } else if (connectionUsageRate > 75) {
                        connectionStatus = "WARNING";
                    }
                    stats.put("connectionStatus", connectionStatus);
                }
            }
        } catch (Exception e) {
            log.error("获取数据库连接统计信息失败: {}", e.getMessage(), e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }
} 