package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryDTO;
import com.deepinnet.skyflow.operationcenter.dto.DictionaryQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.infra.DictionaryService;
import com.deepinnet.skyflow.operationcenter.service.convert.DictionaryConvert;
import com.deepinnet.skyflow.operationcenter.vo.DictionaryVO;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 字典控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dictionary")
@Api(tags = "字典管理")
@Validated
public class DictionaryController {

    @Resource
    private DictionaryService dictionaryService;

    @Resource
    private DictionaryConvert dictionaryConvert;

    @PostMapping("/create")
    @ApiOperation("创建字典")
    public Result<Long> createDictionary(@RequestBody @Valid DictionaryDTO dictionaryDTO) {
        dictionaryDTO.setTenantId(TenantIdUtil.getTenantId());
        Long id = dictionaryService.saveDictionary(dictionaryDTO);
        return Result.success(id);
    }

    @PostMapping("/update")
    @ApiOperation("更新字典")
    public Result<Boolean> updateDictionary(@RequestBody @Valid DictionaryDTO dictionaryDTO) {
        dictionaryDTO.setTenantId(TenantIdUtil.getTenantId());
        boolean success = dictionaryService.updateDictionary(dictionaryDTO);
        return Result.success(success);
    }

    @PostMapping("/get")
    @ApiOperation("根据ID获取字典")
    public Result<DictionaryVO> getDictionaryById(@RequestBody @Valid @NotNull(message = "ID不能为空") 
                                            @ApiParam(value = "字典ID", required = true) Long id) {
        DictionaryDTO dictionaryDTO = dictionaryService.getDictionaryById(id);
        DictionaryVO dictionaryVO = dictionaryConvert.convertToVO(dictionaryDTO);
        return Result.success(dictionaryVO);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询字典")
    public Result<CommonPage<DictionaryVO>> pageQueryDictionary(@RequestBody @Valid DictionaryQueryDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        CommonPage<DictionaryDTO> page = dictionaryService.pageQueryDictionary(queryDTO);
        List<DictionaryVO> dictionaryVOList = dictionaryConvert.convertToVOList(page.getList());
        return Result.success(CommonPage.copyMetaWithNewData(page, dictionaryVOList));
    }

    @PostMapping("/delete")
    @ApiOperation("删除字典")
    public Result<Boolean> deleteDictionary(@RequestBody @Valid @NotNull(message = "ID不能为空") 
                                        @ApiParam(value = "字典ID", required = true) Long id) {
        boolean success = dictionaryService.deleteDictionary(id);
        return Result.success(success);
    }
} 