package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.CategoryDTO;
import com.deepinnet.skyflow.operationcenter.dto.CategoryQueryDTO;
import com.deepinnet.skyflow.operationcenter.enums.BizTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.product.CategoryService;
import com.deepinnet.skyflow.operationcenter.service.convert.CategoryConvert;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.deepinnet.skyflow.operationcenter.vo.CategoryVO;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 类目控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/category")
@Api(tags = "类目管理")
@Validated
public class CategoryController {

    @Resource
    private CategoryService categoryService;

    @Resource
    private CategoryConvert categoryConvert;

    @PostMapping("/create")
    @ApiOperation("创建类目")
    public Result<String> createCategory(@RequestBody @Valid CategoryDTO categoryDTO) {
        // 自动生成类目编号
        String categoryNo = IdGenerateUtil.getId(BizTypeEnum.CATEGORY.getType());
        categoryDTO.setCategoryNo(categoryNo);
        // 设置租户ID
        categoryDTO.setTenantId(TenantIdUtil.getTenantId());
        String id = categoryService.saveCategory(categoryDTO);
        return Result.success(id);
    }

    @PostMapping("/update")
    @ApiOperation("更新类目")
    public Result<Boolean> updateCategory(@RequestBody @Valid CategoryDTO categoryDTO) {
        // 设置租户ID
        categoryDTO.setTenantId(TenantIdUtil.getTenantId());
        boolean success = categoryService.updateCategory(categoryDTO);
        return Result.success(success);
    }

    @PostMapping("/get")
    @ApiOperation("根据ID获取类目")
    public Result<CategoryVO> getCategoryById(@RequestBody @Valid @NotNull(message = "ID不能为空") 
                                            @ApiParam(value = "类目ID", required = true) Integer id) {
        CategoryDTO categoryDTO = categoryService.getCategoryById(id);
        CategoryVO categoryVO = categoryConvert.convertToVO(categoryDTO);
        return Result.success(categoryVO);
    }

    @GetMapping("/getByNo")
    @ApiOperation("根据类目编号获取类目")
    public Result<CategoryVO> getCategoryByNo(@RequestParam @Valid @NotNull(message = "类目编号不能为空")
                                            @ApiParam(value = "类目编号", required = true) String categoryNo) {
        CategoryDTO categoryDTO = categoryService.getCategoryByNo(categoryNo);
        CategoryVO categoryVO = categoryConvert.convertToVO(categoryDTO);
        return Result.success(categoryVO);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询类目")
    public Result<CommonPage<CategoryVO>> pageQueryCategory(@RequestBody @Valid CategoryQueryDTO queryDTO) {
        // 设置租户ID
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        CommonPage<CategoryDTO> page = categoryService.pageQueryCategory(queryDTO);
        List<CategoryVO> categoryVOList = categoryConvert.convertToVOList(page.getList());
        return Result.success(CommonPage.copyMetaWithNewData(page, categoryVOList));
    }

    @PostMapping("/delete")
    @ApiOperation("删除类目")
    public Result<Boolean> deleteCategory(@RequestBody @Valid @NotNull(message = "ID不能为空") 
                                        @ApiParam(value = "类目ID", required = true) Integer id) {
        boolean success = categoryService.deleteCategory(id);
        return Result.success(success);
    }
} 