package com.deepinnet.skyflow.operationcenter.web;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.DataAccessDTO;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.skyflow.operationcenter.service.order.FlightOrderService;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.util.enums.UserTypeEnum;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.deepinnet.spatiotemporalplatform.vo.FlightPlanVO;
import com.deepinnet.tenant.TenantIdUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 飞行订单控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/flight/order")
@Api(tags = "订单管理")
@Validated
public class FlightOrderController {

    @Resource
    private FlightOrderService flightOrderService;

    @Resource
    private UserRemoteClient userRemoteClient;

    @PostMapping("/orderDetail")
    @ApiOperation("[订单管理]=>订单详情")
    public Result<FlightOrderVO> getFlightOrderByNo(@RequestBody @NotNull(message = "订单查询参数不能为空") OrderQueryDTO dto) {
        dto.setTenantId(TenantIdUtil.getTenantId());

        List<String> users = Lists.newArrayList();

        // 普通订单塞入用户编号
        if (ObjectUtil.equals(dto.getOrderType(), OrderTypeEnum.NORMAL.getCode())
                && (ObjectUtil.equals(UserTypeEnum.CUSTOMER, UserUtil.getUserType()) || ObjectUtil.equals(UserTypeEnum.SUPPLIER, UserUtil.getUserType()))) {
            users.add(UserUtil.getUserNo());
        }

        users.addAll(getAvailableUsers(OrderTypeEnum.getEnumByCode(dto.getOrderType())));
        dto.setUserNo(users);

        return Result.success(flightOrderService.getFlightOrderByNo(dto));
    }

    @PostMapping("/orderList")
    @ApiOperation("[订单管理]=>获取订单列表")
    public Result<CommonPage<FlightOrderVO>> getFlightOrderList(@RequestBody @NotNull(message = "订单查询参数不能为空") OrderPageQueryDTO dto) {
        dto.setTenantId(TenantIdUtil.getTenantId());

        return Result.success(flightOrderService.getFlightOrderListByPage(dto));
    }

    @PostMapping("/approveOrder")
    @ApiOperation("[订单管理]=>审批订单")
    public Result<Boolean> approveOrder(@RequestBody @NotNull(message = "审批参数不能为空") OrderApproveDTO dto) {
        dto.setTenantId(TenantIdUtil.getTenantId());
        return Result.success(flightOrderService.orderApprove(dto));
    }

    @PostMapping("/create")
    @ApiOperation("[订单管理]=>创建订单")
    public Result<String> createOrder(@RequestBody @NotNull(message = "订单参数不能为空") FlightOrderCreateDTO dto) {
        dto.setTenantId(TenantIdUtil.getTenantId());
        dto.setUserNo(UserUtil.getUserNo());
        return Result.success(flightOrderService.createOrder(dto));
    }

    @PostMapping("/planList")
    @ApiOperation("[订单管理]=>获取计划列表")
    public Result<CommonPage<FlightPlanVO>> getPlanList(@RequestBody @NotNull(message = "计划查询参数不能为空") FlightPlanPageQueryDTO dto) {

        if (ObjectUtil.equals(UserTypeEnum.CUSTOMER, UserUtil.getUserType())) {
            List<String> users = getAvailableUsers();
            dto.setUserNo(users);
        }

        if (ObjectUtil.equals(UserTypeEnum.SUPPLIER, UserUtil.getUserType())) {
            dto.setFlightUnitId(UserUtil.getUserNo());
        }

        return Result.success(flightOrderService.getFlightPlanListByPage(dto));
    }

    @GetMapping("/planDetail")
    @ApiOperation("[订单管理]=>获取计划详情")
    public Result<FlightPlanVO> getPlanDetail(@RequestParam(value = "planId") String planId) {

        return Result.success(flightOrderService.getFlightPlanDetail(planId));
    }
    
    @PostMapping("/orderUsage")
    @ApiOperation("[订单管理]=>订单用量扣减")
    public Result<Boolean> orderProductUsage(@RequestBody FlightOrderProductUsageDTO dto) {
        return Result.success(flightOrderService.orderProductUsage(dto));
    }

    @PostMapping("/delete")
    @ApiOperation("[订单管理]=>删除订单")
    public Result<Boolean> deleteOrder(@RequestBody @NotNull(message = "订单查询参数不能为空") OrderQueryDTO dto) {
        dto.setTenantId(TenantIdUtil.getTenantId());

        return Result.success(flightOrderService.deleteOrder(dto));
    }

    private List<String> getAvailableUsers(OrderTypeEnum orderTypeEnum) {
        DataAccessDTO availableQueryData = userRemoteClient.getAvailableQueryData();

        if (ObjectUtil.isNull(availableQueryData)) {
            LogUtil.warn("当前暂无可用用户");
            return Lists.newArrayList();
        }

        List<String> userNos = Lists.newArrayList();

        // 需求计划订单塞入 用户编号 ｜ 部门编号
        if (ObjectUtil.equals(orderTypeEnum, OrderTypeEnum.DEMAND_PLAN)
                && CollUtil.isNotEmpty(availableQueryData.getSupportQueryUserNos())
                && !availableQueryData.getSupportQueryUserNos().contains(DataScopeConstants.SUPER_ADMIN)
                && ObjectUtil.notEqual(UserTypeEnum.OPERATION, UserUtil.getUserType())) {
            userNos.addAll(availableQueryData.getSupportQueryUserNos());
        }

        return userNos;
    }

    private List<String> getAvailableUsers() {
        DataAccessDTO availableQueryData = userRemoteClient.getAvailableQueryData();

        if (ObjectUtil.isNull(availableQueryData)) {
            LogUtil.warn("当前暂无可用用户");
            return Lists.newArrayList();
        }

        List<String> userNos = Lists.newArrayList();

        // 需求计划订单塞入 用户编号 ｜ 部门编号
        if (CollUtil.isNotEmpty(availableQueryData.getSupportQueryUserNos())
                && !availableQueryData.getSupportQueryUserNos().contains(DataScopeConstants.SUPER_ADMIN)) {
            userNos.addAll(availableQueryData.getSupportQueryUserNos());
        }

        return userNos;
    }
}