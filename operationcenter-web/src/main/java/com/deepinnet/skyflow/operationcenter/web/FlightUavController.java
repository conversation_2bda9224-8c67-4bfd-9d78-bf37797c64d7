package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBatchOperationDTO;
import com.deepinnet.skyflow.operationcenter.service.client.FlightPlanQueryClient;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavService;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavGroupVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavVO;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavConvert;
import com.deepinnet.spatiotemporalplatform.client.skyflow.FlightClient;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import com.deepinnet.skyflow.operationcenter.dto.FlightUavGroupQueryDTO;

/**
 * 飞行无人机控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/flight/uav")
@Api(tags = "飞行无人机接口")
public class FlightUavController {

    @Resource
    private FlightUavService flightUavService;
    
    @Resource
    private FlightUavConvert flightUavConvert;

    @Resource
    private FlightPlanQueryClient flightPlanQueryClient;

    @PostMapping("/create")
    @ApiOperation("创建飞行无人机")
    public Result<String> createFlightUav(@RequestBody @Valid FlightUavDTO flightUavDTO) {
        // 设置租户ID
        flightUavDTO.setTenantId(TenantIdUtil.getTenantId());
        String flightUavNo = flightUavService.saveFlightUav(flightUavDTO);
        return Result.success(flightUavNo);
    }

    @PutMapping("/update")
    @ApiOperation("更新飞行无人机")
    public Result<Boolean> updateFlightUav(@RequestBody @Valid FlightUavDTO flightUavDTO) {
        // 设置租户ID
        flightUavDTO.setTenantId(TenantIdUtil.getTenantId());
        boolean success = flightUavService.updateFlightUav(flightUavDTO);
        return Result.success(success);
    }

    @GetMapping("/get/{id}")
    @ApiOperation("根据ID获取飞行无人机")
    public Result<FlightUavVO> getFlightUavById(@PathVariable("id") 
                                            @ApiParam(value = "飞行无人机ID", required = true) Integer id) {
        FlightUavDTO flightUavDTO = flightUavService.getFlightUavById(id);
        if (flightUavDTO == null) {
            return Result.success(null);
        }
        FlightUavVO flightUavVO = flightUavConvert.convertToVO(flightUavDTO);
        return Result.success(flightUavVO);
    }

    @GetMapping("/getByNo")
    @ApiOperation("根据编号获取飞行无人机")
    public Result<FlightUavVO> getFlightUavByNo(@RequestParam("flightUavNo") 
                                             @ApiParam(value = "飞行无人机编号", required = true) String flightUavNo) {
        FlightUavDTO flightUavDTO = flightUavService.getFlightUavByNo(flightUavNo);
        if (flightUavDTO == null) {
            return Result.success(null);
        }
        FlightUavVO flightUavVO = flightUavConvert.convertToVO(flightUavDTO);
        return Result.success(flightUavVO);
    }

    @GetMapping("/listBySupplierUserNo")
    @ApiOperation("根据服务商编号获取飞行无人机列表")
    public Result<List<FlightUavVO>> listFlightUavBySupplierUserNo(
            @RequestParam("supplierUserNo") 
            @ApiParam(value = "服务商编号", required = true) String supplierUserNo) {
        List<FlightUavDTO> flightUavDTOList = flightUavService.getFlightUavListBySupplierUserNo(supplierUserNo);
        List<FlightUavVO> flightUavVOList = flightUavConvert.convertToVOList(flightUavDTOList);
        return Result.success(flightUavVOList);
    }
    
    @GetMapping("/groupByModel")
    @ApiOperation("根据服务商编号获取按机型分组的飞行无人机列表 - 支持机型品牌名和出厂型号搜索")
    public Result<List<FlightUavGroupVO>> getUavGroupByModel(
            @RequestParam("supplierUserNo") 
            @ApiParam(value = "服务商编号", required = true) String supplierUserNo,
            @RequestParam(value = "flightUavBmName", required = false) 
            @ApiParam(value = "无人机品牌名称") String flightUavBmName,
            @RequestParam(value = "flightUavBmModelNo", required = false) 
            @ApiParam(value = "出厂型号") String flightUavBmModelNo) {
        
        FlightUavGroupQueryDTO queryDTO = new FlightUavGroupQueryDTO();
        queryDTO.setSupplierUserNo(supplierUserNo);
        queryDTO.setFlightUavBmName(flightUavBmName);
        queryDTO.setFlightUavBmModelNo(flightUavBmModelNo);
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        
        List<FlightUavGroupVO> result = flightUavService.getUavGroupByModel(queryDTO);
        return Result.success(result);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询飞行无人机")
    public Result<Object> pageQueryFlightUav(@RequestBody @Valid FlightUavQueryDTO queryDTO) {
        // 设置租户ID
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        return Result.success(flightUavService.pageQueryFlightUav(queryDTO));
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除飞行无人机")
    public Result<Boolean> deleteFlightUav(@PathVariable("id") 
                                       @ApiParam(value = "飞行无人机ID", required = true) Integer id) {
        boolean success = flightUavService.deleteFlightUav(id);
        return Result.success(success);
    }
    
    @PostMapping("/batch-operation")
    @ApiOperation("批量操作飞行无人机（删除、新增和更新）")
    public Result<Boolean> batchOperateFlightUavs(@RequestBody @Valid FlightUavBatchOperationDTO batchOperationDTO) {
        boolean success = flightUavService.batchOperateFlightUavs(batchOperationDTO);
        return Result.success(success);
    }

    @GetMapping("/liveUrl")
    @ApiOperation("查询无人机直播地址")
    public Result<String> liveUrl(@RequestParam("sn") String sn, @RequestParam(value = "httpProtocol",
            defaultValue = "http") String httpProtocol) {
        return Result.success(flightPlanQueryClient.getLiveUrl(sn, httpProtocol));
    }
} 