package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.PriceDTO;
import com.deepinnet.skyflow.operationcenter.dto.PriceQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.IdDTO;
import com.deepinnet.skyflow.operationcenter.enums.BizTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.product.PriceService;
import com.deepinnet.skyflow.operationcenter.service.convert.PriceConvert;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.deepinnet.skyflow.operationcenter.vo.PriceVO;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 价格控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/price")
@Api(tags = "价格管理")
@Validated
public class PriceController {

    @Resource
    private PriceService priceService;

    @Resource
    private PriceConvert priceConvert;

    @PostMapping("/create")
    @ApiOperation("创建价格")
    public Result<Integer> createPrice(@RequestBody @Valid PriceDTO priceDTO) {
        // 设置租户ID
        priceDTO.setTenantId(TenantIdUtil.getTenantId());
        Integer id = priceService.savePrice(priceDTO);
        return Result.success(id);
    }

    @PostMapping("/update")
    @ApiOperation("更新价格")
    public Result<Boolean> updatePrice(@RequestBody @Valid PriceDTO priceDTO) {
        // 设置租户ID
        priceDTO.setTenantId(TenantIdUtil.getTenantId());
        boolean success = priceService.updatePrice(priceDTO);
        return Result.success(success);
    }

    @PostMapping("/get")
    @ApiOperation("根据ID获取价格")
    public Result<PriceVO> getPriceById(@RequestBody @Valid @NotNull(message = "ID不能为空") 
                                      @ApiParam(value = "价格ID DTO", required = true) IdDTO idDTO) {
        PriceDTO priceDTO = priceService.getPriceById(idDTO.getId());
        PriceVO priceVO = priceConvert.convertToVO(priceDTO);
        return Result.success(priceVO);
    }

    @GetMapping("/getByProductNo")
    @ApiOperation("根据产品编号获取价格列表")
    public Result<List<PriceVO>> getPricesByProductNo(@RequestParam @Valid @NotBlank(message = "产品编号不能为空")
                                                    @ApiParam(value = "产品编号", required = true) String productNo) {
        List<PriceDTO> priceDTOList = priceService.getPricesByProductNo(productNo);
        List<PriceVO> priceVOList = priceConvert.convertToVOList(priceDTOList);
        return Result.success(priceVOList);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询价格")
    public Result<CommonPage<PriceVO>> pageQueryPrice(@RequestBody @Valid PriceQueryDTO queryDTO) {
        // 设置租户ID
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        CommonPage<PriceDTO> page = priceService.pageQueryPrice(queryDTO);
        List<PriceVO> priceVOList = priceConvert.convertToVOList(page.getList());
        return Result.success(CommonPage.copyMetaWithNewData(page, priceVOList));
    }

    @PostMapping("/delete")
    @ApiOperation("删除价格")
    public Result<Boolean> deletePrice(@RequestBody @Valid @NotNull(message = "ID不能为空") 
                                     @ApiParam(value = "价格ID DTO", required = true) IdDTO idDTO) {
        boolean success = priceService.deletePrice(idDTO.getId());
        return Result.success(success);
    }
} 