package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.FileAttachmentClient;
import com.deepinnet.infra.api.dto.FileUploadResultDTO;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR> wong
 * @create 2025/4/19 15:20
 * @Description
 */

@RestController
@RequestMapping("/file")
@Api(tags = "附件接口")
@Validated
public class FileAttachmentController {

    @Resource
    private FileAttachmentClient fileAttachmentClient;

    private static final Long MAX_IMAGE_SIZE = 10 * 1024 * 1024L;

    /**
     * 上传单个文件
     *
     * @param file    文件
     * @param bizType 业务类型
     * @param bizNo   业务编号
     * @return 上传结果
     */
    @PostMapping("/upload")
    public Result<FileUploadResultDTO> uploadFile(
            @ApiParam(value = "文件", required = true) @RequestPart("file") MultipartFile file,
            @ApiParam(value = "业务类型") @RequestParam(value = "bizType", required = false) String bizType,
            @ApiParam(value = "业务编号") @RequestParam(value = "bizNo", required = false) String bizNo) {

        if (file == null || file.isEmpty()) {
            throw new BizException(BizErrorCode.FILE_IS_EMPTY.getCode(), BizErrorCode.FILE_IS_EMPTY.getDesc());
        }

        // 校验文件大小是否超过限制，图片文件限制在10MB
        checkImgFileSize(file);

        return fileAttachmentClient.uploadFile(file, bizType, bizNo);
    }

    /**
     * 批量上传文件
     *
     * @param files   文件列表
     * @param bizType 业务类型
     * @param bizNo   业务编号
     * @return 上传结果列表
     */
    @PostMapping("/batchUpload")
    public Result<List<FileUploadResultDTO>> batchUploadFiles(
            @ApiParam(value = "文件列表", required = true) @RequestPart("files") MultipartFile[] files,
            @ApiParam(value = "业务类型") @RequestParam(value = "bizType", required = false) String bizType,
            @ApiParam(value = "业务编号") @RequestParam(value = "bizNo", required = false) String bizNo) {

        // 校验文件大小是否超过限制，图片文件限制在10MB
        checkImgFileSizes(files);

        return fileAttachmentClient.batchUploadFiles(files, bizType, bizNo);
    }

    /**
     * 根据文件路径获取文件资源
     *
     * @param filePath 文件路径
     * @return 文件资源响应实体
     */
    @GetMapping("/download")
    public ResponseEntity<org.springframework.core.io.Resource> getFileResource(
            @ApiParam(value = "文件路径", required = true) @RequestParam("filePath") String filePath) {
        return fileAttachmentClient.getFileResource(filePath);
    }

    private void checkImgFileSizes(MultipartFile[] files) {
        for (MultipartFile file : files) {
            checkImgFileSize(file);
        }
    }

    private void checkImgFileSize(MultipartFile file) {
        String contentType = file.getContentType();
        long fileSize = file.getSize();
        if (contentType != null && contentType.startsWith("image/")) {
            if (fileSize > MAX_IMAGE_SIZE) {
                throw new BizException(BizErrorCode.FILE_SIZE_EXCEEDS_THE_LIMIT.getCode(), "图片大小不能超过10MB");
            }
        }
    }
}
