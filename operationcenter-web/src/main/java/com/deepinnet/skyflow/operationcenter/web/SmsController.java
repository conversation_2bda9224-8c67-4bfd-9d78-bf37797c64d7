package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.SmsClient;
import com.deepinnet.infra.api.dto.*;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> wong
 * @create 2025/4/19 15:20
 * @Description
 */

@RestController
@RequestMapping("/sms")
@Api(tags = "短信接口")
@Validated
public class SmsController {

    @Resource
    private SmsClient smsClient;

    /**
     * 发送验证码
     *
     * @param requestDTO 请求参数
     * @return 发送结果
     */
    @PostMapping("/code/send")
    @ApiOperation("发送短信")
    public Result<SendSmsResultDTO> sendVerificationCode(@Valid @RequestBody VerifyCodeRequestDTO requestDTO) {
        return smsClient.sendVerificationCode(requestDTO);
    }

    /**
     * 验证验证码
     *
     * @param requestDTO 请求参数
     * @return 验证结果
     */
    @PostMapping("/code/verify")
    @ApiOperation("验证验证码")
    public Result<Boolean> verifyCode(@RequestBody VerifyCodeRequestDTO requestDTO) {
        return smsClient.verifyCode(requestDTO);
    }
}
