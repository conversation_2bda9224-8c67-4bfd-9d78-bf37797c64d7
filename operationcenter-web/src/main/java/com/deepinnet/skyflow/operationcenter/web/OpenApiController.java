package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.openapi.*;
import com.deepinnet.skyflow.operationcenter.service.http.HttpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 开放API控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/openapi")
@Api(tags = "开放平台API接口")
public class OpenApiController {

    @Resource
    private HttpService httpService;

    @ApiOperation("逆地理编码")
    @PostMapping("/reverse/geocoding")
    public Result<ReverseGeocoding> getReverseGeocoding(@RequestBody ReverseGeocodingParam param) {
        ReverseGeoCodingRequest request = new ReverseGeoCodingRequest();
        request.setUrlParams(param);
        return Result.success((ReverseGeocoding) httpService.fetchData(request));
    }

    @ApiOperation("地理编码")
    @PostMapping("/geocode/geocoding")
    public Result<Geocoding> getGeocoding(@RequestBody GeocodingParam param) {
        GeoCodingRequest request = new GeoCodingRequest();
        request.setUrlParams(param);
        return Result.success((Geocoding) httpService.fetchData(request));
    }
} 