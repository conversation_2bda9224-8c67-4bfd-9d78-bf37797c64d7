package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.ProductSupplierClient;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.vo.SupplierInfoVO;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> wong
 * @create 2025/4/19 15:20
 * @Description
 */

@RestController
@RequestMapping("/user/supplier")
@Api(tags = "产品服务商接口")
@Validated
public class ProductSupplierController {

    @Resource
    private ProductSupplierClient productSupplierClient;

    @PostMapping("/save")
    @ApiOperation("保存服务商信息")
    public Result<String> saveSupplierInfo(@Valid @RequestBody SupplierInfoDTO supplierInfoDTO) {
        return productSupplierClient.saveSupplierInfo(supplierInfoDTO);
    }

    @PostMapping("/detail")
    @ApiOperation("查询服务商信息详情")
    public Result<SupplierDetailDTO> getSupplierDetail(@RequestBody SupplierQueryDetailDTO queryDetailDTO) {
        return productSupplierClient.getSupplierDetail(queryDetailDTO);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询服务商")
    public Result<CommonPage<SupplierInfoVO>> pageQuerySuppliers(@Valid @RequestBody SupplierQueryDTO queryDTO) {
        return productSupplierClient.pageQuerySuppliers(queryDTO);
    }
}
